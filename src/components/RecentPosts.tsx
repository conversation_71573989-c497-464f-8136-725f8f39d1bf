'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { collection, query, where, getDocs, limit, orderBy, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import Ram<PERSON>riPlaceholder from './RamiyariPlaceholder';

interface Post {
  id: string;
  title: string;
  content: string;
  thumbnailImage: string;
  category?: string;
  date: Timestamp;
  viewCount: number;
  author: string;
  status: string;
}

export default function RecentPosts() {
  const [recentPosts, setRecentPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchRecentPosts() {
      try {
        setIsLoading(true);
        const postsCollection = collection(db, 'posts');

        // Use a simpler query that doesn't require a composite index
        const postsQuery = query(
          postsCollection,
          orderBy('date', 'desc'),
          limit(20) // Get more to filter
        );

        const postsSnapshot = await getDocs(postsQuery);

        if (postsSnapshot.empty) {
          setRecentPosts([]);
          return;
        }

        // Filter for published posts and limit to 6
        const recentPosts = postsSnapshot.docs
          .map(doc => ({
            id: doc.id,
            ...doc.data()
          } as Post))
          .filter(post => post.status === 'بڵاوکراوەتەوە')
          .slice(0, 6);

        setRecentPosts(recentPosts);
      } catch (err) {
        console.error('Error fetching recent posts:', err);
      } finally {
        setIsLoading(false);
      }
    }

    fetchRecentPosts();
  }, []);

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-center items-center h-40">
          <div className="inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  if (recentPosts.length === 0) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-center items-center h-40">
          <p className="text-xl text-gray-500">هیچ بابەتێک نەدۆزرایەوە</p>
        </div>
      </div>
    );
  }

  return (
    <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="flex justify-between items-center mb-8">
        <h2 className="text-2xl font-bold text-black">دوایین بابەتەکان</h2>
        <Link href="/posts" className="text-[var(--primary-600)] hover:text-[var(--primary-800)] transition-colors">
          هەموو بابەتەکان
        </Link>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {recentPosts.map((post) => (
          <div key={post.id} className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
            <Link href={`/post/${post.id}`}>
              <div className="relative h-48">
                {post.thumbnailImage ? (
                  <Image
                    src={post.thumbnailImage}
                    alt={post.title}
                    fill
                    sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    className="object-cover"
                    loading="lazy"
                    quality={75}
                    unoptimized={true} // Skip Next.js optimization for all images
                  />
                ) : (
                  <RamiyariPlaceholder />
                )}
              </div>
              <div className="p-4">
                {post.category && (
                  <span className="inline-block bg-[var(--primary-100)] text-[var(--primary-700)] px-2 py-1 text-xs font-semibold rounded-md mb-2">
                    {post.category}
                  </span>
                )}
                <h3 className="text-lg font-bold text-black mb-2 line-clamp-2">{post.title}</h3>
                <div className="flex items-center text-gray-500 text-sm mb-2">
                  <span className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {post.date ? new Date(post.date.toDate()).toLocaleDateString('ku-IQ') : ''}
                  </span>
                  <span className="mx-2">•</span>
                  <span className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    {post.viewCount || 0} بینین
                  </span>
                </div>
                <div className="text-sm text-gray-600 line-clamp-2">
                  {post.content ? (
                    <div dangerouslySetInnerHTML={{ __html: post.content.replace(/<[^>]*>/g, '').substring(0, 150) + '...' }} />
                  ) : (
                    <p>بێ ناوەڕۆک</p>
                  )}
                </div>
              </div>
            </Link>
          </div>
        ))}
      </div>
    </section>
  );
}
