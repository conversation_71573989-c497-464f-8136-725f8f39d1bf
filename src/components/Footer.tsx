'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

interface Category {
  id: string;
  name: string;
  slug: string;
}

export default function Footer() {
  const [categories, setCategories] = useState<Category[]>([]);

  useEffect(() => {
    async function fetchCategories() {
      try {
        // Check if we already have categories in localStorage from Navbar
        const cachedCategories = localStorage.getItem('navbarCategories');

        if (cachedCategories) {
          setCategories(JSON.parse(cachedCategories));
          return;
        }

        // Fetch if not cached
        const categoriesCollection = collection(db, 'categories');
        const categoriesSnapshot = await getDocs(categoriesCollection);
        const categoriesList = categoriesSnapshot.docs.map(doc => ({
          id: doc.id,
          name: doc.data().name,
          slug: doc.data().slug
        })) as Category[];

        setCategories(categoriesList);
      } catch (err) {
        console.error('Error fetching categories:', err);
      }
    }

    fetchCategories();
  }, []);

  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-[#3a2a1f] text-[#ffffff] font-semibold" style={{ color: '#ffffff !important' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="flex flex-col items-center md:items-start">
            <Link href="/" className="flex items-center mb-4">
              <div className="bg-[#f9f5f0] p-3 rounded-lg shadow-sm">
                <Image
                  src="/logomain.svg"
                  alt="Ramiyari Logo"
                  width={140}
                  height={50}
                  className="h-12 w-auto"
                  priority
                />
              </div>
            </Link>
            <p className="!text-[#ffffff] mb-4 text-center md:text-right text-lg" style={{ color: '#ffffff !important' }}>
              ڕامیاری، سەرچاوەی هەواڵ و زانیاری ڕامیاری و کۆمەڵایەتی
            </p>
          </div>

          <div className="flex flex-col items-center md:items-end">
            <h3 className="text-xl font-bold mb-4 !text-[#ffffff]" style={{ color: '#ffffff !important' }}>پەیوەندی</h3>
            <div className="flex items-center mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2 text-[#f9f5f0]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <span className="!text-[#ffffff] hover:!text-[#f9f5f0] transition-colors" style={{ color: '#ffffff !important' }}>
                <a href="mailto:<EMAIL>" className="!text-[#ffffff] hover:!text-[#f9f5f0] text-lg font-bold" style={{ color: '#ffffff !important' }}><EMAIL></a>
              </span>
            </div>
          </div>
        </div>

        <div className="border-t border-white/20 mt-8 pt-6 text-center">
          <div className="flex flex-wrap justify-center gap-4 mb-4">
            {categories.slice(0, 5).map((category) => (
              <Link
                key={category.id}
                href={`/category/${category.slug}`}
                className="!text-[#ffffff] hover:!text-[#f9f5f0] transition-colors font-bold text-base"
                style={{ color: '#ffffff !important' }}
              >
                {category.name}
              </Link>
            ))}
          </div>
          <p className="!text-[#ffffff] font-bold text-base" style={{ color: '#ffffff !important' }}>© {currentYear} ڕامیاری. هەموو مافەکان پارێزراون.</p>
        </div>
      </div>
    </footer>
  );
}
