.categoriesContainer {
  overflow-x: auto;
  padding-bottom: 1rem;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.categoriesContainer::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.categoriesRow {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-width: min-content;
}

.featuredCategory {
  width: 100%;
  min-height: 150px;
  background-color: #f9f5f0;
  border-radius: 0.75rem;
  padding: 2rem;
  border: 2px solid #8f5826;
  box-shadow: 0 4px 12px rgba(143, 88, 38, 0.2);
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.featuredCategory:hover {
  box-shadow: 0 8px 20px rgba(143, 88, 38, 0.3);
  transform: translateY(-4px);
}

.featuredCategoryName {
  font-size: 1.75rem;
  font-weight: 700;
  color: #333;
}

.featuredCategoryCount {
  background-color: var(--primary-100);
  color: var(--primary-700);
  padding: 0.5rem 1rem;
  font-size: 1.125rem;
  font-weight: 700;
  border-radius: 9999px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.regularCategories {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
}

@media (min-width: 768px) {
  .regularCategories {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
  }
}

.categoryCard {
  background-color: #f9f5f0;
  border-radius: 0.5rem;
  padding: 1rem;
  border: 2px solid #8f5826;
  box-shadow: 0 4px 8px rgba(143, 88, 38, 0.2);
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 220px;
}

.categoryCard:hover {
  box-shadow: 0 8px 16px rgba(143, 88, 38, 0.3);
  transform: translateY(-2px);
}

@media (min-width: 768px) {
  .categoryCard {
    min-width: 0;
  }
}
