exports.id=791,exports.ids=[791],exports.modules={2844:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(60687),i=s(24157);function a({children:e}){return(0,r.jsx)(i.O,{children:e})}},10396:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},24157:(e,t,s)=>{"use strict";s.d(t,{A:()=>n,O:()=>l});var r=s(60687),i=s(43210);s(98537);let a=(0,i.createContext)({user:null,loading:!0}),n=()=>(0,i.useContext)(a);function l({children:e}){let[t,s]=(0,i.useState)(null),[n,l]=(0,i.useState)(!0);return(0,r.jsx)(a.Provider,{value:{user:t,loading:n},children:e})}},31596:(e,t,s)=>{"use strict";s.d(t,{A:()=>g});var r=s(60687),i=s(16189),a=s(24157),n=s(98537),l=s(43210);let o=(0,l.createContext)(void 0),c=()=>!0;function d({children:e}){let[t,s]=(0,l.useState)({isOpen:c(),isInitialized:!1});return(0,r.jsx)(o.Provider,{value:{isOpen:t.isOpen,isInitialized:t.isInitialized,toggle:()=>{s(e=>{let t=!e.isOpen;try{localStorage.setItem("dashboard_sidebar_state",JSON.stringify(t))}catch(e){console.error("Error saving sidebar state to localStorage:",e)}return{...e,isOpen:t}})}},children:e})}function h(){let e=(0,l.useContext)(o);if(void 0===e)throw Error("useSidebar must be used within a SidebarProvider");return e}var m=s(85814),x=s.n(m);function u(){let{user:e}=(0,a.A)(),t=(0,i.useRouter)(),s=(0,i.usePathname)(),{isOpen:l,isInitialized:o,toggle:c}=h(),d=async()=>{await (0,n.CI)(),t.push("/login")};return o?(0,r.jsxs)("header",{className:`bg-[var(--header-bg)] shadow-md fixed top-0 left-0 ${l?"right-64":"right-0"} z-20 transition-all duration-300`,children:[(0,r.jsxs)("div",{className:"flex justify-between items-center px-6 py-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("button",{type:"button",onClick:c,className:"p-2 rounded-md text-[var(--primary-600)] hover:bg-[var(--primary-50)] transition-colors ml-3","aria-label":"Toggle sidebar",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(x(),{href:"/dashboard/posts",className:"flex items-center",children:[(0,r.jsx)("img",{src:"/logomain.svg",alt:"Ramiyari Logo",className:"h-8 w-auto ml-2"}),(0,r.jsx)("span",{className:"text-xl font-bold text-black hover:text-[var(--primary-600)] transition-colors",children:"بەڕێوەبردنی ناوەڕۆک"})]})})]}),(0,r.jsx)("div",{className:"flex items-center",children:e&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsxs)("div",{className:"flex items-center bg-[var(--gray-100)] px-3 py-1.5 rounded-full",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-[var(--primary-100)] flex items-center justify-center text-[var(--primary-700)] font-bold",children:e.email?.charAt(0).toUpperCase()||"U"}),(0,r.jsx)("div",{className:"mr-2 text-sm font-medium text-black",children:(0,r.jsx)("span",{children:e.email})})]}),(0,r.jsx)("div",{className:"flex items-center mr-4",children:(0,r.jsxs)("button",{type:"button",onClick:d,className:"px-4 py-1.5 bg-[var(--danger-500)] text-white rounded-md hover:bg-[var(--danger-700)] transition-colors text-sm font-medium flex items-center cursor-pointer",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),"دەرچوون"]})})]})})]}),(0,r.jsx)("div",{className:"px-6 py-2 bg-[var(--gray-50)] border-t border-[var(--gray-200)] text-sm",children:(0,r.jsx)("div",{className:"flex items-center text-black",children:(0,r.jsx)("span",{className:"text-black font-medium",children:s.includes("/posts")?"بابەتەکان":s.includes("/categories")?"پۆلەکان":s.includes("/tags")?"تاگەکان":""})})})]}):(0,r.jsx)("header",{className:"bg-[var(--header-bg)] shadow-md fixed top-0 left-0 right-0 z-20",children:(0,r.jsx)("div",{className:"flex justify-between items-center px-6 py-3"})})}let p=[{title:"بلۆگ",path:"/dashboard/posts",icon:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"})})},{title:"پۆلەکان",path:"/dashboard/categories",icon:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})})},{title:"تاگەکان",path:"/dashboard/tags",icon:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})})},{title:"میدیا",path:"/dashboard/media",icon:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})}];function v(){let e=(0,i.usePathname)(),{isOpen:t,isInitialized:s}=h();return s?(0,r.jsx)("div",{className:`h-screen w-64 bg-[var(--sidebar-bg)] text-white fixed right-0 top-0 pt-16 overflow-y-auto shadow-lg z-10 transition-all duration-300 transform ${t?"translate-x-0":"translate-x-full"}`,children:(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"mb-6 text-center",children:[(0,r.jsx)("div",{className:"w-auto h-16 mx-auto mb-2 bg-white rounded-lg flex items-center justify-center shadow-md px-4",children:(0,r.jsx)("img",{src:"/logomain.svg",alt:"Ramiyari Logo",className:"h-10 w-auto"})}),(0,r.jsx)("div",{className:"text-sm text-white font-medium",children:"بەخێربێیت"})]}),(0,r.jsx)("ul",{className:"space-y-1",children:p.map(t=>(0,r.jsx)("li",{className:"mb-3",children:(0,r.jsxs)(x(),{href:t.path,className:`flex items-center p-2.5 rounded-md cursor-pointer transition-colors ${e.includes(t.path)?"bg-[var(--sidebar-active)] text-white":"text-white hover:bg-[var(--sidebar-hover)]"}`,children:[(0,r.jsx)("span",{className:"ml-3 text-white",children:t.icon}),(0,r.jsx)("span",{className:"font-medium text-white",children:t.title})]})},t.path))}),(0,r.jsxs)("div",{className:"mt-8 pt-6 border-t border-[var(--primary-700)]",children:[(0,r.jsx)("div",{className:"text-xs text-white font-medium mb-2",children:"سیستەمی بەڕێوەبردنی ناوەڕۆک"}),(0,r.jsxs)("div",{className:"text-xs text-white",children:["\xa9 ",new Date().getFullYear()]})]})]})}):null}function f({children:e}){let{isOpen:t,isInitialized:s}=h();return(0,r.jsxs)("div",{className:"min-h-screen bg-[var(--background)]",children:[(0,r.jsx)(u,{}),(0,r.jsx)(v,{}),(0,r.jsxs)("div",{className:`pt-28 ${s&&t?"pr-64":"pr-0"} transition-all duration-300`,children:[(0,r.jsx)("main",{className:"p-6",children:e}),(0,r.jsx)("footer",{className:"mt-8 p-6 text-center text-black text-sm",children:(0,r.jsxs)("p",{children:["سیستەمی بەڕێوەبردنی ناوەڕۆک \xa9 ",new Date().getFullYear()]})})]})]})}function g({children:e}){return(0,r.jsx)(d,{children:(0,r.jsx)(f,{children:e})})}},58015:(e,t,s)=>{Promise.resolve().then(s.bind(s,98122))},61135:()=>{},68604:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71557:(e,t,s)=>{"use strict";let r;s.d(t,{IG:()=>d,db:()=>c,j2:()=>o});var i=s(67989),a=s(75535),n=s(65553),l=s(70146);r=(0,i.Dk)().length?(0,i.Dk)()[0]:(0,i.Wp)({apiKey:"AIzaSyDrLmTWQcbGNPUlYcTEWIGNcZbCYe7J8og",authDomain:"blogcling.firebaseapp.com",projectId:"blogcling",storageBucket:"blogcling.firebasestorage.app",messagingSenderId:"771192391646",appId:"1:771192391646:web:37954fb7162ac16d8fff4f",measurementId:"G-NYZ36H3N04"});let o=(0,n.xI)(r);o.useDeviceLanguage();let c=(0,a.aU)(r),d=(0,l.c7)(r)},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>c});var r=s(37413),i=s(2202),a=s.n(i),n=s(64988),l=s.n(n);s(61135);var o=s(98122);let c={title:"ڕامیاری",description:"ڕامیاری، سەرچاوەی هەواڵ و زانیاری ڕامیاری و کۆمەڵایەتی"};function d({children:e}){return(0,r.jsxs)("html",{lang:"ckb",dir:"rtl",children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("link",{rel:"icon",href:"/favicon.svg",type:"image/svg+xml"}),(0,r.jsx)("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/logomain.svg"}),(0,r.jsx)("link",{rel:"manifest",href:"/manifest.webmanifest"}),(0,r.jsx)("meta",{name:"theme-color",content:"#8f5826"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,r.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100..900&display=swap",rel:"stylesheet"}),(0,r.jsx)("link",{rel:"stylesheet",href:"https://cdnjs.cloudflare.com/ajax/libs/jodit/3.24.5/jodit.min.css"})]}),(0,r.jsx)("body",{className:`${a().variable} ${l().variable} antialiased`,children:(0,r.jsx)(o.default,{children:e})})]})}},94967:(e,t,s)=>{Promise.resolve().then(s.bind(s,2844))},98122:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/components/AuthProviderWrapper.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/components/AuthProviderWrapper.tsx","default")},98537:(e,t,s)=>{"use strict";s.d(t,{Bh:()=>l,CI:()=>a,HW:()=>n});var r=s(65553),i=s(71557);async function a(){try{return await (0,r.CI)(i.j2),{success:!0,error:null}}catch(e){return{success:!1,error:e.message}}}function n(){return i.j2.currentUser}function l(e){return(0,r.hg)(i.j2,e)}}};