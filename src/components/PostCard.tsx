import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Timestamp } from 'firebase/firestore';
import RamiyariPlaceholder from './RamiyariPlaceholder';

interface PostCardProps {
  id: string;
  title: string;
  thumbnailImage?: string;
  date?: Timestamp;
  category?: string;
  viewCount?: number;
}

export default function PostCard({ id, title, thumbnailImage, date, category, viewCount }: PostCardProps) {
  const [imgError, setImgError] = useState(false);

  // Check if the image URL is valid
  const isValidImageUrl = thumbnailImage &&
    (thumbnailImage.startsWith('http://') || thumbnailImage.startsWith('https://'));

  // Check if it's a Firebase Storage URL
  const isFirebaseStorageUrl = isValidImageUrl &&
    thumbnailImage && thumbnailImage.includes('firebasestorage.googleapis.com');

  // Check if we have a valid image to display
  const hasValidImage = isValidImageUrl && !imgError;

  return (
    <div className="bg-[#f9f5f0] rounded-lg overflow-hidden border-2 border-[#8f5826] shadow-[0_4px_12px_rgba(143,88,38,0.2)] hover:shadow-[0_8px_20px_rgba(143,88,38,0.3)] transition-all duration-300 h-full flex flex-col">
      <Link href={`/post/${id}`} className="block h-full">
        <div className="relative h-48 bg-gray-100">
          {hasValidImage ? (
            <Image
              key={thumbnailImage} // Add key to force re-render when URL changes
              src={thumbnailImage!}
              alt={title}
              fill
              sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
              className="object-cover"
              loading="lazy"
              quality={75}
              onError={() => {
                console.error('Error loading image:', thumbnailImage);
                setImgError(true);
              }}
              unoptimized={isFirebaseStorageUrl ? true : undefined} // Skip Next.js optimization for Firebase Storage URLs
            />
          ) : (
            <RamiyariPlaceholder />
          )}
        </div>
        <div className="p-4 flex flex-col flex-grow">
          <h3 className="text-lg font-bold text-black mb-2 line-clamp-2">{title}</h3>
          <div className="flex items-center text-gray-500 text-sm mb-2">
            <span className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              {date ? new Date(date.toDate()).toLocaleDateString('ku-IQ') : ''}
            </span>
            <span className="mx-2">•</span>
            <span className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              {viewCount || 0} بینین
            </span>
          </div>
          {category && (
            <span className="inline-block bg-[var(--primary-100)] text-[var(--primary-700)] px-2 py-1 text-xs font-semibold rounded-md mt-auto">
              {category}
            </span>
          )}
        </div>
      </Link>
    </div>
  );
}
