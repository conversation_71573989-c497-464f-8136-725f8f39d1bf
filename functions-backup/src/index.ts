import * as functions from "firebase-functions";
import * as admin from "firebase-admin";

// Initialize Firebase Admin
admin.initializeApp();

// Reference to Firestore
const db = admin.firestore();

/**
 * Scheduled function that runs daily at 2 AM London time (UTC+1 or UTC+0 depending on DST)
 * Adds 100 views to all published posts
 */
export const incrementDailyViews = functions.pubsub
  .schedule("0 2 * * *") // Run at 2 AM every day
  .timeZone("Europe/London") // London time zone
  .onRun(async (context) => {
    try {
      console.log("Starting daily view count increment...");
      
      // Get all published posts
      const postsRef = db.collection("posts");
      const publishedPosts = await postsRef
        .where("status", "==", "بڵاوکراوەتەوە")
        .get();
      
      console.log(`Found ${publishedPosts.size} published posts to update`);
      
      // Batch updates for better performance
      const batchSize = 500; // Firestore allows max 500 operations per batch
      let operationCount = 0;
      let batch = db.batch();
      
      // Process each post
      for (const postDoc of publishedPosts.docs) {
        const currentViewCount = postDoc.data().viewCount || 0;
        const newViewCount = currentViewCount + 100;
        
        // Add to batch
        batch.update(postDoc.ref, { viewCount: newViewCount });
        operationCount++;
        
        // If batch is full, commit it and start a new one
        if (operationCount >= batchSize) {
          await batch.commit();
          console.log(`Committed batch of ${operationCount} updates`);
          batch = db.batch();
          operationCount = 0;
        }
      }
      
      // Commit any remaining updates
      if (operationCount > 0) {
        await batch.commit();
        console.log(`Committed final batch of ${operationCount} updates`);
      }
      
      console.log("Successfully added 100 views to all published posts");
      return null;
    } catch (error) {
      console.error("Error incrementing view counts:", error);
      return null;
    }
  });

// You can add more functions here as needed
