'use client';

import { CSSProperties } from 'react';

interface RamiyariPlaceholderProps {
  className?: string;
  style?: CSSProperties;
}

export default function RamiyariPlaceholder({ className = '', style = {} }: RamiyariPlaceholderProps) {
  return (
    <div 
      className={`flex items-center justify-center bg-gradient-to-br from-[#f9f5f0] to-[#e6d7c3] ${className}`}
      style={{
        width: '100%',
        height: '100%',
        position: 'absolute',
        top: 0,
        left: 0,
        ...style
      }}
    >
      <div className="text-center">
        <div className="text-[#8f5826] font-bold text-2xl md:text-3xl lg:text-4xl">
Ramiyari.com        </div>
        <div className="text-[#8f5826] opacity-70 text-sm md:text-base mt-2">
ئازادی لە دەبرینی نارەزایی        </div>
      </div>
    </div>
  );
}
