import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import GoogleTranslatePost from '@/components/GoogleTranslatePost';

export default function TranslateTestPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">

          {/* Kurdish Google Translate Widget - Small Button Demo */}
          <div className="bg-gray-100 p-4 rounded-lg mb-8">
            <h2 className="text-xl font-bold mb-4" translate="no">Small Translate Button Demo</h2>
            <div className="flex items-center text-gray-500 text-sm">
              <span className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                ٢٠٢٤/١٢/٢٨
              </span>
              <span className="mx-2">•</span>
              <span className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                ١٢٣ بینین
              </span>
              <span className="mx-2">•</span>
              <GoogleTranslatePost postId="test-page" />
            </div>
            <p className="text-sm text-gray-600 mt-2">The translate button now appears inline with the view count and date.</p>
          </div>

          {/* Kurdish Sorani Content Test */}
          <div className="bg-blue-100 p-6 rounded-lg mb-8">
            <h2 className="text-2xl font-bold mb-6 text-center" translate="no">تاقیکردنەوەی وەرگێڕانی کوردی سۆرانی</h2>
            <div className="space-y-6">
              <p translate="yes" lang="ckb" className="text-lg leading-relaxed">
                ئەمە دەقێکی کوردی سۆرانییە کە دەبێت بە ئاسانی وەربگێردرێت بۆ زمانەکانی تر. گووگڵ ترانسلەیت دەبێت بتوانێت ئەم دەقە بناسێتەوە و وەربگێڕێت.
              </p>
              <p translate="yes" lang="ckb" className="text-lg leading-relaxed">
                ڕامیاری سەرچاوەی هەواڵ و زانیاری ڕامیاری و کۆمەڵایەتییە. ئێمە هەوڵدەدەین کە باشترین خزمەتگوزاری بۆ خوێنەرانمان دابین بکەین.
              </p>
              <p translate="yes" lang="ckb" className="text-lg leading-relaxed">
                ئەم بابەتە لە ڕامیاری.کۆم بڵاوکراوەتەوە و دەتوانرێت بە زمانەکانی جیاواز وەربگێردرێت. ئەم دوگمەیە بەکاربهێنە بۆ وەرگێڕان بۆ ئینگلیزی.
              </p>
            </div>
          </div>
          <h1 className="text-3xl md:text-4xl font-bold text-black mb-8" translate="yes">
            تاقیکردنەوەی گۆڕینی زمان
          </h1>
          
          <div className="space-y-6">
            <section className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-2xl font-bold text-black mb-4" translate="yes">
                ناونیشانی یەکەم
              </h2>
              <p className="text-gray-700 mb-4" translate="yes">
                ئەمە دەقێکی تاقیکردنەوەیە بۆ بینینی ئەوەی کە ئایا گووگڵ ترانسلەیت دەتوانێت ئەم ناوەڕۆکە وەربگێڕێت یان نا. ئەم دەقە بە زمانی کوردی نووسراوە و دەبێت بە ئاسانی وەربگێردرێت.
              </p>
              <p className="text-gray-700" translate="yes">
                کاتێک کە گووگڵ ترانسلەیت کاردەکات، دەبێت بتوانێت ئەم دەقە بناسێتەوە و وەربگێڕێت بۆ زمانەکانی تر وەک ئینگلیزی، عەرەبی، فارسی و هتد.
              </p>
            </section>

            <section className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-2xl font-bold text-black mb-4" translate="yes">
                ناونیشانی دووەم
              </h2>
              <div className="prose prose-lg max-w-none blog-content" translate="yes">
                <p>ئەمە نموونەیەکی دیکەی دەقە کە لە ناو کۆمپۆنێنتی PostContent دا بەکاردەهێنرێت. ئەم دەقە دەبێت بە هەمان شێوە وەربگێردرێت.</p>
                
                <h3>ژێرناونیشان</h3>
                <p>ئەمە پەرەگرافێکی دیکەیە کە زانیاری زیاتر لەخۆدەگرێت. دەبێت گووگڵ ترانسلەیت بتوانێت ئەم هەموو دەقانە بناسێتەوە.</p>
                
                <ul>
                  <li>خاڵی یەکەم</li>
                  <li>خاڵی دووەم</li>
                  <li>خاڵی سێیەم</li>
                </ul>
              </div>
            </section>

            <section className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-2xl font-bold text-black mb-4" translate="no">
                ناونیشانی نەگۆڕ (translate="no")
              </h2>
              <p className="text-gray-700 mb-4" translate="yes">
                ئەم بەشە نیشاندەدات کە چۆن دەتوانین کۆنترۆڵی وەرگێڕان بکەین. ناونیشانەکە نابێت وەربگێردرێت بەڵام ئەم دەقە دەبێت وەربگێردرێت.
              </p>
            </section>

            <section className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-2xl font-bold text-black mb-4" translate="yes">
                تاقیکردنەوەی ناوەڕۆکی HTML
              </h2>
              <div 
                className="prose prose-lg max-w-none blog-content"
                translate="yes"
                dangerouslySetInnerHTML={{
                  __html: `
                    <p>ئەمە ناوەڕۆکێکە کە بە dangerouslySetInnerHTML دانراوە، وەک ئەوەی لە PostContent کۆمپۆنێنت بەکاردەهێنرێت.</p>
                    <h3>ژێرناونیشانی HTML</h3>
                    <p>ئەم دەقە دەبێت بە هەمان شێوە وەربگێردرێت وەک دەقی ئاسایی.</p>
                    <p><strong>دەقی قەڵەو</strong> و <em>دەقی لار</em> هەردووکیان دەبێت وەربگێردرێن.</p>
                  `
                }}
              />
            </section>

            <section className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-2xl font-bold text-black mb-4" translate="yes">
                زانیاری تەکنیکی
              </h2>
              <div className="bg-gray-100 p-4 rounded-md" translate="no">
                <p className="text-sm text-gray-600 mb-2">Technical Information (should not be translated):</p>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>Language: ckb (Central Kurdish)</li>
                  <li>Direction: RTL</li>
                  <li>Font: Noto Kufi Arabic</li>
                  <li>Google Translate Meta: Enabled</li>
                </ul>
              </div>
            </section>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
