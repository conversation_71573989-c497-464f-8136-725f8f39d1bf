'use client';

import { useEffect, useRef } from 'react';

interface GoogleTranslatePostProps {
  postId: string;
}

export default function GoogleTranslatePost({ postId }: GoogleTranslatePostProps) {
  const translateRef = useRef<HTMLDivElement>(null);
  const scriptLoadedRef = useRef(false);

  useEffect(() => {
    // Only load Google Translate for post pages
    if (!scriptLoadedRef.current) {
      loadGoogleTranslateScript();
      scriptLoadedRef.current = true;
    }
  }, []);

  const loadGoogleTranslateScript = () => {
    // Check if Google Translate is already loaded
    if ((window as any).google && (window as any).google.translate) {
      initializeGoogleTranslate();
      return;
    }

    // Create the callback function
    (window as any).googleTranslateElementInit = () => {
      initializeGoogleTranslate();
    };

    // Load the Google Translate script
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = 'https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit';
    script.async = true;
    script.onerror = () => {
      console.error('Failed to load Google Translate script');
    };
    
    document.head.appendChild(script);
  };

  const initializeGoogleTranslate = () => {
    try {
      if (translateRef.current && (window as any).google && (window as any).google.translate) {
        new (window as any).google.translate.TranslateElement({
          pageLanguage: 'auto', // Auto-detect source language (better for Kurdish)
          includedLanguages: 'en,ar,fa,tr,de,fr,es,it,ru,zh,ja,ku', // Include Kurdish in target languages
          layout: (window as any).google.translate.TranslateElement.InlineLayout.SIMPLE,
          autoDisplay: false,
          multilanguagePage: true,
          gaTrack: false,
          gaId: null
        }, translateRef.current);
      }
    } catch (error) {
      console.error('Error initializing Google Translate:', error);
    }
  };

  return (
    <div className="google-translate-container mb-6 p-4 bg-gray-50 rounded-lg border">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-700" translate="no">
          وەرگێڕانی ئەم بابەتە
        </h3>
        <span className="text-xs text-gray-500" translate="no">
          Google Translate
        </span>
      </div>
      
      <div ref={translateRef} id={`google_translate_element_${postId}`} className="google-translate-widget">
        {/* Google Translate widget will be inserted here */}
      </div>
      
      <p className="text-xs text-gray-500 mt-2" translate="no">
        ئەم خزمەتگوزارییە لەلایەن گووگڵەوە دابین کراوە
      </p>

      <style jsx global>{`
        .google-translate-container .goog-te-banner-frame {
          display: none !important;
        }
        
        .google-translate-container .goog-te-gadget {
          font-family: 'Noto Kufi Arabic', Arial, sans-serif !important;
          font-size: 12px !important;
        }
        
        .google-translate-container .goog-te-gadget-simple {
          background-color: transparent !important;
          border: 1px solid #d1d5db !important;
          border-radius: 6px !important;
          padding: 8px 12px !important;
          font-size: 14px !important;
        }
        
        .google-translate-container .goog-te-gadget-simple .goog-te-menu-value {
          color: #374151 !important;
          font-family: 'Noto Kufi Arabic', Arial, sans-serif !important;
        }
        
        .google-translate-container .goog-te-gadget-simple .goog-te-menu-value:hover {
          color: #8f5826 !important;
        }
        
        .google-translate-container .goog-te-gadget-icon {
          background-image: none !important;
          margin-right: 8px !important;
        }
        
        .google-translate-container .goog-te-gadget-icon::before {
          content: "🌐";
          font-size: 16px;
        }
        
        /* Hide the Google Translate top banner */
        body .goog-te-banner-frame {
          display: none !important;
        }
        
        body.goog-te-hl {
          top: 0 !important;
        }
        
        /* Style the translated content */
        .goog-te-combo {
          font-family: 'Noto Kufi Arabic', Arial, sans-serif !important;
          padding: 4px 8px !important;
          border: 1px solid #d1d5db !important;
          border-radius: 4px !important;
          background-color: white !important;
        }
      `}</style>
    </div>
  );
}
