'use client';

import { useState } from 'react';

interface GoogleTranslatePostProps {
  postId: string;
}

export default function GoogleTranslatePost({ postId }: GoogleTranslatePostProps) {
  const [isTranslating, setIsTranslating] = useState(false);



  const translateToEnglish = () => {
    if (isTranslating) return;

    setIsTranslating(true);

    try {
      // Use Google Translate URL method for more reliable translation
      const currentUrl = window.location.href;
      const translateUrl = `https://translate.google.com/translate?sl=ckb&tl=en&u=${encodeURIComponent(currentUrl)}`;

      // Open translation in the same window
      window.location.href = translateUrl;

    } catch (error) {
      console.error('Error translating page:', error);
      setIsTranslating(false);
    }
  };



  return (
    <button
      type="button"
      onClick={translateToEnglish}
      disabled={isTranslating}
      className="group inline-flex items-center text-gray-500 text-sm hover:text-[#8f5826] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
      translate="no"
    >
      {isTranslating ? (
        <>
          <svg className="animate-spin h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Translating...
        </>
      ) : (
        <>
          <svg className="h-4 w-4 mr-1 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
          </svg>
          Translate to EN
        </>
      )}
    </button>


    </div>
  );
}
