(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[588],{5086:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var a=t(5155),o=t(9137),s=t.n(o),l=t(2115);let i=(0,t(5028).default)(()=>t.e(999).then(t.t.bind(t,2021,23)),{loadableGenerated:{webpack:()=>[2021]},ssr:!1}),n=e=>{let{value:r,onChange:t,placeholder:o="ناوەڕۆک بنووسە...",height:n=650,width:d="100%",readonly:c=!1,toolbar:m=!0,language:u="en",rtl:p=!0}=e,h=(0,l.useRef)(null),[g,b]=(0,l.useState)(r||"");(0,l.useEffect)(()=>{b(r)},[r]);let x=(0,l.useMemo)(()=>({readonly:c,placeholder:o,height:n,width:d,toolbar:m,language:u,direction:p?"rtl":"ltr",toolbarButtonSize:"middle",buttons:["source","|","bold","italic","underline","strikethrough","|","font","fontsize","brush","paragraph","|","align","indent","outdent","|","ul","ol","|","table","link","image","video","file","|","left","center","right","justify","imageCaption","|","hr","eraser","copyformat","|","superscript","subscript","|","selectall","cut","copy","paste","|","symbol","fullsize","print","about"],uploader:{insertImageAsBase64URI:!0},resizer:{min_width:10,min_height:10,showSize:!0},imageeditor:{crop:!0,resize:!0,resizeWidth:!0,resizeHeight:!0,ratio:!0},extraButtons:[{name:"imageCaption",tooltip:"Add caption to image",icon:"paragraph",exec:e=>{let r=e.selection.current();if(r&&"img"===r.nodeName.toLowerCase())try{let t=document.createElement("figure");t.className="image-with-caption",t.style.float="right",t.style.margin="10px 0 10px 15px",t.style.maxWidth="300px";let a=r.cloneNode(!0);a.style.float="none",a.style.margin="0",a.style.maxWidth="100%";let o=document.createElement("figcaption");o.innerHTML="وەسفی وێنە لێرە بنووسە...",o.style.textAlign="center",o.style.padding="5px",o.style.fontSize="14px",o.style.color="#666",t.appendChild(a),t.appendChild(o),e.selection.insertNode(t),r.parentNode&&r.parentNode.removeChild(r),e.selection.setCursorIn(o),console.log("Caption added successfully")}catch(e){console.error("Error adding caption:",e)}else alert("لطفا سەرەتا وێنەیەک دیاری بکە")}}],events:{afterInit:e=>{e.editor.style.direction="rtl",e.events.on("insertImage",e=>{e.style.float="right",e.style.margin="10px 0 10px 15px",e.style.maxWidth="300px"})}},style:{font:"Noto Kufi Arabic, sans-serif"}}),[c,o,n,d,m,u,p]);return(0,a.jsxs)("div",{className:"jsx-8cd3b8c15bb30d49 jodit-editor-wrapper",children:[(0,a.jsx)(i,{ref:h,value:g,config:x,tabIndex:1,onBlur:e=>{b(e),t(e)},onChange:e=>b(e)}),(0,a.jsx)(s(),{id:"8cd3b8c15bb30d49",children:'.jodit-editor-wrapper{margin-bottom:20px}.jodit-container{-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;font-family:"Noto Kufi Arabic",sans-serif;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px rgba(0,0,0,.1);box-shadow:0 1px 3px rgba(0,0,0,.1)}.jodit-wysiwyg{font-family:"Noto Kufi Arabic",sans-serif;padding:16px!important;font-size:16px!important;line-height:1.6!important}.jodit-toolbar__box{background-color:#f8f9fa;padding:8px!important}.jodit-toolbar-button{margin:0 3px;padding:6px!important}.jodit-toolbar-button__icon{width:20px!important;height:20px!important}.jodit-placeholder{font-family:"Noto Kufi Arabic",sans-serif;font-size:16px!important}.jodit-resizer{border:2px solid#8f5826!important}.jodit-resizer-point{background-color:#8f5826!important}.jodit-wysiwyg img{max-width:100%;height:auto;float:right;margin:10px 0 10px 15px}.jodit-wysiwyg p{overflow:auto}.jodit-wysiwyg p:after{content:"";display:table;clear:both}.jodit-wysiwyg{overflow:auto}.jodit-wysiwyg figure{display:inline-block;margin:0;max-width:100%}.jodit-wysiwyg figure.image-with-caption{float:right;margin:10px 0 10px 15px}.jodit-wysiwyg figure img{float:none;margin:0;display:block}.jodit-wysiwyg figcaption{text-align:center;font-size:14px;color:#666;padding:5px}'})]})}},5946:(e,r,t)=>{"use strict";t.d(r,{jn:()=>c,If:()=>i,V1:()=>d,PX:()=>n});var a=t(858),o=t(7039);async function s(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1200,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1200,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.85;return new Promise((o,s)=>{try{let l=new Image;l.onload=()=>{let i=l.width,n=l.height;if(i>r||n>t){let e=i/n;i>r&&(n=(i=r)/e),n>t&&(i=(n=t)*e)}let d=document.createElement("canvas");d.width=i,d.height=n;let c=d.getContext("2d");if(!c)return void s(Error("Could not get canvas context"));c.drawImage(l,0,0,i,n);let m=e.split(";")[0].split(":")[1]||"image/jpeg",u=d.toDataURL(m,a);o(u)},l.onerror=()=>{console.error("Error loading image for optimization"),o(e)},l.src=e}catch(r){console.error("Error optimizing image:",r),o(e)}})}async function l(e,r,t,l,i,n){try{let d=e;if(l&&i)try{d=await s(e,l,i,n),console.log("Image optimized successfully")}catch(e){console.error("Error optimizing image:",e)}let c=d.match(/^data:([A-Za-z-+/]+);base64,(.+)$/);if(!c||3!==c.length)throw Error("Invalid base64 image format");let m=c[1],u=c[2],p="jpg";m.includes("png")?p="png":m.includes("gif")?p="gif":m.includes("webp")&&(p="webp");let h=t||"".concat(Date.now(),".").concat(p),g=(0,a.KR)(o.IG,"".concat(r,"/").concat(h));try{let e=await (0,a.ls)(g,u,"base64",{contentType:m,cacheControl:"public, max-age=31536000"}),r=await (0,a.qk)(e.ref);return console.log("Image uploaded successfully to Firebase Storage:",r),r}catch(e){return console.error("Firebase Storage upload failed:",e),e instanceof Error&&(e.message.includes("unauthorized")?(console.warn("Storage permission denied. Using base64 fallback. Please update Firebase Storage rules."),alert("تکایە ڕێساکانی Firebase Storage نوێ بکەرەوە بۆ ڕێگەدان بە بارکردنی وێنەکان")):e.message.includes("CORS")&&console.warn("CORS error detected. Using base64 fallback. Please configure CORS for Firebase Storage.")),console.warn("Falling back to storing image as base64 in Firestore"),d}}catch(r){return console.error("Error processing image:",r),console.warn("Error occurred, falling back to base64 storage"),e}}function i(e){let r=Date.now(),t=Math.random().toString(36).substring(2,8);if(e){let a=e.split(".").pop()||"jpg";return"".concat(r,"-").concat(t,".").concat(a)}return"".concat(r,"-").concat(t,".jpg")}async function n(e,r){return l(e,"posts/thumbnails",r,600,600,.7)}async function d(e,r){return l(e,"posts/featured",r,1600,1600,.85)}async function c(e){try{var r;if(!e||!e.includes("firebasestorage.googleapis.com"))return console.warn("Not a Firebase Storage URL:",e),!1;let t=new URL(e),s=decodeURIComponent(null==(r=t.pathname.split("/o/")[1])?void 0:r.split("?")[0]);if(!s)return console.warn("Could not extract path from URL:",e),!1;let l=(0,a.KR)(o.IG,s);return await (0,a.XR)(l),console.log("File deleted successfully:",s),!0}catch(e){return console.error("Error deleting file:",e),!1}}},5995:()=>{},6302:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var a=t(5155),o=t(2115),s=t(5695),l=t(6766),i=t(7961),n=t(8997),d=t(5086),c=t(5317),m=t(7039),u=t(5946);function p(){let{user:e,loading:r}=(0,i.A)(),t=(0,s.useRouter)(),[p,h]=(0,o.useState)(""),[g,b]=(0,o.useState)(""),[x,f]=(0,o.useState)(""),[v,y]=(0,o.useState)(""),[w,j]=(0,o.useState)("draft"),[N,k]=(0,o.useState)(!1),[C,S]=(0,o.useState)(null),[E,F]=(0,o.useState)(null),[z,I]=(0,o.useState)(0),[L,R]=(0,o.useState)(new Date().toISOString().split("T")[0]),[A,D]=(0,o.useState)([]),[M,_]=(0,o.useState)([]),[P,B]=(0,o.useState)(!0),[U,W]=(0,o.useState)(!0);(0,o.useEffect)(()=>{async function r(){try{let e=(0,c.rJ)(m.db,"categories"),r=(await (0,c.GG)(e)).docs.map(e=>({id:e.id,name:e.data().name,slug:e.data().slug}));D(r)}catch(e){console.error("Error fetching categories:",e)}finally{B(!1)}}e&&r()},[e]),(0,o.useEffect)(()=>{async function r(){try{let e=(0,c.rJ)(m.db,"tags"),r=(await (0,c.GG)(e)).docs.map(e=>({id:e.id,name:e.data().name,slug:e.data().slug}));_(r)}catch(e){console.error("Error fetching tags:",e)}finally{W(!1)}}e&&r()},[e]),(0,o.useEffect)(()=>{r||e||t.push("/login")},[e,r,t]);let G=(e,r)=>{var t;let a=null==(t=e.target.files)?void 0:t[0];if(a){let e=new FileReader;e.onloadend=()=>{r(e.result)},e.readAsDataURL(a)}},[O,K]=(0,o.useState)(!1),[T,H]=(0,o.useState)(null),V=async r=>{r.preventDefault(),K(!0),H(null);try{if(!p.trim())throw Error("تکایە ناونیشانێک بنووسە");if(!g.trim())throw Error("تکایە ناوەڕۆکێک بنووسە");if(!e)throw Error("تکایە دووبارە چوونەژوورەوە بکە");let r=v.split(",").map(e=>e.trim()).filter(e=>e.length>0),a=null,o=null;if(C)try{let e=(0,u.If)("featured.jpg");a=await (0,u.V1)(C,e),console.log("Featured image uploaded and optimized:",a)}catch(e){throw console.error("Error uploading featured image:",e),Error("هەڵە لە بارکردنی وێنەی سەرەکی")}if(E)try{let e=(0,u.If)("thumbnail.jpg");o=await (0,u.PX)(E,e),console.log("Thumbnail image uploaded and optimized:",o)}catch(e){throw console.error("Error uploading thumbnail image:",e),Error("هەڵە لە بارکردنی وێنەی بچووک")}console.log("Saving content with dimensions:",g);let s={title:p,content:g,category:x,tags:r,status:"publish"===w?"بڵاوکراوەتەوە":"پێشنووس",isPrimary:N,featuredImage:a,thumbnailImage:o,viewCount:z,date:c.Dc.fromDate(new Date(L)),author:(null==e?void 0:e.email)||"ئەدمین",createdAt:c.Dc.now(),updatedAt:c.Dc.now()};console.log("Saving post to Firestore...");let l=(0,c.rJ)(m.db,"posts"),i=await (0,c.gS)(l,s);console.log("Post saved successfully with ID:",i.id),alert("بابەت بە سەرکەوتوویی پاشەکەوت کرا!"),t.push("/dashboard/posts")}catch(e){console.error("Error saving post:",e),H(e.message||"هەڵەیەک ڕوویدا لە پاشەکەوتکردنی بابەت")}finally{K(!1)}};return r?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("p",{className:"text-xl",children:"باری ڕاستاندنەکە بارکردن..."})}):e?(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"max-w-5xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-black",children:"بابەتی نوێ"}),(0,a.jsx)("button",{type:"button",onClick:()=>t.push("/dashboard/posts"),className:"px-4 py-2 bg-[var(--gray-500)] text-white rounded-md hover:bg-[var(--gray-600)] transition-colors cursor-pointer",children:"گەڕانەوە"})]}),(0,a.jsxs)("div",{className:"bg-[var(--card-bg)] rounded-lg shadow-md border border-[var(--card-border)] p-6",children:[T&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md",children:T}),(0,a.jsxs)("form",{onSubmit:V,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"title",className:"block text-black text-sm font-bold mb-2",children:"ناونیشان"}),(0,a.jsx)("input",{type:"text",id:"title",value:p,onChange:e=>h(e.target.value),className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors",required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-black text-sm font-bold mb-2",children:"وێنەی سەرەکی"}),(0,a.jsxs)("div",{className:"border-2 border-dashed border-[var(--gray-300)] rounded-md p-4 text-center",children:[C?(0,a.jsxs)("div",{className:"relative h-48 mb-2",children:[(0,a.jsx)(l.default,{src:C,alt:"Featured",fill:!0,style:{objectFit:"cover"},className:"rounded-md"}),(0,a.jsx)("button",{type:"button",onClick:()=>S(null),className:"absolute top-2 left-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors cursor-pointer",title:"سڕینەوەی وێنە","aria-label":"سڕینەوەی وێنە",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}):(0,a.jsxs)("div",{className:"h-48 flex flex-col items-center justify-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-[var(--gray-400)]",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),(0,a.jsx)("p",{className:"mt-2 text-sm text-black",children:"کرتە بکە بۆ هەڵبژاردنی وێنە"})]}),(0,a.jsx)("input",{type:"file",id:"featuredImage",accept:"image/*",className:"hidden",onChange:e=>G(e,S)}),(0,a.jsx)("label",{htmlFor:"featuredImage",className:"mt-2 inline-block px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors cursor-pointer",children:C?"گۆڕینی وێنە":"هەڵبژاردنی وێنە"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-black text-sm font-bold mb-2",children:"وێنەی بچووک"}),(0,a.jsxs)("div",{className:"border-2 border-dashed border-[var(--gray-300)] rounded-md p-4 text-center",children:[E?(0,a.jsxs)("div",{className:"relative h-48 mb-2",children:[(0,a.jsx)(l.default,{src:E,alt:"Thumbnail",fill:!0,style:{objectFit:"cover"},className:"rounded-md"}),(0,a.jsx)("button",{type:"button",onClick:()=>F(null),className:"absolute top-2 left-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors cursor-pointer",title:"سڕینەوەی وێنە","aria-label":"سڕینەوەی وێنە",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}):(0,a.jsxs)("div",{className:"h-48 flex flex-col items-center justify-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-[var(--gray-400)]",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),(0,a.jsx)("p",{className:"mt-2 text-sm text-black",children:"کرتە بکە بۆ هەڵبژاردنی وێنە"})]}),(0,a.jsx)("input",{type:"file",id:"thumbnailImage",accept:"image/*",className:"hidden",onChange:e=>G(e,F)}),(0,a.jsx)("label",{htmlFor:"thumbnailImage",className:"mt-2 inline-block px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors cursor-pointer",children:E?"گۆڕینی وێنە":"هەڵبژاردنی وێنە"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"content",className:"block text-black text-sm font-bold mb-2",children:"ناوەڕۆک"}),(0,a.jsx)(d.A,{value:g,onChange:e=>{console.log("Editor content updated"),b(e)},placeholder:"ناوەڕۆک بنووسە...",height:650,width:"100%",rtl:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"category",className:"block text-black text-sm font-bold mb-2",children:"پۆل"}),P?(0,a.jsxs)("div",{className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md bg-gray-50 flex items-center",children:[(0,a.jsx)("div",{className:"inline-block w-4 h-4 border-2 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin ml-2"}),(0,a.jsx)("span",{className:"text-gray-500",children:"بارکردنی پۆلەکان..."})]}):(0,a.jsxs)("select",{id:"category",value:x,onChange:e=>f(e.target.value),className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors cursor-pointer",children:[(0,a.jsx)("option",{value:"",children:"هەڵبژاردنی پۆل"}),A.map(e=>(0,a.jsx)("option",{value:e.slug,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"tags",className:"block text-black text-sm font-bold mb-2",children:"تاگەکان"}),U?(0,a.jsxs)("div",{className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md bg-gray-50 flex items-center",children:[(0,a.jsx)("div",{className:"inline-block w-4 h-4 border-2 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin ml-2"}),(0,a.jsx)("span",{className:"text-gray-500",children:"بارکردنی تاگەکان..."})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:v.split(",").map(e=>e.trim()).filter(e=>e.length>0).map((e,r)=>(0,a.jsxs)("span",{className:"bg-[var(--primary-100)] text-[var(--primary-700)] px-2 py-1 rounded-md text-sm flex items-center",children:[e,(0,a.jsx)("button",{type:"button",onClick:()=>{let e=v.split(",").map(e=>e.trim()).filter(e=>e.length>0);e.splice(r,1),y(e.join(", "))},className:"ml-1 text-[var(--primary-500)] hover:text-[var(--primary-700)] transition-colors cursor-pointer",title:"سڕینەوەی تاگ","aria-label":"سڕینەوەی تاگ",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]},r))}),(0,a.jsx)("div",{className:"flex",children:(0,a.jsxs)("select",{id:"tagSelector",name:"tagSelector","aria-label":"هەڵبژاردنی تاگ",title:"هەڵبژاردنی تاگ",className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors cursor-pointer",onChange:e=>{if(e.target.value){let r=e.target.value,t=v.split(",").map(e=>e.trim()).filter(e=>e.length>0);t.includes(r)||y([...t,r].join(", ")),e.target.value=""}},children:[(0,a.jsx)("option",{value:"",children:"هەڵبژاردنی تاگ"}),M.map(e=>(0,a.jsx)("option",{value:e.name,children:e.name},e.id))]})})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"publishDate",className:"block text-black text-sm font-bold mb-2",children:"بەرواری بڵاوکردنەوە"}),(0,a.jsx)("input",{type:"date",id:"publishDate",value:L,onChange:e=>R(e.target.value),className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors cursor-pointer"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"viewCount",className:"block text-black text-sm font-bold mb-2",children:"ژمارەی بینەران"}),(0,a.jsx)("input",{type:"number",id:"viewCount",value:z,onChange:e=>I(parseInt(e.target.value)||0),min:"0",className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-black text-sm font-bold mb-2",children:"دۆخ"}),(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",id:"draft",name:"status",value:"draft",checked:"draft"===w,onChange:()=>j("draft"),className:"ml-2 h-4 w-4 text-[var(--primary-600)] cursor-pointer"}),(0,a.jsx)("label",{htmlFor:"draft",className:"ml-2 text-black cursor-pointer hover:text-[var(--primary-600)] transition-colors",children:"پێشنووس"})]}),(0,a.jsxs)("div",{className:"flex items-center mr-6",children:[(0,a.jsx)("input",{type:"radio",id:"publish",name:"status",value:"publish",checked:"publish"===w,onChange:()=>j("publish"),className:"ml-2 h-4 w-4 text-[var(--primary-600)] cursor-pointer"}),(0,a.jsx)("label",{htmlFor:"publish",className:"ml-2 text-black cursor-pointer hover:text-[var(--primary-600)] transition-colors",children:"بڵاوکردنەوە"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-black text-sm font-bold mb-2",children:"بابەتی سەرەکی"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:"relative inline-block w-10 ml-2 align-middle select-none transition duration-200 ease-in",children:[(0,a.jsx)("input",{type:"checkbox",name:"isPrimary",id:"isPrimary",checked:N,onChange:()=>k(!N),className:"toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"}),(0,a.jsx)("label",{htmlFor:"isPrimary",className:"toggle-label block overflow-hidden h-6 rounded-full cursor-pointer ".concat(N?"bg-[var(--primary-600)]":"bg-gray-300")})]}),(0,a.jsx)("label",{htmlFor:"isPrimary",className:"text-black cursor-pointer hover:text-[var(--primary-600)] transition-colors",children:N?"بەڵێ":"نەخێر"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-[var(--gray-200)]",children:[(0,a.jsx)("button",{type:"submit",disabled:O,className:"px-6 py-3 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors font-medium flex items-center cursor-pointer ".concat(O?"opacity-70 cursor-not-allowed":""),children:O?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"}),(0,a.jsx)("span",{children:"پاشەکەوتکردن..."})]}):"پاشەکەوتکردن"}),(0,a.jsx)("button",{type:"button",disabled:O,onClick:()=>{h(""),b(""),f(""),y(""),j("draft"),S(null),F(null),I(0),R(new Date().toISOString().split("T")[0]),window.location.reload()},className:"px-6 py-3 bg-[var(--gray-500)] text-white rounded-md hover:bg-[var(--gray-600)] transition-colors font-medium cursor-pointer ".concat(O?"opacity-70 cursor-not-allowed":""),children:"پاککردنەوە"})]})]})]})]})}):null}t(5995)},9092:(e,r,t)=>{Promise.resolve().then(t.bind(t,6302))}},e=>{var r=r=>e(e.s=r);e.O(0,[738,992,118,288,244,766,659,997,441,684,358],()=>r(9092)),_N_E=e.O()}]);