const { chromium } = require('playwright');

(async () => {
  // Launch the browser
  const browser = await chromium.launch({ 
    headless: false, // Show the browser
    slowMo: 100 // Slow down operations for visibility
  });
  
  // Create a new browser context
  const context = await browser.newContext({
    viewport: { width: 1280, height: 800 }
  });
  
  // Open a new page
  const page = await context.newPage();
  
  try {
    // Set up console log capture
    page.on('console', msg => {
      console.log(`PAGE LOG: ${msg.text()}`);
    });
    
    // Set up response logging for image requests
    page.on('response', response => {
      const url = response.url();
      if (url.includes('firebasestorage.googleapis.com') || url.includes('image') || url.endsWith('.jpg') || url.endsWith('.png')) {
        console.log(`Image response: ${url} - Status: ${response.status()}`);
      }
    });
    
    // Navigate to the login page
    console.log('Navigating to the login page...');
    await page.goto('http://localhost:3000/login');
    
    // Wait for the login form to load
    console.log('Waiting for the login form to load...');
    await page.waitForSelector('form', { timeout: 10000 });
    
    // Fill in the login form with the provided credentials
    console.log('Filling in login credentials...');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Azhee2001');
    
    // Submit the form
    console.log('Submitting login form...');
    await page.click('button[type="submit"]');
    
    // Wait for navigation after login
    console.log('Waiting for navigation after login...');
    await page.waitForNavigation({ timeout: 30000 });
    
    // Navigate to the new post page
    console.log('Navigating to the new post page...');
    await page.goto('http://localhost:3000/dashboard/posts/new');
    
    // Wait for the editor to load
    console.log('Waiting for the editor to load...');
    await page.waitForSelector('.lexical-editor-container', { timeout: 10000 });
    
    console.log('Successfully loaded the editor page. You can now manually test image uploads.');
    console.log('The browser will remain open for manual testing.');
    console.log('Press Ctrl+C in the terminal when you want to close the browser.');
    
    // Keep the script running
    await new Promise(() => {}); // This promise never resolves, keeping the script running
    
  } catch (error) {
    console.error('Error:', error);
    await page.screenshot({ path: 'error-screenshot.png', fullPage: true });
  }
})();
