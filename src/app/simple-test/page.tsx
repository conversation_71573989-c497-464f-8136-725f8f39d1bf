export default function SimpleTestPage() {
  return (
    <html lang="en">
      <head>
        <title>Simple Translation Test</title>
        <meta name="google" content="translate" />
      </head>
      <body style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
        <h1>Simple Translation Test</h1>
        
        <div style={{ background: '#f0f0f0', padding: '20px', margin: '20px 0', borderRadius: '8px' }}>
          <h2>English Content</h2>
          <p>This is a simple English paragraph that should be easily translatable by Google Translate.</p>
          <p>Google Translate should be able to detect and translate this content without any issues.</p>
        </div>

        <div style={{ background: '#e8f4fd', padding: '20px', margin: '20px 0', borderRadius: '8px' }}>
          <h2>Kurdish Content</h2>
          <p>ئەمە دەقێکی کوردییە کە دەبێت بە ئاسانی وەربگێردرێت.</p>
          <p>گووگڵ ترانسلەیت دەبێت بتوانێت ئەم دەقە بناسێتەوە و وەربگێڕێت.</p>
        </div>

        <div style={{ background: '#fff3cd', padding: '20px', margin: '20px 0', borderRadius: '8px' }}>
          <h2>Arabic Content</h2>
          <p>هذا نص عربي يجب أن يترجم بسهولة.</p>
          <p>جوجل ترانسليت يجب أن يكون قادرا على ترجمة هذا النص.</p>
        </div>

        <div style={{ background: '#d4edda', padding: '20px', margin: '20px 0', borderRadius: '8px' }}>
          <h2>Instructions</h2>
          <p>Right-click on this page and select "Translate to [your language]" to test Google Translate functionality.</p>
          <p>Or use the Google Translate browser extension if you have it installed.</p>
        </div>
      </body>
    </html>
  );
}
