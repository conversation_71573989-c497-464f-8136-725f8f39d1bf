import { useState, useRef } from 'react';
import Image from 'next/image';
import styles from './FeaturedImageComponent.module.css';
import RamiyariPlaceholder from './RamiyariPlaceholder';

interface FeaturedImageComponentProps {
  imageUrl: string;
  title: string;
}

export default function FeaturedImageComponent({ imageUrl, title }: FeaturedImageComponentProps) {
  const [imgError, setImgError] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);

  // Check if it's a Firebase Storage URL
  const isFirebaseStorageUrl = imageUrl && imageUrl.includes('firebasestorage.googleapis.com');

  // Check if we have a valid image URL
  const hasValidImage = imageUrl && !imgError && imageUrl.trim() !== '';

  return (
    <div className={styles.featuredImageContainer}>
      {/* Container with aspect ratio */}
      <div className={styles.aspectRatioContainer}>
        {hasValidImage ? (
          <Image
            key={imageUrl} // Add key to force re-render when URL changes
            ref={imageRef}
            src={imageUrl}
            alt={title}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px"
            className={styles.featuredImage}
            priority
            loading="eager"
            quality={85}
            onError={() => {
              console.error('Error loading featured image:', imageUrl);
              setImgError(true);
            }}
            unoptimized={isFirebaseStorageUrl ? true : undefined} // Skip Next.js optimization for Firebase Storage URLs
          />
        ) : (
          <RamiyariPlaceholder />
        )}
      </div>
    </div>
  );
}
