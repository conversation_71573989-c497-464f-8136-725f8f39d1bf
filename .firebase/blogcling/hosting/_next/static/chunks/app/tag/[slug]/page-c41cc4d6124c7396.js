(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[55],{3310:e=>{e.exports={featuredPost:"FeaturedPost_featuredPost__s1v6r",imageContainer:"FeaturedPost_imageContainer__tN_ZP",contentContainer:"FeaturedPost_contentContainer__ii0wc",title:"FeaturedPost_title__pyXoN",meta:"FeaturedPost_meta__I_SqY",metaItem:"FeaturedPost_metaItem__AoiLn",excerpt:"FeaturedPost_excerpt__A0L8I",readMore:"FeaturedPost_readMore__X6wyR"}},4636:(e,t,s)=>{Promise.resolve().then(s.bind(s,9524))},4672:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var a=s(5155),r=s(2115),o=s(6766),l=s(6874),n=s.n(l),i=s(3310),c=s.n(i),d=s(9788);function m(e){let{post:t}=e;console.log("FeaturedPost rendering with post:",t),console.log("FeaturedPost image URL:",t.thumbnailImage);let[s,l]=(0,r.useState)(!1),i=t.thumbnailImage&&(t.thumbnailImage.startsWith("http://")||t.thumbnailImage.startsWith("https://")),m=i&&t.thumbnailImage.includes("firebasestorage.googleapis.com");return(0,a.jsxs)("div",{className:c().featuredPost,children:[(0,a.jsx)("div",{className:c().imageContainer,children:(0,a.jsx)(n(),{href:"/post/".concat(t.id),style:{display:"block",height:"100%",position:"relative"},children:i&&!s?(0,a.jsx)(o.default,{src:t.thumbnailImage,alt:t.title,fill:!0,sizes:"(max-width: 768px) 100vw, 40vw",className:"object-cover",loading:"eager",priority:!0,quality:90,onError:()=>{console.error("Error loading image:",t.thumbnailImage),l(!0)},unoptimized:!!m||void 0},t.thumbnailImage):(0,a.jsx)(d.A,{})})}),(0,a.jsxs)("div",{className:c().contentContainer,children:[(0,a.jsx)("h2",{className:c().title,children:t.title}),(0,a.jsxs)("div",{className:c().meta,children:[(0,a.jsxs)("span",{className:c().metaItem,children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),t.date?new Date(t.date.toDate()).toLocaleDateString("ku-IQ"):""]}),(0,a.jsxs)("span",{className:c().metaItem,children:[(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),t.viewCount||0," بینین"]})]}),t.content&&(0,a.jsx)("div",{className:c().excerpt,children:(0,a.jsx)("div",{dangerouslySetInnerHTML:{__html:t.content.replace(/<[^>]*>/g,"").substring(0,150)+"..."}})}),(0,a.jsx)(n(),{href:"/post/".concat(t.id),className:c().readMore,children:"خوێندنەوەی زیاتر"})]})]})}},7624:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var a=s(5155),r=s(2115),o=s(6766),l=s(6874),n=s.n(l),i=s(9788);function c(e){let{id:t,title:s,thumbnailImage:l,date:c,category:d,viewCount:m}=e,[h,g]=(0,r.useState)(!1),x=l&&(l.startsWith("http://")||l.startsWith("https://")),u=x&&l&&l.includes("firebasestorage.googleapis.com");return(0,a.jsx)("div",{className:"bg-[#f9f5f0] rounded-lg overflow-hidden border-2 border-[#8f5826] shadow-[0_4px_12px_rgba(143,88,38,0.2)] hover:shadow-[0_8px_20px_rgba(143,88,38,0.3)] transition-all duration-300 h-full flex flex-col",children:(0,a.jsxs)(n(),{href:"/post/".concat(t),className:"block h-full",children:[(0,a.jsx)("div",{className:"relative h-48 bg-gray-100",children:x&&!h?(0,a.jsx)(o.default,{src:l,alt:s,fill:!0,sizes:"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw",className:"object-cover",loading:"lazy",quality:75,onError:()=>{console.error("Error loading image:",l),g(!0)},unoptimized:!!u||void 0},l):(0,a.jsx)(i.A,{})}),(0,a.jsxs)("div",{className:"p-4 flex flex-col flex-grow",children:[(0,a.jsx)("h3",{className:"text-lg font-bold text-black mb-2 line-clamp-2",children:s}),(0,a.jsxs)("div",{className:"flex items-center text-gray-500 text-sm mb-2",children:[(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),c?new Date(c.toDate()).toLocaleDateString("ku-IQ"):""]}),(0,a.jsx)("span",{className:"mx-2",children:"•"}),(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),m||0," بینین"]})]}),d&&(0,a.jsx)("span",{className:"inline-block bg-[var(--primary-100)] text-[var(--primary-700)] px-2 py-1 text-xs font-semibold rounded-md mt-auto",children:d})]})]})})}},9524:(e,t,s)=>{"use strict";s.d(t,{default:()=>m});var a=s(5155),r=s(2115),o=s(7624),l=s(5317),n=s(7039),i=s(1290),c=s(6821),d=s(4672);function m(e){let{slug:t}=e,[s,m]=(0,r.useState)([]),[h,g]=(0,r.useState)(""),[x,u]=(0,r.useState)(!0),[p,f]=(0,r.useState)(null),[w,j]=(0,r.useState)(!0),[v,y]=(0,r.useState)(!1);(0,r.useEffect)(()=>{async function e(){try{u(!0),m([]),f(null),j(!0);let e=(0,l.rJ)(n.db,"tags"),s=(0,l.P)(e,(0,l._M)("slug","==",t)),a=await (0,l.GG)(s);if(console.log('Searching for tag with slug "'.concat(t,'"')),console.log("Found ".concat(a.docs.length," matching tags")),a.empty){console.log("No tag found with this slug"),g("تاگی نەدۆزراوەتەوە"),m([]),u(!1);return}let r=a.docs[0].data();g(r.name),console.log("Tag found:",{id:a.docs[0].id,name:r.name,slug:r.slug});let o=(0,l.rJ)(n.db,"posts");try{let e=(0,l.P)(o,(0,l._M)("tags","array-contains",r.name),(0,l.AB)(39)),t=await (0,l.GG)(e);if(console.log("Found ".concat(t.docs.length," posts with tag match")),!t.empty){f(t.docs[t.docs.length-1]);let e=t.docs.map(e=>({id:e.id,...e.data()})).filter(e=>"بڵاوکراوەتەوە"===e.status),s=e.slice(0,13);j(e.length>13),m(s),u(!1);return}}catch(e){console.error("Error with array-contains query:",e)}console.log("Falling back to client-side filtering");let i=(0,l.P)(o,(0,l.My)("date","desc"),(0,l.AB)(39)),c=await (0,l.GG)(i);if(console.log("Found ".concat(c.docs.length," published posts total")),c.empty){j(!1),u(!1);return}f(c.docs[c.docs.length-1]);let d=c.docs.map(e=>({id:e.id,...e.data()})).filter(e=>"بڵاوکراوەتەوە"===e.status&&e.tags&&Array.isArray(e.tags)&&e.tags.includes(t));console.log("Filtered to ".concat(d.length," posts for tag ").concat(r.name));let h=d.slice(0,13);j(d.length>13),m(h)}catch(e){console.error("Error fetching tag posts:",e)}finally{u(!1)}}t&&e()},[t]);let b=async()=>{if(p&&w&&!v){y(!0);try{let e=(0,l.rJ)(n.db,"posts");try{let a=(0,l.rJ)(n.db,"tags"),r=(0,l.P)(a,(0,l._M)("slug","==",t)),o=await (0,l.GG)(r);if(o.empty)return void j(!1);let i=o.docs[0].data(),c=(0,l.P)(e,(0,l._M)("tags","array-contains",i.name),(0,l.AB)(39)),d=await (0,l.GG)(c);if(console.log("Loaded ".concat(d.docs.length," more posts with tag match")),!d.empty){f(d.docs[d.docs.length-1]);let e=d.docs.map(e=>({id:e.id,...e.data()})).filter(e=>"بڵاوکراوەتەوە"===e.status).filter(e=>!s.some(t=>t.id===e.id)),t=e.slice(0,13);j(e.length>13),m(e=>[...e,...t]),y(!1);return}}catch(e){console.error("Error with array-contains query:",e)}let a=(0,l.P)(e,(0,l.My)("date","desc"),(0,l.HM)(p),(0,l.AB)(39)),r=await (0,l.GG)(a);if(console.log("Loaded ".concat(r.docs.length," more posts total")),r.empty){j(!1),y(!1);return}f(r.docs[r.docs.length-1]);let o=r.docs.map(e=>({id:e.id,...e.data()})).filter(e=>"بڵاوکراوەتەوە"===e.status&&e.tags&&Array.isArray(e.tags)&&e.tags.includes(t));console.log("Filtered to ".concat(o.length," more posts for this tag"));let i=o.slice(0,13);j(i.length>=13),m(e=>[...e,...i])}catch(e){console.error("Error loading more posts:",e)}finally{y(!1)}}};return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,a.jsx)(i.default,{}),(0,a.jsx)("main",{className:"flex-grow",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-black mb-4 relative pr-6 inline-block after:content-[''] after:absolute after:top-0 after:bottom-0 after:right-0 after:w-2 after:bg-[#8f5826] after:rounded-full",children:h}),(0,a.jsx)("div",{className:"h-1 bg-[#8f5826] w-full my-6 mb-8 rounded-full opacity-20"}),x?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin"})}):0===s.length?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("p",{className:"text-xl text-gray-500",children:"هیچ بابەتێک نەدۆزرایەوە بۆ ئەم تاگە"})}):(0,a.jsxs)(a.Fragment,{children:[s.length>0&&(0,a.jsxs)(a.Fragment,{children:[console.log("Featured post in tag page:",s[0]),(0,a.jsx)(d.A,{post:s[0]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:s.slice(1).map(e=>(0,a.jsx)(o.A,{id:e.id,title:e.title,thumbnailImage:e.thumbnailImage,date:e.date,category:Array.isArray(e.category)?e.category[0]:e.category,viewCount:e.viewCount},e.id))}),w&&(0,a.jsx)("div",{className:"mt-12 flex justify-center",children:(0,a.jsx)("button",{onClick:b,disabled:v,className:"px-6 py-3 rounded-md bg-[var(--primary-600)] text-white hover:bg-[var(--primary-700)] transition-colors ".concat(v?"opacity-70 cursor-not-allowed":""),type:"button",children:v?(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"چاوەڕوانبە..."]}):"بینینی زیاتر"})})]})]})}),(0,a.jsx)(c.default,{})]})}},9788:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(5155);function r(e){let{className:t="",style:s={}}=e;return(0,a.jsx)("div",{className:"flex items-center justify-center bg-gradient-to-br from-[#f9f5f0] to-[#e6d7c3] ".concat(t),style:{width:"100%",height:"100%",position:"absolute",top:0,left:0,...s},children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-[#8f5826] font-bold text-2xl md:text-3xl lg:text-4xl",children:"Ramiyari.com        "}),(0,a.jsx)("div",{className:"text-[#8f5826] opacity-70 text-sm md:text-base mt-2",children:"ئازادی لە دەبرینی نارەزایی        "})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[301,992,118,288,244,766,78,441,684,358],()=>t(4636)),_N_E=e.O()}]);