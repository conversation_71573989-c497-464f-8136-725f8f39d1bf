// Script to generate 50 fake posts and add them to Firebase
const { initializeApp } = require('firebase/app');
const { getFirestore, collection, addDoc, Timestamp } = require('firebase/firestore');
const { getStorage, ref, uploadString, getDownloadURL } = require('firebase/storage');

// Initialize Firebase (make sure to use your own config)
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const storage = getStorage(app);

// Sample Kurdish titles
const titles = [
  "چۆن دەتوانین ژینگە بپارێزین؟",
  "گەشتێک بۆ شاخەکانی کوردستان",
  "خواردنە نەریتییەکانی کوردستان",
  "مێژووی کورد لە سەدەی بیستەم",
  "هونەری خۆشنووسی کوردی",
  "گۆرانی و مۆسیقای کوردی",
  "ئەدەبیاتی کوردی لە سەردەمی نوێدا",
  "جلوبەرگی نەریتی کوردی",
  "فێستیڤاڵە کولتوورییەکانی کوردستان",
  "زمانی کوردی و دیالێکتەکانی",
  "داهاتووی تەکنەلۆژیا لە کوردستان",
  "وەرزشە نەریتییەکانی کوردستان",
  "کاریگەری ئینتەرنێت لەسەر کۆمەڵگای کوردی",
  "پەروەردە و فێرکردن لە کوردستان",
  "گەشتیاری لە کوردستان",
  "کشتوکاڵ و بەرهەمە خۆماڵییەکان",
  "هونەری شێوەکاری کوردی",
  "ئابووری کوردستان لە سەدەی بیست و یەک",
  "ژنان لە کۆمەڵگای کوردیدا",
  "میدیای کوردی لە سەردەمی دیجیتاڵ",
  "فۆلکلۆری کوردی و چیرۆکە نەریتییەکان",
  "پیشەسازی لە کوردستان",
  "تەندروستی و چاودێری پزیشکی",
  "سروشتی کوردستان و جوانییەکانی",
  "شیعری کوردی لە سەردەمی نوێدا",
  "ئاینەکان لە کوردستان",
  "گەنجان و داهاتووی کوردستان",
  "کەش و هەوای کوردستان",
  "شارەکانی کوردستان و تایبەتمەندییەکانیان",
  "پەیوەندییە کۆمەڵایەتییەکان لە کوردستان",
  "کاریگەری کۆرۆنا لەسەر کۆمەڵگای کوردی",
  "وەبەرهێنان لە کوردستان",
  "گەشەی شارنشینی لە کوردستان",
  "کێشە ژینگەییەکانی کوردستان",
  "پەیوەندی نێوان نەوەکان لە کوردستان",
  "کاریگەری میدیای کۆمەڵایەتی لەسەر کولتووری کوردی",
  "گەشتێک بۆ گوندەکانی کوردستان",
  "خۆراکی تەندروست لە کوردستان",
  "هونەری فیلمسازی کوردی",
  "مۆسیقای مۆدێرنی کوردی",
  "پەیوەندی نێوان کورد و نەتەوەکانی دیکە",
  "داب و نەریتەکانی هاوسەرگیری لە کوردستان",
  "ئەدەبیاتی منداڵان بە زمانی کوردی",
  "کاریگەری گۆڕانکارییە کەشوهەواییەکان لەسەر کوردستان",
  "تەکنەلۆژیای زانیاری لە کوردستان",
  "پەیوەندی نێوان هونەر و کۆمەڵگا لە کوردستان",
  "گەشەی ئابووری لە ناوچە گوندنشینەکان",
  "کاریگەری جیهانگیری لەسەر کولتووری کوردی",
  "پاراستنی میراتی کولتووری کوردستان",
  "داهێنان و نوێگەری لە کۆمەڵگای کوردیدا"
];

// Sample categories
const categories = ["کولتوور", "مێژوو", "تەکنەلۆژیا", "ژینگە", "هونەر", "وەرزش", "خۆراک", "گەشتیاری", "پەروەردە", "ئابووری"];

// Sample tags
const tags = ["کوردستان", "کولتوور", "مێژوو", "تەکنەلۆژیا", "ژینگە", "هونەر", "وەرزش", "خۆراک", "گەشتیاری", "پەروەردە",
              "ئابووری", "کۆمەڵگا", "زمان", "ئەدەبیات", "میدیا", "گەنجان", "ژنان", "سروشت", "شار", "گوند"];

// Sample content (Kurdish Lorem Ipsum)
const loremIpsum = `
<p>لۆرێم ئیپسوم دۆلۆر سیت ئامێت، کۆنسێکتێتور ئادیپیسینگ ئێلیت، سێد دۆ ئێیوسمۆد تێمپۆر ئینسیدیدونت ئوت لابۆرێ ئێت دۆلۆرێ ماگنا ئالیکوا. ئوت ئێنیم ئاد مینیم ڤێنیام، کویس نۆسترود ئێکسێرسیتاتیۆن ئوللامکۆ لابۆریس نیسی ئوت ئالیکویپ ئێکس ئێئا کۆممۆدۆ کۆنسێکوات. دویس ئاوتێ ئیرورێ دۆلۆر ئین رێپرێهێندێریت ئین ڤۆلوپتاتێ ڤێلیت ئێسسێ سیللوم دۆلۆرێ ئێو فوگیات نوللا پاریاتور. ئێکسسێپتێور سینت ئۆککائێکات کوپیداتات نۆن پرۆیدێنت، سونت ئین کولپا کوی ئۆففیسیا دێسێرونت مۆللیت ئانیم ئید ئێست لابۆروم.</p>

<p>سێد ئوت پێرسپیسیاتیس ئوندێ ئۆمنیس ئیستێ ناتوس ئێررۆر سیت ڤۆلوپتاتێم ئاککوسانتیوم دۆلۆرێمکوێ لاودانتیوم، تۆتام رێم ئاپێریام، ئێئاکوێ ئیپسا کوائێ ئاب ئیللۆ ئینڤێنتۆرێ ڤێریتاتیس ئێت کواسی ئارکیتێکتۆ بێئاتائێ ڤیتائێ دیکتا سونت ئێکسپلیکابۆ. نێمۆ ئێنیم ئیپسام ڤۆلوپتاتێم کویا ڤۆلوپتاس سیت ئاسپێرناتور ئاوت ئۆدیت ئاوت فوگیت، سێد کویا کۆنسێکوونتور ماگنی دۆلۆرێس ئێۆس کوی راتیۆنێ ڤۆلوپتاتێم سێکوی نێسسیونت.</p>

<p>نێکوێ پۆررۆ کویسکوام ئێست، کوی دۆلۆرێم ئیپسوم کویا دۆلۆر سیت ئامێت، کۆنسێکتێتور، ئادیپیسکی ڤێلیت، سێد کویا نۆن نومکوام ئێیوس مۆدی تێمپۆرا ئینسیدونت ئوت لابۆرێ ئێت دۆلۆرێ ماگنام ئالیکوام کوائێرات ڤۆلوپتاتێم. ئوت ئێنیم ئاد مینیما ڤێنیام، کویس نۆستروم ئێکسێرسیتاتیۆنێم ئوللام کۆرپۆریس سوسسیپیت لابۆریۆسام، نیسی ئوت ئالیکوید ئێکس ئێئا کۆممۆدی کۆنسێکواتور؟ کویس ئاوتێم ڤێل ئێوم ئیورێ رێپرێهێندێریت کوی ئین ئێئا ڤۆلوپتاتێ ڤێلیت ئێسسێ کوام نیهیل مۆلێستیائێ کۆنسێکواتور، ڤێل ئیللوم کوی دۆلۆرێم ئێوم فوگیات کوۆ ڤۆلوپتاس نوللا پاریاتور؟</p>

<p>ئات ڤێرۆ ئێۆس ئێت ئاککوساموس ئێت ئیوستۆ ئۆدیۆ دیگنیسسیمۆس دوسیموس کوی بلاندیتییس پرائێسێنتیوم ڤۆلوپتاتوم دێلێنیتی ئاتکوێ کۆرروپتی کوۆس دۆلۆرێس ئێت کواس مۆلێستیاس ئێکسسێپتوری سینت ئۆککائێکاتی کوپیدیتات نۆن پرۆڤیدێنت، سیمیلیکوێ سونت ئین کولپا کوی ئۆففیسیا دێسێرونت مۆللیتیا ئانیمی، ئید ئێست لابۆروم ئێت دۆلۆروم فوگا. ئێت هاروم کویدێم رێروم فاسیلیس ئێست ئێت ئێکسپێدیتا دیستینکتیۆ. نام لیبێرۆ تێمپۆرێ، کوم سۆلوتا نۆبیس ئێست ئێلیگێندی ئۆپتیۆ کومکوێ نیهیل ئیمپێدیت کوۆ مینوس ئید کوۆد ماکسیمێ پلاسێئات فاسێرێ پۆسسیموس، ئۆمنیس ڤۆلوپتاس ئاسسومێندا ئێست، ئۆمنیس دۆلۆر رێپێللێندوس.</p>

<p>تێمپۆریبوس ئاوتێم کویبوسدام ئێت ئاوت ئۆففیسییس دێبیتیس ئاوت رێروم نێسێسسیتاتیبوس سائێپێ ئێڤێنیێت ئوت ئێت ڤۆلوپتاتێس رێپودیاندائێ سینت ئێت مۆلێستیائێ نۆن رێکوساندائێ. ئیتاکوێ ئێئاروم رێروم هیس تێنێتور ئا ساپیێنتێ دێلێکتوس، ئوت ئاوت رێیسیێندیس ڤۆلوپتاتیبوس مایۆرێس ئالیاس کۆنسێکواتور ئاوت پێرفێرێندیس دۆلۆریبوس ئاسپێریۆرێس رێپێللات.</p>
`;

// Sample image URLs (placeholder images)
const imageUrls = [
  "https://via.placeholder.com/800x600/8f5826/ffffff?text=Kurdish+Blog+Post+1",
  "https://via.placeholder.com/800x600/8f5826/ffffff?text=Kurdish+Blog+Post+2",
  "https://via.placeholder.com/800x600/8f5826/ffffff?text=Kurdish+Blog+Post+3",
  "https://via.placeholder.com/800x600/8f5826/ffffff?text=Kurdish+Blog+Post+4",
  "https://via.placeholder.com/800x600/8f5826/ffffff?text=Kurdish+Blog+Post+5",
  "https://via.placeholder.com/800x600/8f5826/ffffff?text=Kurdish+Blog+Post+6",
  "https://via.placeholder.com/800x600/8f5826/ffffff?text=Kurdish+Blog+Post+7",
  "https://via.placeholder.com/800x600/8f5826/ffffff?text=Kurdish+Blog+Post+8",
  "https://via.placeholder.com/800x600/8f5826/ffffff?text=Kurdish+Blog+Post+9",
  "https://via.placeholder.com/800x600/8f5826/ffffff?text=Kurdish+Blog+Post+10"
];

// Function to get random items from an array
function getRandomItems(array, count) {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

// Function to get a random date within the last year
function getRandomDate() {
  const now = new Date();
  const pastYear = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
  const randomTime = pastYear.getTime() + Math.random() * (now.getTime() - pastYear.getTime());
  return new Date(randomTime);
}

// Function to get random view count
function getRandomViewCount() {
  return Math.floor(Math.random() * 1000) + 50;
}

// Function to create a fake post
async function createFakePost(index) {
  try {
    const title = titles[index % titles.length];
    const category = categories[Math.floor(Math.random() * categories.length)];
    const postTags = getRandomItems(tags, Math.floor(Math.random() * 5) + 1);
    const imageUrl = imageUrls[Math.floor(Math.random() * imageUrls.length)];
    const date = Timestamp.fromDate(getRandomDate());
    const viewCount = getRandomViewCount();
    const isPrimary = index < 5; // Make the first 5 posts primary

    // Create post document
    const postData = {
      title,
      content: loremIpsum,
      thumbnailImage: imageUrl,
      featuredImage: imageUrl,
      category,
      tags: postTags,
      date,
      viewCount,
      status: 'بڵاوکراوەتەوە',
      isPrimary
    };

    const docRef = await addDoc(collection(db, 'posts'), postData);
    console.log(`Post ${index + 1} created with ID: ${docRef.id}`);
    return docRef.id;
  } catch (error) {
    console.error(`Error creating post ${index + 1}:`, error);
    return null;
  }
}

// Main function to create 50 fake posts
async function createFakePosts() {
  console.log('Starting to create 50 fake posts...');

  const promises = [];
  for (let i = 0; i < 50; i++) {
    promises.push(createFakePost(i));
  }

  try {
    await Promise.all(promises);
    console.log('All 50 fake posts created successfully!');
  } catch (error) {
    console.error('Error creating fake posts:', error);
  }
}

// Run the script
createFakePosts();
