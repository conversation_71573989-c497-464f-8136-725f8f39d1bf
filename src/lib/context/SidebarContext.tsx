'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface SidebarContextType {
  isOpen: boolean;
  toggle: () => void;
  isInitialized: boolean;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

// Key for localStorage
const SIDEBAR_STATE_KEY = 'dashboard_sidebar_state';

// Function to safely get the initial state from localStorage
const getInitialState = (): boolean => {
  if (typeof window === 'undefined') {
    return true; // Default for server-side rendering
  }

  try {
    const savedState = localStorage.getItem(SIDEBAR_STATE_KEY);
    if (savedState !== null) {
      return JSON.parse(savedState);
    }
  } catch (error) {
    console.error('Error loading sidebar state from localStorage:', error);
  }

  return true; // Default if no saved state or error
};

export function SidebarProvider({ children }: { children: ReactNode }) {
  // Use null as initial state to indicate "not loaded yet"
  const [state, setState] = useState<{ isOpen: boolean; isInitialized: boolean }>({
    isOpen: getInitialState(),
    isInitialized: false
  });

  // Set initialized flag after hydration
  useEffect(() => {
    setState(prevState => ({
      ...prevState,
      isInitialized: true
    }));
  }, []);

  const toggle = () => {
    setState(prevState => {
      const newIsOpen = !prevState.isOpen;

      // Save to localStorage
      try {
        localStorage.setItem(SIDEBAR_STATE_KEY, JSON.stringify(newIsOpen));
      } catch (error) {
        console.error('Error saving sidebar state to localStorage:', error);
      }

      return {
        ...prevState,
        isOpen: newIsOpen
      };
    });
  };

  return (
    <SidebarContext.Provider value={{
      isOpen: state.isOpen,
      isInitialized: state.isInitialized,
      toggle
    }}>
      {children}
    </SidebarContext.Provider>
  );
}

export function useSidebar() {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
}
