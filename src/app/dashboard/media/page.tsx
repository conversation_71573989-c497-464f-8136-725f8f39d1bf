'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useAuth } from '@/lib/firebase/auth-context';
import DashboardLayout from '@/components/DashboardLayout';
import { listMediaFiles, deleteMediaFile, MediaFile } from '@/lib/firebase/media';
import LoadingOverlay from '@/components/LoadingOverlay';

export default function MediaPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<MediaFile | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFolder, setActiveFolder] = useState<string | null>(null);
  const [previewImage, setPreviewImage] = useState<MediaFile | null>(null);

  // Fetch media files
  useEffect(() => {
    async function fetchMediaFiles() {
      try {
        setIsLoading(true);
        const files = await listMediaFiles();
        setMediaFiles(files);
      } catch (err) {
        console.error('Error fetching media files:', err);
        setError('هەڵە لە هێنانی فایلەکان');
      } finally {
        setIsLoading(false);
      }
    }

    if (user) {
      fetchMediaFiles();
    }
  }, [user]);

  // Redirect if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  // Handle file deletion
  const handleDeleteFile = async () => {
    if (!selectedFile) return;
    
    try {
      setIsDeleting(true);
      await deleteMediaFile(selectedFile.fullPath);
      
      // Remove the file from the state
      setMediaFiles(prev => prev.filter(file => file.fullPath !== selectedFile.fullPath));
      setShowDeleteConfirm(false);
      setSelectedFile(null);
    } catch (err) {
      console.error('Error deleting file:', err);
      setError('هەڵە لە سڕینەوەی فایل');
    } finally {
      setIsDeleting(false);
    }
  };

  // Filter files based on search term and active folder
  const filteredFiles = mediaFiles.filter(file => {
    const matchesSearch = searchTerm === '' || 
      file.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFolder = activeFolder === null || file.folder === activeFolder;
    
    return matchesSearch && matchesFolder;
  });

  // Get unique folders
  const folders = Array.from(new Set(mediaFiles.map(file => file.folder)));

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };

  // Format date
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading || isLoading) {
    return (
      <DashboardLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin mb-4"></div>
          <p className="text-xl mr-3">بارکردنی میدیا...</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      {/* Loading overlay for delete operation */}
      <LoadingOverlay isLoading={isDeleting} message="سڕینەوەی فایل..." />
      
      {/* Image preview modal */}
      {previewImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
            <div className="p-4 border-b flex justify-between items-center">
              <h3 className="text-lg font-semibold">{previewImage.name}</h3>
              <button 
                onClick={() => setPreviewImage(null)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="flex-1 overflow-auto p-4 flex items-center justify-center bg-gray-100">
              <div className="relative">
                <img 
                  src={previewImage.url} 
                  alt={previewImage.name}
                  className="max-w-full max-h-[70vh] object-contain"
                />
              </div>
            </div>
            <div className="p-4 border-t bg-gray-50">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p><span className="font-semibold">فۆڵدەر:</span> {previewImage.folder}</p>
                  <p><span className="font-semibold">قەبارە:</span> {formatFileSize(previewImage.size)}</p>
                </div>
                <div>
                  <p><span className="font-semibold">جۆر:</span> {previewImage.contentType}</p>
                  <p><span className="font-semibold">بەروار:</span> {formatDate(previewImage.timeCreated)}</p>
                </div>
                <div className="md:col-span-2">
                  <p className="font-semibold">لینک:</p>
                  <input 
                    type="text" 
                    value={previewImage.url} 
                    readOnly 
                    className="w-full p-2 border rounded text-xs bg-white"
                    onClick={(e) => (e.target as HTMLInputElement).select()}
                  />
                </div>
              </div>
              <div className="mt-4 flex justify-end">
                <button
                  onClick={() => {
                    setSelectedFile(previewImage);
                    setShowDeleteConfirm(true);
                    setPreviewImage(null);
                  }}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                >
                  سڕینەوە
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Delete confirmation modal */}
      {showDeleteConfirm && selectedFile && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
            <h3 className="text-xl font-bold mb-4">دڵنیای لە سڕینەوە؟</h3>
            <p className="mb-6">
              ئایا دڵنیای لە سڕینەوەی ئەم فایلە؟ ئەم کردارە ناگەڕێتەوە.
              <br />
              <span className="font-semibold">{selectedFile.name}</span>
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setSelectedFile(null);
                }}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors ml-4"
              >
                پاشگەزبوونەوە
              </button>
              <button
                onClick={handleDeleteFile}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                سڕینەوە
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-black">بەڕێوەبردنی میدیا</h1>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md">
            {error}
          </div>
        )}

        <div className="bg-[var(--card-bg)] rounded-lg shadow-md border border-[var(--card-border)] p-6">
          {/* Search and filter */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <input
                  type="text"
                  placeholder="گەڕان بە ناوی فایل..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full py-2 px-3 pl-10 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)]"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </div>
            <div>
              <select
                value={activeFolder || ''}
                onChange={(e) => setActiveFolder(e.target.value || null)}
                className="w-full md:w-48 py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)]"
              >
                <option value="">هەموو فۆڵدەرەکان</option>
                {folders.map(folder => (
                  <option key={folder} value={folder}>{folder}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Media grid */}
          {filteredFiles.length > 0 ? (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {filteredFiles.map((file) => (
                <div 
                  key={file.fullPath} 
                  className="border border-[var(--gray-200)] rounded-lg overflow-hidden bg-white hover:shadow-md transition-shadow"
                >
                  <div 
                    className="relative h-32 bg-gray-100 cursor-pointer"
                    onClick={() => setPreviewImage(file)}
                  >
                    {file.contentType.startsWith('image/') ? (
                      <Image
                        src={file.url}
                        alt={file.name}
                        fill
                        style={{ objectFit: 'cover' }}
                        sizes="(max-width: 768px) 50vw, 33vw"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                    )}
                    <div className="absolute top-2 right-2 bg-gray-100 text-xs px-2 py-1 rounded-full">
                      {formatFileSize(file.size)}
                    </div>
                  </div>
                  <div className="p-2">
                    <p className="text-sm font-medium truncate" title={file.name}>
                      {file.name}
                    </p>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
                        {file.folder}
                      </span>
                      <button
                        onClick={() => {
                          setSelectedFile(file);
                          setShowDeleteConfirm(true);
                        }}
                        className="text-red-500 hover:text-red-700"
                        title="سڕینەوە"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              {searchTerm || activeFolder ? (
                <p className="text-gray-500">هیچ فایلێک نەدۆزرایەوە. تکایە هەوڵی گەڕانێکی تر بدە.</p>
              ) : (
                <p className="text-gray-500">هیچ فایلێک نییە.</p>
              )}
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
