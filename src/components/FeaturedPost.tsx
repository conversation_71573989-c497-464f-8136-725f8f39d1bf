'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Timestamp } from 'firebase/firestore';
import styles from './FeaturedPost.module.css';
import RamiyariPlaceholder from './RamiyariPlaceholder';

interface FeaturedPostProps {
  post: {
    id: string;
    title: string;
    content?: string;
    thumbnailImage: string;
    date: Timestamp;
    viewCount: number;
    category?: string | string[];
    tags?: string[];
  };
}

export default function FeaturedPost({ post }: FeaturedPostProps) {
  console.log('FeaturedPost rendering with post:', post);
  console.log('FeaturedPost image URL:', post.thumbnailImage);

  // State to track image loading errors
  const [imageError, setImageError] = useState(false);

  // Check if the image URL is valid (starts with http or https)
  const isValidImageUrl = post.thumbnailImage &&
    (post.thumbnailImage.startsWith('http://') || post.thumbnailImage.startsWith('https://'));

  // Check if the URL is from Firebase Storage (which might be returning 404)
  const isFirebaseStorageUrl = isValidImageUrl && post.thumbnailImage.includes('firebasestorage.googleapis.com');

  // Check if we have a valid image to display
  const hasValidImage = isValidImageUrl && !imageError;

  // Function to handle image loading errors
  const handleImageError = () => {
    console.error('Error loading image:', post.thumbnailImage);
    setImageError(true);
  };

  return (
    <div className={styles.featuredPost}>
      <div className={styles.imageContainer}>
        <Link href={`/post/${post.id}`} style={{ display: 'block', height: '100%', position: 'relative' }}>
          {hasValidImage ? (
            <Image
              key={post.thumbnailImage}
              src={post.thumbnailImage}
              alt={post.title}
              fill
              sizes="(max-width: 768px) 100vw, 40vw"
              className="object-cover"
              loading="eager"
              priority
              quality={90}
              onError={handleImageError}
              unoptimized={isFirebaseStorageUrl ? true : undefined} // Skip Next.js image optimization for Firebase Storage URLs
            />
          ) : (
            <RamiyariPlaceholder />
          )}
        </Link>
      </div>
      <div className={styles.contentContainer}>
        <h2 className={styles.title}>{post.title}</h2>
        <div className={styles.meta}>
          <span className={styles.metaItem}>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            {post.date ? new Date(post.date.toDate()).toLocaleDateString('ku-IQ') : ''}
          </span>
          <span className={styles.metaItem}>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            {post.viewCount || 0} بینین
          </span>
        </div>
        {post.content && (
          <div className={styles.excerpt}>
            <div dangerouslySetInnerHTML={{
              __html: post.content
                .replace(/<[^>]*>/g, '') // Remove HTML tags
                .substring(0, 150) + '...' // Limit to 150 chars
            }} />
          </div>
        )}
        <Link href={`/post/${post.id}`} className={styles.readMore}>
          خوێندنەوەی زیاتر
        </Link>
      </div>
    </div>
  );
}
