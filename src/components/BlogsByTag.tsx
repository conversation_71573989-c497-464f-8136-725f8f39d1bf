'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { collection, query, getDocs, limit, orderBy, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import styles from './BlogsByTag.module.css';
import RamiyariPlaceholder from './RamiyariPlaceholder';

interface Post {
  id: string;
  title: string;
  thumbnailImage: string;
  category?: string;
  date: Timestamp;
  viewCount: number;
  tags?: string[];
  status: string;
}

interface Tag {
  id: string;
  name: string;
  slug: string;
  posts: Post[];
}

export default function BlogsByTag() {
  const [tags, setTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchTagsWithPosts() {
      try {
        setIsLoading(true);

        // Fetch tags first
        const tagsCollection = collection(db, 'tags');
        const tagsSnapshot = await getDocs(tagsCollection);

        if (tagsSnapshot.empty) {
          setTags([]);
          return;
        }

        // Get all tags
        const tagsList = tagsSnapshot.docs.map(doc => ({
          id: doc.id,
          name: doc.data().name || doc.id,
          slug: doc.data().slug || doc.id,
          posts: [] as Post[]
        }));

        // Fetch categories to map slugs to names
        const categoriesCollection = collection(db, 'categories');
        const categoriesSnapshot = await getDocs(categoriesCollection);
        const categoriesMap = new Map();
        categoriesSnapshot.docs.forEach(doc => {
          const data = doc.data();
          categoriesMap.set(data.slug, data.name);
        });

        // Fetch posts - use a simpler query that doesn't require a composite index
        const postsCollection = collection(db, 'posts');
        const postsQuery = query(
          postsCollection,
          orderBy('date', 'desc'),
          limit(100) // Limit to avoid processing too many posts
        );

        const postsSnapshot = await getDocs(postsQuery);

        if (postsSnapshot.empty) {
          setTags(tagsList);
          return;
        }

        // Process all posts and filter for published posts in JavaScript
        const posts = postsSnapshot.docs
          .map(doc => {
            const data = doc.data();
            // Replace category slug with category name if available
            if (data.category && categoriesMap.has(data.category)) {
              data.category = categoriesMap.get(data.category);
            }
            return {
              id: doc.id,
              ...data
            } as Post;
          })
          .filter(post => post.status === 'بڵاوکراوەتەوە');

        // Assign posts to tags
        const tagsWithPosts = tagsList.map(tag => {
          const tagPosts = posts.filter(post =>
            post.tags && Array.isArray(post.tags) && post.tags.includes(tag.name)
          ).slice(0, 8); // Limit to 8 posts per tag

          return {
            ...tag,
            posts: tagPosts
          };
        });

        // Filter out tags with no posts
        const filteredTags = tagsWithPosts.filter(tag => tag.posts.length > 0);

        // Sort tags by number of posts (most posts first)
        filteredTags.sort((a, b) => b.posts.length - a.posts.length);

        // Limit to top 5 tags
        setTags(filteredTags.slice(0, 5));
      } catch (err) {
        console.error('Error fetching tags with posts:', err);
        setError('هەڵە لە هێنانی بابەتەکان بەپێی تاگ');
        setTags([]); // Reset tags on error
      } finally {
        setIsLoading(false);
      }
    }

    fetchTagsWithPosts();
  }, []);

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-center items-center h-40">
          <div className="inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  if (error) {
    // Show a user-friendly error message
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-8">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="mr-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (tags.length === 0) {
    return null; // Don't show the section if there are no tags with posts
  }

  return (
    <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {tags.map((tag, index) => (
        <div key={tag.id} className={`mb-10 ${index > 0 ? 'mt-6' : ''} p-4 relative`}>
          {/* Top horizontal line with gaps for corners */}
          <div className="absolute top-0 left-[8px] right-[8px] h-[2px] bg-[#dbb995]"></div>

          {/* Right vertical line with gap for corner */}
          <div className="absolute top-[8px] bottom-[calc(100%-180px)] right-0 w-[2px] bg-[#dbb995]"></div>

          {/* Corner decoration - only for the right corner */}
          <div className="absolute top-0 right-0 w-[8px] h-[8px] border-t-[2px] border-r-[2px] border-[#dbb995] rounded-tr-lg"></div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-black relative pr-6 after:content-[''] after:absolute after:top-0 after:bottom-0 after:right-0 after:w-2 after:bg-[#8f5826] after:rounded-full">{tag.name}</h2>
            <Link
              href={`/tag/${tag.slug}`}
              className="text-[#8f5826] hover:text-[#734620] transition-colors font-semibold px-4 py-2 border-2 border-[#8f5826] rounded-md hover:bg-[#f9f5f0]"
            >
              بینینی زیاتر
            </Link>
          </div>

          <div className={styles.scrollContainer}>
            <div className={styles.scrollRow}>
              {tag.posts.slice(0, 8).map((post) => (
                <div key={post.id} className={`${styles.postCard} ${styles.postCardScroll}`}>
                  <Link href={`/post/${post.id}`}>
                    <div className="relative h-48">
                      {post.thumbnailImage ? (
                        <Image
                          src={post.thumbnailImage}
                          alt={post.title}
                          fill
                          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                          className="object-cover"
                          loading="lazy"
                          quality={75}
                        />
                      ) : (
                        <RamiyariPlaceholder />
                      )}
                    </div>
                    <div className="p-4">
                      {post.category && (
                        <span className="inline-block bg-[var(--primary-100)] text-[var(--primary-700)] px-2 py-1 text-xs font-semibold rounded-md mb-2">
                          {post.category}
                        </span>
                      )}
                      <h3 className="text-lg font-bold text-black mb-2 line-clamp-2">{post.title}</h3>
                      <div className="flex items-center text-gray-500 text-sm">
                        <span className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          {post.date ? new Date(post.date.toDate()).toLocaleDateString('ku-IQ') : ''}
                        </span>
                        <span className="mx-2">•</span>
                        <span className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          {post.viewCount || 0} بینین
                        </span>
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </div>
      ))}
    </section>
  );
}
