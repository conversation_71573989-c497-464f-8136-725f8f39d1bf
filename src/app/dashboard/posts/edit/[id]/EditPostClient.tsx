'use client';

import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useAuth } from '@/lib/firebase/auth-context';
import DashboardLayout from '@/components/DashboardLayout';
import JoditEditor from '@/components/JoditEditor';
import { doc, getDoc, updateDoc, Timestamp, collection, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import {
  generateUniqueFilename,
  uploadFeaturedImage,
  uploadThumbnailImage,
  deleteFileFromStorage
} from '@/lib/firebase/storage';

// CSS for toggle switch
import './toggle.css';

// Define category and tag types
interface Category {
  id: string;
  name: string;
  slug: string;
}

interface Tag {
  id: string;
  name: string;
  slug: string;
}

interface Post {
  id: string;
  title: string;
  content: string;
  category?: string;
  tags?: string[];
  status: string;
  isPrimary?: boolean;
  featuredImage?: string | null;
  thumbnailImage?: string | null;
  viewCount?: number;
  date: Timestamp;
  author: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

interface EditPostClientProps {
  id: string;
}

export default function EditPostClient({ id }: EditPostClientProps) {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [post, setPost] = useState<Post | null>(null);
  const [title, setTitle] = useState('');
  const [editorContent, setEditorContent] = useState('');
  // No need for editor state with Jodit
  const [category, setCategory] = useState('');
  const [tags, setTags] = useState('');
  const [status, setStatus] = useState('draft');
  const [isPrimary, setIsPrimary] = useState(false);
  const [featuredImage, setFeaturedImage] = useState<string | null>(null);
  const [thumbnailImage, setThumbnailImage] = useState<string | null>(null);
  const [originalFeaturedImage, setOriginalFeaturedImage] = useState<string | null>(null);
  const [originalThumbnailImage, setOriginalThumbnailImage] = useState<string | null>(null);
  const [viewCount, setViewCount] = useState<number>(0);
  const [publishDate, setPublishDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for categories and tags from Firebase
  const [categories, setCategories] = useState<Category[]>([]);
  const [allTags, setAllTags] = useState<Tag[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(true);
  const [isLoadingTags, setIsLoadingTags] = useState(true);

  // Fetch categories from Firebase
  useEffect(() => {
    async function fetchCategories() {
      try {
        const categoriesCollection = collection(db, 'categories');
        const categoriesSnapshot = await getDocs(categoriesCollection);
        const categoriesList = categoriesSnapshot.docs.map(doc => ({
          id: doc.id,
          name: doc.data().name,
          slug: doc.data().slug
        })) as Category[];

        setCategories(categoriesList);
      } catch (err) {
        console.error('Error fetching categories:', err);
      } finally {
        setIsLoadingCategories(false);
      }
    }

    if (user) {
      fetchCategories();
    }
  }, [user]);

  // Fetch tags from Firebase
  useEffect(() => {
    async function fetchTags() {
      try {
        const tagsCollection = collection(db, 'tags');
        const tagsSnapshot = await getDocs(tagsCollection);
        const tagsList = tagsSnapshot.docs.map(doc => ({
          id: doc.id,
          name: doc.data().name,
          slug: doc.data().slug
        })) as Tag[];

        setAllTags(tagsList);
      } catch (err) {
        console.error('Error fetching tags:', err);
      } finally {
        setIsLoadingTags(false);
      }
    }

    if (user) {
      fetchTags();
    }
  }, [user]);

  // Fetch post data
  useEffect(() => {
    async function fetchPost() {
      try {
        setIsLoading(true);
        const postRef = doc(db, 'posts', id);
        const postSnap = await getDoc(postRef);

        if (postSnap.exists()) {
          const postData = { id: postSnap.id, ...postSnap.data() } as Post;
          setPost(postData);

          // Set form fields
          setTitle(postData.title || '');
          setEditorContent(postData.content || '');
          setCategory(postData.category || '');
          setTags(postData.tags?.join(', ') || '');
          setStatus(postData.status === 'بڵاوکراوەتەوە' ? 'publish' : 'draft');
          setIsPrimary(postData.isPrimary || false);
          // Store both current and original image URLs
          setFeaturedImage(postData.featuredImage || null);
          setThumbnailImage(postData.thumbnailImage || null);
          setOriginalFeaturedImage(postData.featuredImage || null);
          setOriginalThumbnailImage(postData.thumbnailImage || null);
          setViewCount(postData.viewCount || 0);

          // Format date for input
          if (postData.date) {
            const date = postData.date.toDate();
            setPublishDate(date.toISOString().split('T')[0]);
          }
        } else {
          setError('بابەت نەدۆزرایەوە');
          setTimeout(() => {
            router.push('/dashboard/posts');
          }, 2000);
        }
      } catch (err) {
        console.error('Error fetching post:', err);
        setError('هەڵە لە هێنانی بابەت');
      } finally {
        setIsLoading(false);
      }
    }

    if (user && id) {
      fetchPost();
    }
  }, [user, id, router]);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>, setImage: React.Dispatch<React.SetStateAction<string | null>>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Removed file size limit as requested
      const reader = new FileReader();
      reader.onloadend = () => {
        setImage(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle Jodit editor changes
  const handleEditorChange = (content: string) => {
    console.log('Editor content updated');
    setEditorContent(content);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      if (!title.trim()) {
        throw new Error('تکایە ناونیشانێک بنووسە');
      }

      if (!editorContent.trim()) {
        throw new Error('تکایە ناوەڕۆکێک بنووسە');
      }

      // Check if user is authenticated
      if (!user) {
        throw new Error('تکایە دووبارە چوونەژوورەوە بکە');
      }

      // Convert tags string to array
      const tagsArray = tags.split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      // Check if images are new (base64 strings) or existing URLs
      let featuredImageUrl = featuredImage;
      let thumbnailImageUrl = thumbnailImage;

      // Handle featured image changes
      if (featuredImage !== originalFeaturedImage) {
        // If original image exists and was replaced or removed, delete it from storage
        if (originalFeaturedImage &&
            originalFeaturedImage.includes('firebasestorage.googleapis.com') &&
            featuredImage !== originalFeaturedImage) {
          try {
            // Only delete if the image was actually replaced (not just removed in UI)
            // because removal is handled by the remove button click handler
            if (featuredImage && featuredImage.startsWith('data:image')) {
              await deleteFileFromStorage(originalFeaturedImage);
              console.log('Original featured image deleted from storage');
            }
          } catch (error) {
            console.error('Error deleting original featured image:', error);
          }
        }

        // Upload new featured image if it's a base64 string (new upload)
        if (featuredImage && featuredImage.startsWith('data:image')) {
          try {
            const filename = generateUniqueFilename('featured.jpg');
            featuredImageUrl = await uploadFeaturedImage(featuredImage, filename);
            console.log('Featured image uploaded and optimized:', featuredImageUrl);
          } catch (error) {
            console.error('Error uploading featured image:', error);
            throw new Error('هەڵە لە بارکردنی وێنەی سەرەکی');
          }
        }
      }

      // Handle thumbnail image changes
      if (thumbnailImage !== originalThumbnailImage) {
        // If original image exists and was replaced or removed, delete it from storage
        if (originalThumbnailImage &&
            originalThumbnailImage.includes('firebasestorage.googleapis.com') &&
            thumbnailImage !== originalThumbnailImage) {
          try {
            // Only delete if the image was actually replaced (not just removed in UI)
            // because removal is handled by the remove button click handler
            if (thumbnailImage && thumbnailImage.startsWith('data:image')) {
              await deleteFileFromStorage(originalThumbnailImage);
              console.log('Original thumbnail image deleted from storage');
            }
          } catch (error) {
            console.error('Error deleting original thumbnail image:', error);
          }
        }

        // Upload new thumbnail image if it's a base64 string (new upload)
        if (thumbnailImage && thumbnailImage.startsWith('data:image')) {
          try {
            const filename = generateUniqueFilename('thumbnail.jpg');
            thumbnailImageUrl = await uploadThumbnailImage(thumbnailImage, filename);
            console.log('Thumbnail image uploaded and optimized:', thumbnailImageUrl);
          } catch (error) {
            console.error('Error uploading thumbnail image:', error);
            throw new Error('هەڵە لە بارکردنی وێنەی بچووک');
          }
        }
      }

      // Process the editor content to ensure image dimensions are preserved
      console.log('Saving content with dimensions:', editorContent);

      // Create post object with updated data
      const postData = {
        title,
        content: editorContent,
        category,
        tags: tagsArray,
        status: status === 'publish' ? 'بڵاوکراوەتەوە' : 'پێشنووس',
        isPrimary,
        featuredImage: featuredImageUrl,
        thumbnailImage: thumbnailImageUrl,
        viewCount,
        date: Timestamp.fromDate(new Date(publishDate)),
        updatedAt: Timestamp.now()
      };

      // Update in Firestore
      const postRef = doc(db, 'posts', id);
      await updateDoc(postRef, postData);

      alert('بابەت بە سەرکەوتوویی نوێ کرایەوە!');
      router.push('/dashboard/posts');
    } catch (err: any) {
      console.error('Error updating post:', err);
      setError(err.message || 'هەڵەیەک ڕوویدا لە نوێکردنەوەی بابەت');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading || isLoading) {
    return (
      <DashboardLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin mb-4"></div>
          <p className="text-xl mr-3">بارکردنی بابەت...</p>
        </div>
      </DashboardLayout>
    );
  }

  if (!user) {
    return null;
  }

  if (error && !post) {
    return (
      <DashboardLayout>
        <div className="max-w-5xl mx-auto">
          <div className="bg-red-50 border border-red-200 text-red-700 p-6 rounded-md">
            <h2 className="text-xl font-bold mb-2">هەڵە</h2>
            <p>{error}</p>
            <button
              type="button"
              onClick={() => router.push('/dashboard/posts')}
              className="mt-4 px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors cursor-pointer"
            >
              گەڕانەوە بۆ لیستی بابەتەکان
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-5xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-black">دەستکاریکردنی بابەت</h1>
          <button
            type="button"
            onClick={() => router.push('/dashboard/posts')}
            className="px-4 py-2 bg-[var(--gray-500)] text-white rounded-md hover:bg-[var(--gray-600)] transition-colors cursor-pointer"
          >
            گەڕانەوە
          </button>
        </div>

        <div className="bg-[var(--card-bg)] rounded-lg shadow-md border border-[var(--card-border)] p-6">
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md">
              {error}
            </div>
          )}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="title" className="block text-black text-sm font-bold mb-2">
                ناونیشان
              </label>
              <input
                type="text"
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Featured Image */}
              <div>
                <label className="block text-black text-sm font-bold mb-2">
                  وێنەی سەرەکی
                </label>
                <div className="border-2 border-dashed border-[var(--gray-300)] rounded-md p-4 text-center">
                  {featuredImage ? (
                    <div className="relative h-48 mb-2">
                      <Image
                        src={featuredImage}
                        alt="Featured"
                        fill
                        style={{ objectFit: 'cover' }}
                        className="rounded-md"
                      />
                      <button
                        type="button"
                        onClick={async () => {
                          // If this is an existing Firebase Storage image (not a new upload), delete it
                          if (featuredImage &&
                              featuredImage === originalFeaturedImage &&
                              featuredImage.includes('firebasestorage.googleapis.com')) {
                            try {
                              await deleteFileFromStorage(featuredImage);
                              console.log('Featured image deleted from storage');
                            } catch (error) {
                              console.error('Error deleting featured image:', error);
                            }
                          }
                          setFeaturedImage(null);
                        }}
                        className="absolute top-2 left-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors cursor-pointer"
                        title="سڕینەوەی وێنە"
                        aria-label="سڕینەوەی وێنە"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <div className="h-48 flex flex-col items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-[var(--gray-400)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <p className="mt-2 text-sm text-black">کرتە بکە بۆ هەڵبژاردنی وێنە</p>
                    </div>
                  )}
                  <input
                    type="file"
                    id="featuredImage"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => handleImageUpload(e, setFeaturedImage)}
                  />
                  <label
                    htmlFor="featuredImage"
                    className="mt-2 inline-block px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors cursor-pointer"
                  >
                    {featuredImage ? 'گۆڕینی وێنە' : 'هەڵبژاردنی وێنە'}
                  </label>
                </div>
              </div>

              {/* Thumbnail Image */}
              <div>
                <label className="block text-black text-sm font-bold mb-2">
                  وێنەی بچووک
                </label>
                <div className="border-2 border-dashed border-[var(--gray-300)] rounded-md p-4 text-center">
                  {thumbnailImage ? (
                    <div className="relative h-48 mb-2">
                      <Image
                        src={thumbnailImage}
                        alt="Thumbnail"
                        fill
                        style={{ objectFit: 'cover' }}
                        className="rounded-md"
                      />
                      <button
                        type="button"
                        onClick={async () => {
                          // If this is an existing Firebase Storage image (not a new upload), delete it
                          if (thumbnailImage &&
                              thumbnailImage === originalThumbnailImage &&
                              thumbnailImage.includes('firebasestorage.googleapis.com')) {
                            try {
                              await deleteFileFromStorage(thumbnailImage);
                              console.log('Thumbnail image deleted from storage');
                            } catch (error) {
                              console.error('Error deleting thumbnail image:', error);
                            }
                          }
                          setThumbnailImage(null);
                        }}
                        className="absolute top-2 left-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors cursor-pointer"
                        title="سڕینەوەی وێنە"
                        aria-label="سڕینەوەی وێنە"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <div className="h-48 flex flex-col items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-[var(--gray-400)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <p className="mt-2 text-sm text-black">کرتە بکە بۆ هەڵبژاردنی وێنە</p>
                    </div>
                  )}
                  <input
                    type="file"
                    id="thumbnailImage"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => handleImageUpload(e, setThumbnailImage)}
                  />
                  <label
                    htmlFor="thumbnailImage"
                    className="mt-2 inline-block px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors cursor-pointer"
                  >
                    {thumbnailImage ? 'گۆڕینی وێنە' : 'هەڵبژاردنی وێنە'}
                  </label>
                </div>
              </div>
            </div>

            <div>
              <label htmlFor="content" className="block text-black text-sm font-bold mb-2">
                ناوەڕۆک
              </label>
              <JoditEditor
                value={editorContent}
                onChange={handleEditorChange}
                placeholder="ناوەڕۆک بنووسە..."
                height={650}
                width="100%"
                rtl={true}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="category" className="block text-black text-sm font-bold mb-2">
                  پۆل
                </label>
                {isLoadingCategories ? (
                  <div className="w-full py-2 px-3 border border-[var(--gray-300)] rounded-md bg-gray-50 flex items-center">
                    <div className="inline-block w-4 h-4 border-2 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin ml-2"></div>
                    <span className="text-gray-500">بارکردنی پۆلەکان...</span>
                  </div>
                ) : (
                  <select
                    id="category"
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                    className="w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors cursor-pointer"
                  >
                    <option value="">هەڵبژاردنی پۆل</option>
                    {categories.map((cat) => (
                      <option key={cat.id} value={cat.slug}>
                        {cat.name}
                      </option>
                    ))}
                  </select>
                )}
              </div>

              <div>
                <label htmlFor="tags" className="block text-black text-sm font-bold mb-2">
                  تاگەکان
                </label>
                {isLoadingTags ? (
                  <div className="w-full py-2 px-3 border border-[var(--gray-300)] rounded-md bg-gray-50 flex items-center">
                    <div className="inline-block w-4 h-4 border-2 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin ml-2"></div>
                    <span className="text-gray-500">بارکردنی تاگەکان...</span>
                  </div>
                ) : (
                  <div>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {tags.split(',')
                        .map(tag => tag.trim())
                        .filter(tag => tag.length > 0)
                        .map((tag, index) => (
                          <span
                            key={index}
                            className="bg-[var(--primary-100)] text-[var(--primary-700)] px-2 py-1 rounded-md text-sm flex items-center"
                          >
                            {tag}
                            <button
                              type="button"
                              onClick={() => {
                                const tagsArray = tags.split(',').map(t => t.trim()).filter(t => t.length > 0);
                                tagsArray.splice(index, 1);
                                setTags(tagsArray.join(', '));
                              }}
                              className="ml-1 text-[var(--primary-500)] hover:text-[var(--primary-700)] transition-colors"
                              title="سڕینەوەی تاگ"
                              aria-label="سڕینەوەی تاگ"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                              </svg>
                            </button>
                          </span>
                        ))}
                    </div>
                    <div className="flex">
                      <select
                        id="tagSelector"
                        name="tagSelector"
                        aria-label="هەڵبژاردنی تاگ"
                        title="هەڵبژاردنی تاگ"
                        className="w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors cursor-pointer"
                        onChange={(e) => {
                          if (e.target.value) {
                            const selectedTag = e.target.value;
                            const currentTags = tags.split(',').map(t => t.trim()).filter(t => t.length > 0);
                            if (!currentTags.includes(selectedTag)) {
                              const newTags = [...currentTags, selectedTag].join(', ');
                              setTags(newTags);
                            }
                            e.target.value = ''; // Reset select
                          }
                        }}
                      >
                        <option value="">هەڵبژاردنی تاگ</option>
                        {allTags.map((tag) => (
                          <option key={tag.id} value={tag.name}>
                            {tag.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="publishDate" className="block text-black text-sm font-bold mb-2">
                  بەرواری بڵاوکردنەوە
                </label>
                <input
                  type="date"
                  id="publishDate"
                  value={publishDate}
                  onChange={(e) => setPublishDate(e.target.value)}
                  className="w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors cursor-pointer"
                />
              </div>

              <div>
                <label htmlFor="viewCount" className="block text-black text-sm font-bold mb-2">
                  ژمارەی بینەران
                </label>
                <input
                  type="number"
                  id="viewCount"
                  value={viewCount}
                  onChange={(e) => setViewCount(parseInt(e.target.value) || 0)}
                  min="0"
                  className="w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-black text-sm font-bold mb-2">
                  دۆخ
                </label>
                <div className="flex items-center space-x-6">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="draft"
                      name="status"
                      value="draft"
                      checked={status === 'draft'}
                      onChange={() => setStatus('draft')}
                      className="ml-2 h-4 w-4 text-[var(--primary-600)] cursor-pointer"
                    />
                    <label htmlFor="draft" className="ml-2 text-black cursor-pointer hover:text-[var(--primary-600)] transition-colors">پێشنووس</label>
                  </div>

                  <div className="flex items-center mr-6">
                    <input
                      type="radio"
                      id="publish"
                      name="status"
                      value="publish"
                      checked={status === 'publish'}
                      onChange={() => setStatus('publish')}
                      className="ml-2 h-4 w-4 text-[var(--primary-600)] cursor-pointer"
                    />
                    <label htmlFor="publish" className="ml-2 text-black cursor-pointer hover:text-[var(--primary-600)] transition-colors">بڵاوکردنەوە</label>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-black text-sm font-bold mb-2">
                  بابەتی سەرەکی
                </label>
                <div className="flex items-center">
                  <div className="relative inline-block w-10 ml-2 align-middle select-none transition duration-200 ease-in">
                    <input
                      type="checkbox"
                      name="isPrimary"
                      id="isPrimary"
                      checked={isPrimary}
                      onChange={() => setIsPrimary(!isPrimary)}
                      className="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                    />
                    <label
                      htmlFor="isPrimary"
                      className={`toggle-label block overflow-hidden h-6 rounded-full cursor-pointer ${
                        isPrimary ? 'bg-[var(--primary-600)]' : 'bg-gray-300'
                      }`}
                    ></label>
                  </div>
                  <label htmlFor="isPrimary" className="text-black cursor-pointer hover:text-[var(--primary-600)] transition-colors">
                    {isPrimary ? 'بەڵێ' : 'نەخێر'}
                  </label>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between pt-4 border-t border-[var(--gray-200)]">
              <button
                type="submit"
                disabled={isSubmitting}
                className={`px-6 py-3 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors font-medium flex items-center cursor-pointer ${isSubmitting ? 'opacity-70 cursor-not-allowed' : ''}`}
              >
                {isSubmitting ? (
                  <>
                    <span className="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"></span>
                    <span>نوێکردنەوە...</span>
                  </>
                ) : (
                  'نوێکردنەوە'
                )}
              </button>
              <button
                type="button"
                onClick={() => router.push('/dashboard/posts')}
                className="px-6 py-3 bg-[var(--gray-500)] text-white rounded-md hover:bg-[var(--gray-600)] transition-colors font-medium cursor-pointer"
              >
                پاشگەزبوونەوە
              </button>
            </div>
          </form>
        </div>
      </div>
    </DashboardLayout>
  );
}
