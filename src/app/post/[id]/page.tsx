import PostPageClient from './PostPageClient';
import { generateMetadata } from './generateMetadata';

type Params = Promise<{ id: string }>;

// Export the generateMetadata function
export { generateMetadata };

export default async function PostPage({ params }: { params: Params }) {
  // Await the params to get the id
  const { id: postId } = await params;

  return <PostPageClient id={postId} />;
}

