'use client';

import { useEffect, useRef } from 'react';

interface PostContentProps {
  content: string;
}

export default function PostContent({ content }: PostContentProps) {
  const contentRef = useRef<HTMLDivElement>(null);

  // Function to process images after the component mounts
  useEffect(() => {
    if (contentRef.current) {
      const images = contentRef.current.querySelectorAll('img');

      images.forEach((img) => {
        // Add loading="lazy" for better performance
        img.setAttribute('loading', 'lazy');

        // Skip if the image is already processed
        if (img.parentElement?.classList.contains('img-wrapper')) {
          return;
        }

        // Create a wrapper div for the image
        const wrapper = document.createElement('div');
        wrapper.className = 'img-wrapper';

        // Get the parent element
        const parent = img.parentNode;

        // If the parent is a figure, we don't need to add another wrapper
        if (parent instanceof HTMLElement && parent.tagName.toLowerCase() === 'figure') {
          // Just ensure the image has the right styles
          img.style.width = 'auto';
          img.style.maxWidth = '100%';
          img.style.height = 'auto';
        } else if (parent) {
          // Otherwise, wrap the image
          parent.insertBefore(wrapper, img);
          wrapper.appendChild(img);
        }

        // Add event listener to handle image load
        img.addEventListener('load', () => {
          // Always set width to auto to prevent horizontal stretching
          img.style.width = 'auto';
          img.style.maxWidth = '100%';
          img.style.height = 'auto';

          // Force the browser to respect the aspect ratio
          const aspectRatio = img.naturalWidth / img.naturalHeight;
          img.style.aspectRatio = `${aspectRatio}`;

          // Add loaded class to show the image with transition
          img.classList.add('loaded');
        });

        // Handle images that might already be loaded
        if (img.complete) {
          const event = new Event('load');
          img.dispatchEvent(event);
        }
      });
    }
  }, [content]); // Re-run when content changes

  return (
    <article
      ref={contentRef}
      className="prose prose-lg max-w-none blog-content"
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
}
