'use client';

import React, { useRef, useState, useMemo, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import JoditReactEditor with SSR disabled
const JoditReactEditor = dynamic(() => import('jodit-react'), {
  ssr: false,
});

interface JoditEditorProps {
  value: string;
  onChange: (content: string) => void;
  placeholder?: string;
  height?: number | string;
  width?: number | string;
  readonly?: boolean;
  toolbar?: boolean | string;
  language?: string;
  rtl?: boolean;
}

const JoditEditor: React.FC<JoditEditorProps> = ({
  value,
  onChange,
  placeholder = 'ناوەڕۆک بنووسە...',
  height = 650, // Increased by 30% from 500
  width = '100%',
  readonly = false,
  toolbar = true,
  language = 'en',
  rtl = true,
}) => {
  const editor = useRef(null);
  const [content, setContent] = useState(value || '');

  useEffect(() => {
    setContent(value);
  }, [value]);

  const config = useMemo(() => {
    return {
      readonly,
      placeholder,
      height,
      width,
      toolbar,
      language,
      direction: rtl ? 'rtl' as 'rtl' : 'ltr' as 'ltr',
      toolbarButtonSize: 'middle' as 'middle',
      buttons: [
        'source',
        '|',
        'bold',
        'italic',
        'underline',
        'strikethrough',
        '|',
        'font',
        'fontsize',
        'brush',
        'paragraph',
        '|',
        'align',
        'indent',
        'outdent',
        '|',
        'ul',
        'ol',
        '|',
        'table',
        'link',
        'image',
        'video',
        'file',
        '|',
        'left',
        'center',
        'right',
        'justify',
        'imageCaption',
        '|',
        'hr',
        'eraser',
        'copyformat',
        '|',
        'superscript',
        'subscript',
        '|',
        'selectall',
        'cut',
        'copy',
        'paste',
        '|',
        'symbol',
        'fullsize',
        'print',
        'about'
      ],
      uploader: {
        insertImageAsBase64URI: true
      },
      // Enhanced image handling
      resizer: {
        min_width: 10,
        min_height: 10,
        showSize: true
      },
      imageeditor: {
        crop: true,
        resize: true,
        resizeWidth: true,
        resizeHeight: true,
        ratio: true
      },
      // Image configuration for RTL
      // Removed due to type incompatibility
      extraButtons: [
        {
          name: 'imageCaption',
          tooltip: 'Add caption to image',
          icon: 'paragraph',
          exec: (editor: any) => {
            const selected = editor.selection.current();

            // Check if the selected element is an image
            if (selected && selected.nodeName.toLowerCase() === 'img') {
              try {
                // Create a figure element with proper styling
                const figure = document.createElement('figure');
                figure.className = 'image-with-caption';
                figure.style.float = 'right';
                figure.style.margin = '10px 0 10px 15px';
                figure.style.maxWidth = '300px';

                // Clone the image to preserve all attributes
                const img = selected.cloneNode(true);

                // Ensure image has proper styling
                img.style.float = 'none';
                img.style.margin = '0';
                img.style.maxWidth = '100%';

                // Create caption
                const figcaption = document.createElement('figcaption');
                figcaption.innerHTML = 'وەسفی وێنە لێرە بنووسە...';
                figcaption.style.textAlign = 'center';
                figcaption.style.padding = '5px';
                figcaption.style.fontSize = '14px';
                figcaption.style.color = '#666';

                // Add image and caption to figure
                figure.appendChild(img);
                figure.appendChild(figcaption);

                // Replace the original image with the figure
                editor.selection.insertNode(figure);

                // Remove the original image if it's still there
                if (selected.parentNode) {
                  selected.parentNode.removeChild(selected);
                }

                // Select the caption for immediate editing
                editor.selection.setCursorIn(figcaption);

                console.log('Caption added successfully');
              } catch (error) {
                console.error('Error adding caption:', error);
              }
            } else {
              alert('لطفا سەرەتا وێنەیەک دیاری بکە');
            }
          }
        }
      ],
      events: {
        afterInit: (instance: any) => {
          // Set RTL direction
          instance.editor.style.direction = 'rtl';

          // Add event listener for image insertion
          instance.events.on('insertImage', (image: HTMLImageElement) => {
            // Ensure image is inserted at cursor position with proper styling
            image.style.float = 'right';
            image.style.margin = '10px 0 10px 15px';
            image.style.maxWidth = '300px';
          });
        }
      },
      // Custom styling
      style: {
        font: 'Noto Kufi Arabic, sans-serif',
      },
    };
  }, [readonly, placeholder, height, width, toolbar, language, rtl]);

  const handleBlur = (newContent: string) => {
    setContent(newContent);
    onChange(newContent);
  };

  return (
    <div className="jodit-editor-wrapper">
      <JoditReactEditor
        ref={editor}
        value={content}
        config={config}
        tabIndex={1}
        onBlur={handleBlur}
        onChange={(newContent) => setContent(newContent)}
      />
      <style jsx global>{`
        .jodit-editor-wrapper {
          margin-bottom: 20px;
        }
        .jodit-container {
          border-radius: 0.375rem;
          font-family: 'Noto Kufi Arabic', sans-serif;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .jodit-wysiwyg {
          font-family: 'Noto Kufi Arabic', sans-serif;
          padding: 16px !important; /* More padding for content area */
          font-size: 16px !important; /* Larger font size */
          line-height: 1.6 !important; /* Better line spacing */
        }
        .jodit-toolbar__box {
          background-color: #f8f9fa;
          padding: 8px !important; /* More padding for toolbar */
        }
        .jodit-toolbar-button {
          margin: 0 3px; /* Slightly more space between buttons */
          padding: 6px !important; /* Larger buttons */
        }
        .jodit-toolbar-button__icon {
          width: 20px !important; /* Larger icons */
          height: 20px !important;
        }
        .jodit-placeholder {
          font-family: 'Noto Kufi Arabic', sans-serif;
          font-size: 16px !important;
        }
        /* Better styling for image resizing */
        .jodit-resizer {
          border: 2px solid #8f5826 !important;
        }
        .jodit-resizer-point {
          background-color: #8f5826 !important;
        }
        /* Image styles - RTL direction */
        .jodit-wysiwyg img {
          max-width: 100%;
          height: auto;
          float: right;
          margin: 10px 0 10px 15px;
        }

        /* Clear floats after paragraphs */
        .jodit-wysiwyg p {
          overflow: auto; /* Ensures text wraps around floated images */
        }

        .jodit-wysiwyg p:after {
          content: "";
          display: table;
          clear: both;
        }

        /* Ensure content container allows for floating */
        .jodit-wysiwyg {
          overflow: auto; /* Prevents content from overflowing */
        }

        /* Caption support */
        .jodit-wysiwyg figure {
          display: inline-block;
          margin: 0;
          max-width: 100%;
        }

        .jodit-wysiwyg figure.image-with-caption {
          float: right;
          margin: 10px 0 10px 15px;
        }

        .jodit-wysiwyg figure img {
          float: none;
          margin: 0;
          display: block;
        }
        .jodit-wysiwyg figcaption {
          text-align: center;
          font-size: 14px;
          color: #666;
          padding: 5px;
        }
      `}</style>
    </div>
  );
};

export default JoditEditor;
