[debug] [2025-05-05T07:49:24.614Z] ----------------------------------------------------------------------
[debug] [2025-05-05T07:49:24.616Z] Command:       /usr/local/bin/node /Users/<USER>/.npm-global/bin/firebase deploy --only functions
[debug] [2025-05-05T07:49:24.616Z] CLI Version:   14.2.2
[debug] [2025-05-05T07:49:24.616Z] Platform:      darwin
[debug] [2025-05-05T07:49:24.616Z] Node Version:  v22.15.0
[debug] [2025-05-05T07:49:24.616Z] Time:          Mon May 05 2025 08:49:24 GMT+0100 (British Summer Time)
[debug] [2025-05-05T07:49:24.616Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-05-05T07:49:24.707Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-05T07:49:24.707Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-05T07:49:24.707Z] [iam] checking project blogcling for permissions ["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]
[debug] [2025-05-05T07:49:24.707Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:24.707Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:24.708Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/blogcling:testIamPermissions [none]
[debug] [2025-05-05T07:49:24.708Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/blogcling:testIamPermissions x-goog-quota-user=projects/blogcling
[debug] [2025-05-05T07:49:24.708Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/blogcling:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-05-05T07:49:25.347Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/blogcling:testIamPermissions 200
[debug] [2025-05-05T07:49:25.348Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/blogcling:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-05-05T07:49:25.348Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:25.348Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:25.348Z] >>> [apiv2][query] POST https://iam.googleapis.com/v1/projects/blogcling/serviceAccounts/<EMAIL>:testIamPermissions [none]
[debug] [2025-05-05T07:49:25.348Z] >>> [apiv2][body] POST https://iam.googleapis.com/v1/projects/blogcling/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[debug] [2025-05-05T07:49:25.935Z] <<< [apiv2][status] POST https://iam.googleapis.com/v1/projects/blogcling/serviceAccounts/<EMAIL>:testIamPermissions 404
[debug] [2025-05-05T07:49:25.936Z] <<< [apiv2][body] POST https://iam.googleapis.com/v1/projects/blogcling/serviceAccounts/<EMAIL>:testIamPermissions {"error":{"code":404,"message":"Unknown service account","status":"NOT_FOUND"}}
[debug] [2025-05-05T07:49:25.936Z] [functions] service account IAM check errored, deploy may fail: Request to https://iam.googleapis.com/v1/projects/blogcling/serviceAccounts/<EMAIL>:testIamPermissions had HTTP Error: 404, Unknown service account {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":404,"message":"Unknown service account","status":"NOT_FOUND"}},"response":{"statusCode":404}},"exit":1,"message":"Request to https://iam.googleapis.com/v1/projects/blogcling/serviceAccounts/<EMAIL>:testIamPermissions had HTTP Error: 404, Unknown service account","status":404}
[info] 
[info] === Deploying to 'blogcling'...
[info] 
[info] i  deploying functions 
[info] Running command: npm --prefix "$RESOURCE_DIR" run build
[info] ✔  functions: Finished running predeploy script. 
[debug] [2025-05-05T07:49:26.822Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:26.822Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:26.822Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/blogcling [none]
[debug] [2025-05-05T07:49:27.027Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/blogcling 200
[debug] [2025-05-05T07:49:27.028Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/blogcling {"projectNumber":"************","projectId":"blogcling","lifecycleState":"ACTIVE","name":"blogcling","labels":{"firebase":"enabled","firebase-core":"disabled","generative-language":"enabled"},"createTime":"2025-04-30T23:57:37.478901Z"}
[info] i  functions: preparing codebase default for deployment 
[info] i  functions: ensuring required API cloudfunctions.googleapis.com is enabled... 
[debug] [2025-05-05T07:49:27.030Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:27.030Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:27.030Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:27.030Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: ensuring required API cloudbuild.googleapis.com is enabled... 
[debug] [2025-05-05T07:49:27.030Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:27.030Z] Checked if tokens are valid: true, expires at: *************
[info] i  artifactregistry: ensuring required API artifactregistry.googleapis.com is enabled... 
[debug] [2025-05-05T07:49:27.030Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:27.031Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:27.031Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudfunctions.googleapis.com [none]
[debug] [2025-05-05T07:49:27.031Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudfunctions.googleapis.com x-goog-quota-user=projects/blogcling
[debug] [2025-05-05T07:49:27.033Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/runtimeconfig.googleapis.com [none]
[debug] [2025-05-05T07:49:27.033Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/runtimeconfig.googleapis.com x-goog-quota-user=projects/blogcling
[debug] [2025-05-05T07:49:27.035Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudbuild.googleapis.com [none]
[debug] [2025-05-05T07:49:27.035Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudbuild.googleapis.com x-goog-quota-user=projects/blogcling
[debug] [2025-05-05T07:49:27.037Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/artifactregistry.googleapis.com [none]
[debug] [2025-05-05T07:49:27.037Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/artifactregistry.googleapis.com x-goog-quota-user=projects/blogcling
[debug] [2025-05-05T07:49:27.902Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudfunctions.googleapis.com 200
[debug] [2025-05-05T07:49:27.903Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudfunctions.googleapis.com [omitted]
[warn] ⚠  functions: missing required API cloudfunctions.googleapis.com. Enabling now... 
[debug] [2025-05-05T07:49:27.904Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:27.904Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:27.904Z] >>> [apiv2][query] POST https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudfunctions.googleapis.com:enable [none]
[debug] [2025-05-05T07:49:27.904Z] >>> [apiv2][(partial)header] POST https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudfunctions.googleapis.com:enable x-goog-quota-user=projects/blogcling
[debug] [2025-05-05T07:49:27.908Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudbuild.googleapis.com 200
[debug] [2025-05-05T07:49:27.909Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudbuild.googleapis.com [omitted]
[warn] ⚠  functions: missing required API cloudbuild.googleapis.com. Enabling now... 
[debug] [2025-05-05T07:49:27.909Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:27.909Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:27.909Z] >>> [apiv2][query] POST https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudbuild.googleapis.com:enable [none]
[debug] [2025-05-05T07:49:27.909Z] >>> [apiv2][(partial)header] POST https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudbuild.googleapis.com:enable x-goog-quota-user=projects/blogcling
[debug] [2025-05-05T07:49:27.914Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/runtimeconfig.googleapis.com 200
[debug] [2025-05-05T07:49:27.915Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/runtimeconfig.googleapis.com [omitted]
[debug] [2025-05-05T07:49:27.926Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/artifactregistry.googleapis.com 200
[debug] [2025-05-05T07:49:27.926Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/artifactregistry.googleapis.com [omitted]
[warn] ⚠  artifactregistry: missing required API artifactregistry.googleapis.com. Enabling now... 
[debug] [2025-05-05T07:49:27.926Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:27.927Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:27.927Z] >>> [apiv2][query] POST https://serviceusage.googleapis.com/v1/projects/blogcling/services/artifactregistry.googleapis.com:enable [none]
[debug] [2025-05-05T07:49:27.927Z] >>> [apiv2][(partial)header] POST https://serviceusage.googleapis.com/v1/projects/blogcling/services/artifactregistry.googleapis.com:enable x-goog-quota-user=projects/blogcling
[debug] [2025-05-05T07:49:28.991Z] <<< [apiv2][status] POST https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudbuild.googleapis.com:enable 200
[debug] [2025-05-05T07:49:28.992Z] <<< [apiv2][body] POST https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudbuild.googleapis.com:enable [omitted]
[debug] [2025-05-05T07:49:29.098Z] <<< [apiv2][status] POST https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudfunctions.googleapis.com:enable 200
[debug] [2025-05-05T07:49:29.099Z] <<< [apiv2][body] POST https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudfunctions.googleapis.com:enable [omitted]
[debug] [2025-05-05T07:49:29.299Z] <<< [apiv2][status] POST https://serviceusage.googleapis.com/v1/projects/blogcling/services/artifactregistry.googleapis.com:enable 200
[debug] [2025-05-05T07:49:29.299Z] <<< [apiv2][body] POST https://serviceusage.googleapis.com/v1/projects/blogcling/services/artifactregistry.googleapis.com:enable [omitted]
[debug] [2025-05-05T07:49:38.997Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:38.998Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:38.998Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudbuild.googleapis.com [none]
[debug] [2025-05-05T07:49:38.998Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudbuild.googleapis.com x-goog-quota-user=projects/blogcling
[debug] [2025-05-05T07:49:39.102Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:39.102Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:39.103Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudfunctions.googleapis.com [none]
[debug] [2025-05-05T07:49:39.103Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudfunctions.googleapis.com x-goog-quota-user=projects/blogcling
[debug] [2025-05-05T07:49:39.301Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:39.301Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:39.302Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/artifactregistry.googleapis.com [none]
[debug] [2025-05-05T07:49:39.302Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/artifactregistry.googleapis.com x-goog-quota-user=projects/blogcling
[debug] [2025-05-05T07:49:39.846Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudbuild.googleapis.com 200
[debug] [2025-05-05T07:49:39.847Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudbuild.googleapis.com [omitted]
[info] ✔  functions: required API cloudbuild.googleapis.com is enabled 
[debug] [2025-05-05T07:49:39.899Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudfunctions.googleapis.com 200
[debug] [2025-05-05T07:49:39.899Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudfunctions.googleapis.com [omitted]
[info] ✔  functions: required API cloudfunctions.googleapis.com is enabled 
[debug] [2025-05-05T07:49:40.098Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/artifactregistry.googleapis.com 200
[debug] [2025-05-05T07:49:40.098Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/artifactregistry.googleapis.com [omitted]
[info] ✔  artifactregistry: required API artifactregistry.googleapis.com is enabled 
[debug] [2025-05-05T07:49:40.100Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:40.100Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T07:49:40.100Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/blogcling/adminSdkConfig [none]
[debug] [2025-05-05T08:02:15.724Z] ----------------------------------------------------------------------
[debug] [2025-05-05T08:02:15.726Z] Command:       /usr/local/bin/node /Users/<USER>/.npm-global/bin/firebase deploy --only functions
[debug] [2025-05-05T08:02:15.726Z] CLI Version:   14.2.2
[debug] [2025-05-05T08:02:15.726Z] Platform:      darwin
[debug] [2025-05-05T08:02:15.726Z] Node Version:  v22.15.0
[debug] [2025-05-05T08:02:15.727Z] Time:          Mon May 05 2025 09:02:15 GMT+0100 (British Summer Time)
[debug] [2025-05-05T08:02:15.727Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-05-05T08:02:15.825Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-05T08:02:15.826Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-05T08:02:15.826Z] [iam] checking project blogcling for permissions ["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]
[debug] [2025-05-05T08:02:15.826Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:15.826Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:15.827Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/blogcling:testIamPermissions [none]
[debug] [2025-05-05T08:02:15.827Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/blogcling:testIamPermissions x-goog-quota-user=projects/blogcling
[debug] [2025-05-05T08:02:15.827Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/blogcling:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-05-05T08:02:16.500Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/blogcling:testIamPermissions 200
[debug] [2025-05-05T08:02:16.501Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/blogcling:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-05-05T08:02:16.501Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:16.501Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:16.501Z] >>> [apiv2][query] POST https://iam.googleapis.com/v1/projects/blogcling/serviceAccounts/<EMAIL>:testIamPermissions [none]
[debug] [2025-05-05T08:02:16.501Z] >>> [apiv2][body] POST https://iam.googleapis.com/v1/projects/blogcling/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[debug] [2025-05-05T08:02:17.139Z] <<< [apiv2][status] POST https://iam.googleapis.com/v1/projects/blogcling/serviceAccounts/<EMAIL>:testIamPermissions 200
[debug] [2025-05-05T08:02:17.139Z] <<< [apiv2][body] POST https://iam.googleapis.com/v1/projects/blogcling/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[info] 
[info] === Deploying to 'blogcling'...
[info] 
[info] i  deploying functions 
[info] Running command: npm --prefix "$RESOURCE_DIR" run build
[info] ✔  functions: Finished running predeploy script. 
[debug] [2025-05-05T08:02:18.160Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:18.160Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:18.160Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/blogcling [none]
[debug] [2025-05-05T08:02:18.326Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/blogcling 200
[debug] [2025-05-05T08:02:18.326Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/blogcling {"projectNumber":"************","projectId":"blogcling","lifecycleState":"ACTIVE","name":"blogcling","labels":{"firebase":"enabled","firebase-core":"disabled","generative-language":"enabled"},"createTime":"2025-04-30T23:57:37.478901Z"}
[info] i  functions: preparing codebase default for deployment 
[info] i  functions: ensuring required API cloudfunctions.googleapis.com is enabled... 
[debug] [2025-05-05T08:02:18.327Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:18.327Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:18.328Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:18.328Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: ensuring required API cloudbuild.googleapis.com is enabled... 
[debug] [2025-05-05T08:02:18.328Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:18.328Z] Checked if tokens are valid: true, expires at: *************
[info] i  artifactregistry: ensuring required API artifactregistry.googleapis.com is enabled... 
[debug] [2025-05-05T08:02:18.328Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:18.328Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:18.328Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudfunctions.googleapis.com [none]
[debug] [2025-05-05T08:02:18.328Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudfunctions.googleapis.com x-goog-quota-user=projects/blogcling
[debug] [2025-05-05T08:02:18.329Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/runtimeconfig.googleapis.com [none]
[debug] [2025-05-05T08:02:18.330Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/runtimeconfig.googleapis.com x-goog-quota-user=projects/blogcling
[debug] [2025-05-05T08:02:18.330Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudbuild.googleapis.com [none]
[debug] [2025-05-05T08:02:18.330Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudbuild.googleapis.com x-goog-quota-user=projects/blogcling
[debug] [2025-05-05T08:02:18.331Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/artifactregistry.googleapis.com [none]
[debug] [2025-05-05T08:02:18.331Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/artifactregistry.googleapis.com x-goog-quota-user=projects/blogcling
[debug] [2025-05-05T08:02:19.140Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/artifactregistry.googleapis.com 200
[debug] [2025-05-05T08:02:19.140Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/artifactregistry.googleapis.com [omitted]
[info] ✔  artifactregistry: required API artifactregistry.googleapis.com is enabled 
[debug] [2025-05-05T08:02:19.146Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudfunctions.googleapis.com 200
[debug] [2025-05-05T08:02:19.146Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudfunctions.googleapis.com [omitted]
[info] ✔  functions: required API cloudfunctions.googleapis.com is enabled 
[debug] [2025-05-05T08:02:19.154Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/runtimeconfig.googleapis.com 200
[debug] [2025-05-05T08:02:19.154Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/runtimeconfig.googleapis.com [omitted]
[debug] [2025-05-05T08:02:19.173Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudbuild.googleapis.com 200
[debug] [2025-05-05T08:02:19.173Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudbuild.googleapis.com [omitted]
[info] ✔  functions: required API cloudbuild.googleapis.com is enabled 
[debug] [2025-05-05T08:02:19.174Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:19.174Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:19.174Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/blogcling/adminSdkConfig [none]
[debug] [2025-05-05T08:02:19.592Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/blogcling/adminSdkConfig 200
[debug] [2025-05-05T08:02:19.592Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/blogcling/adminSdkConfig {"projectId":"blogcling","storageBucket":"blogcling.firebasestorage.app"}
[debug] [2025-05-05T08:02:19.592Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:19.593Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:19.593Z] >>> [apiv2][query] GET https://runtimeconfig.googleapis.com/v1beta1/projects/blogcling/configs [none]
[debug] [2025-05-05T08:02:19.863Z] <<< [apiv2][status] GET https://runtimeconfig.googleapis.com/v1beta1/projects/blogcling/configs 200
[debug] [2025-05-05T08:02:19.863Z] <<< [apiv2][body] GET https://runtimeconfig.googleapis.com/v1beta1/projects/blogcling/configs {}
[debug] [2025-05-05T08:02:19.866Z] Validating nodejs source
[warn] ⚠  functions: Runtime Node.js 18 was deprecated on 2025-04-30 and will be decommissioned on 2025-10-31, after which you will not be able to deploy without upgrading. Consider upgrading now to avoid disruption. See https://cloud.google.com/functions/docs/runtime-support for full details on the lifecycle policy 
[warn] ⚠  functions: package.json indicates an outdated version of firebase-functions. Please upgrade using npm install --save firebase-functions@latest in your functions directory. 
[warn] ⚠  functions: Please note that there will be breaking changes when you upgrade. 
[debug] [2025-05-05T08:02:20.276Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "build": "tsc",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "18"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^11.11.1",
    "firebase-functions": "^4.9.0"
  },
  "devDependencies": {
    "typescript": "^5.1.6"
  },
  "private": true
}
[debug] [2025-05-05T08:02:20.276Z] Building nodejs source
[info] i  functions: Loading and analyzing source code for codebase default to determine what to deploy 
[info] i  functions: You are using a version of firebase-functions SDK (4.9.0) that does not have support for the newest Firebase Extensions features. Please update firebase-functions SDK to >=5.1.0 to use them correctly 
[debug] [2025-05-05T08:02:20.277Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-05-05T08:02:20.280Z] Found firebase-functions binary at '/Users/<USER>/Desktop/nextjs/cms/functions/node_modules/.bin/firebase-functions'
[info] Serving at port 8082

[debug] [2025-05-05T08:02:20.519Z] Got response from /__/functions.yaml {"endpoints":{"incrementDailyViews":{"platform":"gcfv1","availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"serviceAccountEmail":null,"vpc":null,"scheduleTrigger":{"schedule":"0 2 * * *","retryConfig":{"retryCount":null,"maxDoublings":null,"maxRetryDuration":null,"maxBackoffDuration":null,"minBackoffDuration":null},"timeZone":"Europe/London"},"labels":{},"entryPoint":"incrementDailyViews"}},"specVersion":"v1alpha1","requiredAPIs":[{"api":"cloudscheduler.googleapis.com","reason":"Needed for scheduled functions."}]}
[info] i  functions: preparing functions directory for uploading... 
[info] i  functions: packaged /Users/<USER>/Desktop/nextjs/cms/functions (40.75 KB) for uploading 
[debug] [2025-05-05T08:02:24.575Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:24.575Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:24.575Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v1/projects/blogcling/locations/-/functions [none]
[debug] [2025-05-05T08:02:25.163Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v1/projects/blogcling/locations/-/functions 200
[debug] [2025-05-05T08:02:25.164Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v1/projects/blogcling/locations/-/functions {}
[debug] [2025-05-05T08:02:25.164Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:25.164Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:25.165Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/blogcling/locations/-/functions filter=environment%3D%22GEN_2%22
[debug] [2025-05-05T08:02:26.100Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/blogcling/locations/-/functions 200
[debug] [2025-05-05T08:02:26.101Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/blogcling/locations/-/functions {}
[info] i  functions: ensuring required API cloudscheduler.googleapis.com is enabled... 
[debug] [2025-05-05T08:02:26.104Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:26.105Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:26.105Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudscheduler.googleapis.com [none]
[debug] [2025-05-05T08:02:26.105Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudscheduler.googleapis.com x-goog-quota-user=projects/blogcling
[debug] [2025-05-05T08:02:26.913Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudscheduler.googleapis.com 200
[debug] [2025-05-05T08:02:26.913Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/blogcling/services/cloudscheduler.googleapis.com [omitted]
[info] ✔  functions: required API cloudscheduler.googleapis.com is enabled 
[debug] [2025-05-05T08:02:26.916Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:26.916Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:26.916Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/blogcling [none]
[debug] [2025-05-05T08:02:27.563Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/blogcling 200
[debug] [2025-05-05T08:02:27.563Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/blogcling {"projectNumber":"************","projectId":"blogcling","lifecycleState":"ACTIVE","name":"blogcling","labels":{"firebase":"enabled","firebase-core":"disabled","generative-language":"enabled"},"createTime":"2025-04-30T23:57:37.478901Z"}
[debug] [2025-05-05T08:02:27.566Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:27.566Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:27.566Z] >>> [apiv2][query] POST https://cloudfunctions.googleapis.com/v1/projects/blogcling/locations/us-central1/functions:generateUploadUrl [none]
[debug] [2025-05-05T08:02:27.566Z] >>> [apiv2][body] POST https://cloudfunctions.googleapis.com/v1/projects/blogcling/locations/us-central1/functions:generateUploadUrl {}
[debug] [2025-05-05T08:02:28.241Z] <<< [apiv2][status] POST https://cloudfunctions.googleapis.com/v1/projects/blogcling/locations/us-central1/functions:generateUploadUrl 200
[debug] [2025-05-05T08:02:28.242Z] <<< [apiv2][body] POST https://cloudfunctions.googleapis.com/v1/projects/blogcling/locations/us-central1/functions:generateUploadUrl {"uploadUrl":"https://storage.googleapis.com/uploads-************.us-central1.cloudfunctions.appspot.com/8dcc14b0-5e9d-46c1-ac4e-5942516d23f6.zip?GoogleAccessId=<EMAIL>&Expires=**********&Signature=bKfP5Q921HUbIFbXAPppXPIA8Mvr4x1V3nAqlsn3okapsz7NuQbe69iNwH8FXLfuQt%2BeMCbHY8q686SAlqapDKR0Fd8WepMw9RCYnO43AePbqD%2F%2F%2BbPTOc0H1AFLnwt%2F09XYWL8YSz7l63KDe%2Fg1p0HWQrhPYnyEfcrLtUizmYMXgAmjxPSJHRRKbvNkjZvyKT7XilbXwZyAk%2Bt3pkCk%2Bt55cGfjRZp36oNU81pyl2TjtPLlStBatZkeFtbeBV%2FwkWo8Sk%2FgJ%2FUr0ptiMEsOk6djl7op38MZogE6KrKK1mF2U6BL9ixtu%2BZ5vESaYeUGsbYdpIUw7hww%2FZNJJrSTiA%3D%3D"}
[debug] [2025-05-05T08:02:28.243Z] >>> [apiv2][query] PUT https://storage.googleapis.com/uploads-************.us-central1.cloudfunctions.appspot.com/8dcc14b0-5e9d-46c1-ac4e-5942516d23f6.zip GoogleAccessId=service-************%40gcf-admin-robot.iam.gserviceaccount.com&Expires=**********&Signature=bKfP5Q921HUbIFbXAPppXPIA8Mvr4x1V3nAqlsn3okapsz7NuQbe69iNwH8FXLfuQt%2BeMCbHY8q686SAlqapDKR0Fd8WepMw9RCYnO43AePbqD%2F%2F%2BbPTOc0H1AFLnwt%2F09XYWL8YSz7l63KDe%2Fg1p0HWQrhPYnyEfcrLtUizmYMXgAmjxPSJHRRKbvNkjZvyKT7XilbXwZyAk%2Bt3pkCk%2Bt55cGfjRZp36oNU81pyl2TjtPLlStBatZkeFtbeBV%2FwkWo8Sk%2FgJ%2FUr0ptiMEsOk6djl7op38MZogE6KrKK1mF2U6BL9ixtu%2BZ5vESaYeUGsbYdpIUw7hww%2FZNJJrSTiA%3D%3D
[debug] [2025-05-05T08:02:28.243Z] >>> [apiv2][body] PUT https://storage.googleapis.com/uploads-************.us-central1.cloudfunctions.appspot.com/8dcc14b0-5e9d-46c1-ac4e-5942516d23f6.zip [stream]
[debug] [2025-05-05T08:02:28.619Z] <<< [apiv2][status] PUT https://storage.googleapis.com/uploads-************.us-central1.cloudfunctions.appspot.com/8dcc14b0-5e9d-46c1-ac4e-5942516d23f6.zip 200
[debug] [2025-05-05T08:02:28.619Z] <<< [apiv2][body] PUT https://storage.googleapis.com/uploads-************.us-central1.cloudfunctions.appspot.com/8dcc14b0-5e9d-46c1-ac4e-5942516d23f6.zip [omitted]
[info] ✔  functions: functions folder uploaded successfully 
[info] i  functions: creating Node.js 18 (1st Gen) function incrementDailyViews(us-central1)... 
[debug] [2025-05-05T08:02:28.626Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:28.626Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:28.627Z] >>> [apiv2][query] POST https://cloudfunctions.googleapis.com/v1/projects/blogcling/locations/us-central1/functions [none]
[debug] [2025-05-05T08:02:28.627Z] >>> [apiv2][body] POST https://cloudfunctions.googleapis.com/v1/projects/blogcling/locations/us-central1/functions {"name":"projects/blogcling/locations/us-central1/functions/incrementDailyViews","sourceUploadUrl":"https://storage.googleapis.com/uploads-************.us-central1.cloudfunctions.appspot.com/8dcc14b0-5e9d-46c1-ac4e-5942516d23f6.zip?GoogleAccessId=<EMAIL>&Expires=**********&Signature=bKfP5Q921HUbIFbXAPppXPIA8Mvr4x1V3nAqlsn3okapsz7NuQbe69iNwH8FXLfuQt%2BeMCbHY8q686SAlqapDKR0Fd8WepMw9RCYnO43AePbqD%2F%2F%2BbPTOc0H1AFLnwt%2F09XYWL8YSz7l63KDe%2Fg1p0HWQrhPYnyEfcrLtUizmYMXgAmjxPSJHRRKbvNkjZvyKT7XilbXwZyAk%2Bt3pkCk%2Bt55cGfjRZp36oNU81pyl2TjtPLlStBatZkeFtbeBV%2FwkWo8Sk%2FgJ%2FUr0ptiMEsOk6djl7op38MZogE6KrKK1mF2U6BL9ixtu%2BZ5vESaYeUGsbYdpIUw7hww%2FZNJJrSTiA%3D%3D","entryPoint":"incrementDailyViews","runtime":"nodejs18","dockerRegistry":"ARTIFACT_REGISTRY","labels":{"deployment-tool":"cli-firebase","deployment-scheduled":"true","firebase-functions-hash":"6fb24aeb714bef5672e4c4bbd87566a3ebda85f5"},"eventTrigger":{"eventType":"google.pubsub.topic.publish","resource":"projects/blogcling/topics/firebase-schedule-incrementDailyViews-us-central1"},"minInstances":null,"maxInstances":null,"ingressSettings":null,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"blogcling\",\"storageBucket\":\"blogcling.firebasestorage.app\"}","GCLOUD_PROJECT":"blogcling","EVENTARC_CLOUD_EVENT_SOURCE":"projects/blogcling/locations/us-central1/functions/incrementDailyViews"},"serviceAccountEmail":null,"availableMemoryMb":null,"timeout":null,"vpcConnector":null,"vpcConnectorEgressSettings":null,"buildEnvironmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""}}
[debug] [2025-05-05T08:02:30.580Z] <<< [apiv2][status] POST https://cloudfunctions.googleapis.com/v1/projects/blogcling/locations/us-central1/functions 400
[debug] [2025-05-05T08:02:30.581Z] <<< [apiv2][body] POST https://cloudfunctions.googleapis.com/v1/projects/blogcling/locations/us-central1/functions {"error":{"code":400,"message":"Failed to create 1st Gen function projects/blogcling/locations/us-central1/functions/incrementDailyViews: Default service account '<EMAIL>' doesn't exist. Please recreate this account or specify a different account. Please visit https://cloud.google.com/functions/docs/troubleshooting for in-depth troubleshooting documentation.","status":"FAILED_PRECONDITION"}}
[warn] ⚠  functions: failed to create function projects/blogcling/locations/us-central1/functions/incrementDailyViews 
[error] Failed to create function projects/blogcling/locations/us-central1/functions/incrementDailyViews
[debug] [2025-05-05T08:02:30.592Z] Total Function Deployment time: 1960
[debug] [2025-05-05T08:02:30.592Z] 1 Functions Deployed
[debug] [2025-05-05T08:02:30.592Z] 1 Functions Errored
[debug] [2025-05-05T08:02:30.592Z] 0 Function Deployments Aborted
[debug] [2025-05-05T08:02:30.592Z] Average Function Deployment time: 1959
[info] 
[info] Functions deploy had errors with the following functions:
	incrementDailyViews(us-central1)
[debug] [2025-05-05T08:02:30.666Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:30.666Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-05-05T08:02:30.666Z] >>> [apiv2][query] GET https://artifactregistry.googleapis.com/v1/projects/blogcling/locations/us-central1/repositories/gcf-artifacts [none]
[debug] [2025-05-05T08:02:31.426Z] <<< [apiv2][status] GET https://artifactregistry.googleapis.com/v1/projects/blogcling/locations/us-central1/repositories/gcf-artifacts 404
[debug] [2025-05-05T08:02:31.426Z] <<< [apiv2][body] GET https://artifactregistry.googleapis.com/v1/projects/blogcling/locations/us-central1/repositories/gcf-artifacts {"error":{"code":404,"message":"Requested entity was not found.","status":"NOT_FOUND"}}
[debug] [2025-05-05T08:02:31.426Z] Failed to check artifact cleanup policy for region us-central1: Request to https://artifactregistry.googleapis.com/v1/projects/blogcling/locations/us-central1/repositories/gcf-artifacts had HTTP Error: 404, Requested entity was not found. {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":404,"message":"Requested entity was not found.","status":"NOT_FOUND"}},"response":{"statusCode":404}},"exit":1,"message":"Request to https://artifactregistry.googleapis.com/v1/projects/blogcling/locations/us-central1/repositories/gcf-artifacts had HTTP Error: 404, Requested entity was not found.","status":404}
[debug] [2025-05-05T08:02:31.427Z] Functions deploy failed.
[debug] [2025-05-05T08:02:31.427Z] {
  "endpoint": {
    "id": "incrementDailyViews",
    "project": "blogcling",
    "region": "us-central1",
    "entryPoint": "incrementDailyViews",
    "platform": "gcfv1",
    "runtime": "nodejs18",
    "scheduleTrigger": {
      "schedule": "0 2 * * *",
      "timeZone": "Europe/London",
      "retryConfig": {
        "maxBackoffSeconds": null,
        "minBackoffSeconds": null,
        "maxRetrySeconds": null,
        "retryCount": null,
        "maxDoublings": null
      }
    },
    "labels": {
      "deployment-tool": "cli-firebase"
    },
    "serviceAccount": null,
    "ingressSettings": null,
    "availableMemoryMb": null,
    "timeoutSeconds": null,
    "maxInstances": null,
    "minInstances": null,
    "vpc": null,
    "environmentVariables": {
      "FIREBASE_CONFIG": "{\"projectId\":\"blogcling\",\"storageBucket\":\"blogcling.firebasestorage.app\"}",
      "GCLOUD_PROJECT": "blogcling",
      "EVENTARC_CLOUD_EVENT_SOURCE": "projects/blogcling/locations/us-central1/functions/incrementDailyViews"
    },
    "codebase": "default",
    "targetedByOnly": false,
    "hash": "6fb24aeb714bef5672e4c4bbd87566a3ebda85f5"
  },
  "op": "create",
  "original": {
    "name": "FirebaseError",
    "children": [],
    "context": {
      "function": "projects/blogcling/locations/us-central1/functions/incrementDailyViews"
    },
    "exit": 1,
    "message": "Failed to create function projects/blogcling/locations/us-central1/functions/incrementDailyViews",
    "original": {
      "name": "FirebaseError",
      "children": [],
      "context": {
        "body": {
          "error": {
            "code": 400,
            "message": "Failed to create 1st Gen function projects/blogcling/locations/us-central1/functions/incrementDailyViews: Default service account '<EMAIL>' doesn't exist. Please recreate this account or specify a different account. Please visit https://cloud.google.com/functions/docs/troubleshooting for in-depth troubleshooting documentation.",
            "status": "FAILED_PRECONDITION"
          }
        },
        "response": {
          "statusCode": 400
        }
      },
      "exit": 1,
      "message": "Request to https://cloudfunctions.googleapis.com/v1/projects/blogcling/locations/us-central1/functions had HTTP Error: 400, Failed to create 1st Gen function projects/blogcling/locations/us-central1/functions/incrementDailyViews: Default service account '<EMAIL>' doesn't exist. Please recreate this account or specify a different account. Please visit https://cloud.google.com/functions/docs/troubleshooting for in-depth troubleshooting documentation.",
      "status": 400
    },
    "status": 400,
    "code": 400
  }
}
[debug] [2025-05-05T08:02:31.452Z] Error: Failed to create function incrementDailyViews in region us-central1
    at /Users/<USER>/.npm-global/lib/node_modules/firebase-tools/lib/deploy/functions/release/fabricator.js:52:11
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Fabricator.createV1Function (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/lib/deploy/functions/release/fabricator.js:182:32)
    at async Fabricator.createEndpoint (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/lib/deploy/functions/release/fabricator.js:134:13)
    at async handle (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/lib/deploy/functions/release/fabricator.js:89:17)
[error] 
[error] Error: There was an error deploying functions
