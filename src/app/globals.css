/* Font is now loaded via Google Fonts CDN */
@import "tailwindcss";

* {
  font-family: 'Noto <PERSON> Arabic', sans-serif;
}

:root {
  /* Base colors */
  --background: #ffffff;
  --foreground: #000000;

  /* Primary colors - Brown theme */
  --primary-50: #faf5f0;
  --primary-100: #f5ebe0;
  --primary-200: #e9d5c0;
  --primary-300: #dbb995;
  --primary-400: #c99c6e;
  --primary-500: #b17d4a;
  --primary-600: #8F5826;
  --primary-700: #7a4a20;
  --primary-800: #653d1c;
  --primary-900: #52321a;

  /* Gray colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Success colors */
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-700: #15803d;

  /* Warning colors */
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-700: #b45309;

  /* Danger colors */
  --danger-50: #fef2f2;
  --danger-500: #ef4444;
  --danger-700: #b91c1c;

  /* UI colors */
  --sidebar-bg: #653d1c;
  --sidebar-text: #ffffff;
  --sidebar-hover: #7a4a20;
  --sidebar-active: #8F5826;
  --header-bg: #ffffff;
  --card-bg: #ffffff;
  --card-border: #e5e7eb;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Disable dark mode to ensure consistent white background */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #ffffff;
    --foreground: #000000;
    --header-bg: #ffffff;
    --card-bg: #ffffff;
    --card-border: #e5e7eb;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Noto Kufi Arabic', var(--font-sans), Arial, sans-serif;
  text-align: right;
  line-height: 1.5;
}

h1, h2, h3, h4, h5, h6 {
  color: var(--gray-900);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-family: 'Noto Kufi Arabic', var(--font-sans), Arial, sans-serif;
}

p {
  margin-bottom: 1rem;
  color: var(--gray-800);
}

a {
  color: var(--primary-600);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-800);
}

button, input, select, textarea {
  font-family: 'Noto Kufi Arabic', var(--font-sans), Arial, sans-serif;
}

/* RTL support for form elements */
input, textarea, select {
  text-align: right;
  direction: rtl;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--gray-300);
  border-radius: 0.375rem;
  background-color: white;
  color: var(--gray-700);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}

/* RTL support for buttons */
button {
  direction: rtl;
  font-weight: 500;
  transition: all 0.2s ease;
}

/* Card styling */
.card {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

/* Table styling */
table {
  width: 100%;
  border-collapse: collapse;
}

th {
  font-weight: 600;
  text-align: right;
  padding: 0.75rem 1rem;
  background-color: var(--gray-50);
  color: var(--gray-700);
  border-bottom: 1px solid var(--gray-200);
}

td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--gray-200);
}

tr:hover {
  background-color: var(--gray-50);
}

/* Content display styles for blog posts */
.blog-content img,
.blog-content img.blog-image-processed {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  float: right;
  margin: 10px 0 10px 15px;
  object-fit: contain;
  max-height: 600px; /* Prevent excessively tall images */
  width: auto !important; /* Force image to maintain natural width */
  display: inline-block !important; /* Ensure proper display */
  aspect-ratio: attr(width) / attr(height); /* Use natural aspect ratio */
}

/* Override Tailwind prose styles that might affect image floating */
.prose img {
  float: right !important;
  margin: 10px 0 10px 15px !important;
  display: inline-block !important;
  object-fit: contain !important;
  max-height: 600px !important; /* Prevent excessively tall images */
  width: auto !important; /* Force image to maintain natural width */
  max-width: 100% !important; /* Ensure image doesn't overflow container */
  aspect-ratio: attr(width) / attr(height) !important; /* Use natural aspect ratio */
}

/* Clear floats after paragraphs */
.blog-content p, .prose p {
  overflow: visible; /* Prevents scrollbars while still allowing text wrapping */
}

.blog-content p:after, .prose p:after {
  content: "";
  display: table;
  clear: both;
}

/* Google Translate Support - Kurdish Optimized */
.goog-te-banner-frame {
  display: none !important;
}

body.goog-te-hl {
  top: 0 !important;
}

.goog-te-menu-value {
  padding: 8px 12px !important;
  font-family: 'Noto Kufi Arabic', Arial, sans-serif !important;
  font-size: 14px !important;
  color: #374151 !important;
}

.goog-te-gadget-simple {
  background-color: white !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  font-family: 'Noto Kufi Arabic', Arial, sans-serif !important;
}

.goog-te-gadget-simple:hover {
  border-color: #8f5826 !important;
}

.goog-te-gadget-simple .goog-te-menu-value {
  font-size: 14px !important;
  color: #374151 !important;
  font-family: 'Noto Kufi Arabic', Arial, sans-serif !important;
}

.goog-te-gadget-simple .goog-te-menu-value:hover {
  color: #8f5826 !important;
}

/* Kurdish text specific styling */
.blog-content[lang="ku"] {
  font-family: 'Noto Kufi Arabic', Arial, sans-serif !important;
  direction: rtl !important;
  text-align: right !important;
}

/* Ensure translated content maintains proper styling */
.translated-ltr {
  direction: ltr !important;
  text-align: left !important;
}

/* Preserve RTL for untranslated elements */
[translate="no"] {
  direction: rtl !important;
  text-align: right !important;
}

/* Google Translate dropdown styling */
.goog-te-combo {
  font-family: 'Noto Kufi Arabic', Arial, sans-serif !important;
  padding: 4px 8px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  background-color: white !important;
  font-size: 14px !important;
}

/* Figure and caption styling */
.blog-content figure, .prose figure {
  display: inline-block;
  margin: 0;
  max-width: 100%;
  float: right;
  margin-left: 15px;
  margin-bottom: 10px;
}

.blog-content figure.image-with-caption, .prose figure.image-with-caption {
  float: right;
  margin: 10px 0 10px 15px;
}

.blog-content figure img, .prose figure img {
  float: none;
  margin: 0;
  display: block;
  max-width: 100%;
  height: auto;
  object-fit: contain;
  max-height: 600px; /* Prevent excessively tall images */
  width: auto !important; /* Force image to maintain natural width */
  box-sizing: border-box; /* Ensure padding and border are included in width calculation */
}

.blog-content figcaption, .prose figcaption {
  text-align: center;
  font-size: 14px;
  color: #666;
  padding: 5px;
}

/* Ensure content is properly cleared */
.blog-content:after, .prose:after {
  content: "";
  display: table;
  clear: both;
}

/* Special handling for large images */
.blog-content img.large-image, .prose img.large-image {
  display: block;
  float: none;
  margin: 20px auto;
  max-width: 100%;
  height: auto;
  object-fit: contain;
}

/* Responsive image container for maintaining aspect ratio */
.image-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.image-container img {
  position: relative;
  width: 100%;
  height: auto;
  object-fit: contain;
}

/* Image wrapper to prevent horizontal stretching */
.blog-content .img-wrapper, .prose .img-wrapper {
  display: inline-block;
  max-width: 100%;
  float: right;
  margin: 10px 0 10px 15px;
}

.blog-content .img-wrapper img, .prose .img-wrapper img {
  display: block;
  max-width: 100%;
  height: auto;
  width: auto !important;
  margin: 0;
  float: none;
}

/* Fix for images that might be inserted directly into the content */
.blog-content > img, .prose > img {
  display: inline-block;
  max-width: 100%;
  width: auto !important;
}

/* Add transition for smoother image loading */
.blog-content img, .prose img {
  transition: opacity 0.3s ease;
}

/* Add loaded class for images */
.blog-content img.loaded, .prose img.loaded {
  opacity: 1;
}

/* Force aspect ratio preservation */
.blog-content img, .prose img {
  aspect-ratio: attr(width) / attr(height);
}
