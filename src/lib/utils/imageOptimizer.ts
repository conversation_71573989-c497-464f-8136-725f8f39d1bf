/**
 * Image optimization utility for resizing and optimizing images before upload
 */

/**
 * Resize and optimize an image
 * @param base64Image - Base64 encoded image string
 * @param maxWidth - Maximum width of the resized image
 * @param maxHeight - Maximum height of the resized image
 * @param quality - JPEG quality (0-1)
 * @returns Promise with optimized base64 image
 */
export async function optimizeImage(
  base64Image: string,
  maxWidth: number = 1200,
  maxHeight: number = 1200,
  quality: number = 0.85
): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      // Create an image element to load the base64 image
      const img = new Image();
      img.onload = () => {
        // Calculate new dimensions while maintaining aspect ratio
        let width = img.width;
        let height = img.height;
        
        if (width > maxWidth || height > maxHeight) {
          const aspectRatio = width / height;
          
          if (width > maxWidth) {
            width = maxWidth;
            height = width / aspectRatio;
          }
          
          if (height > maxHeight) {
            height = maxHeight;
            width = height * aspectRatio;
          }
        }
        
        // Create a canvas to draw the resized image
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        
        // Draw the image on the canvas
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('Could not get canvas context'));
          return;
        }
        
        ctx.drawImage(img, 0, 0, width, height);
        
        // Get the content type from the base64 string
        const contentType = base64Image.split(';')[0].split(':')[1] || 'image/jpeg';
        
        // Convert canvas to base64 with specified quality
        const optimizedBase64 = canvas.toDataURL(contentType, quality);
        
        resolve(optimizedBase64);
      };
      
      img.onerror = () => {
        console.error('Error loading image for optimization');
        // Return original image if optimization fails
        resolve(base64Image);
      };
      
      // Set the source of the image to the base64 string
      img.src = base64Image;
    } catch (error) {
      console.error('Error optimizing image:', error);
      // Return original image if optimization fails
      resolve(base64Image);
    }
  });
}

/**
 * Optimize a thumbnail image with smaller dimensions and lower quality
 * @param base64Image - Base64 encoded image string
 * @returns Promise with optimized thumbnail base64 image
 */
export async function optimizeThumbnail(base64Image: string): Promise<string> {
  // Thumbnails are optimized to 600x600 with 70% quality
  return optimizeImage(base64Image, 600, 600, 0.7);
}

/**
 * Optimize a featured image with larger dimensions and higher quality
 * @param base64Image - Base64 encoded image string
 * @returns Promise with optimized featured base64 image
 */
export async function optimizeFeaturedImage(base64Image: string): Promise<string> {
  // Featured images are optimized to 1600x1600 with 85% quality
  return optimizeImage(base64Image, 1600, 1600, 0.85);
}
