// Script to generate a small number of test posts for testing
const { initializeApp } = require('firebase/app');
const { getFirestore, collection, addDoc, Timestamp } = require('firebase/firestore');

// Initialize Firebase (make sure to use your own config)
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Sample Kurdish titles for test posts
const testTitles = [
  "بابەتی تاقیکردنەوە ١: تەکنەلۆژیای نوێ",
  "بابەتی تاقیکردنەوە ٢: کولتووری کوردی",
  "بابەتی تاقیکردنەوە ٣: گەشتیاری",
  "بابەتی تاقیکردنەوە ٤: وەرزش",
  "بابەتی تاقیکردنەوە ٥: خۆراک",
  "بابەتی تاقیکردنەوە ٦: هونەر",
  "بابەتی تاقیکردنەوە ٧: مێژوو",
  "بابەتی تاقیکردنەوە ٨: ژینگە",
  "بابەتی تاقیکردنەوە ٩: پەروەردە",
  "بابەتی تاقیکردنەوە ١٠: ئابووری"
];

// Sample categories
const categories = ["کولتوور", "مێژوو", "تەکنەلۆژیا", "ژینگە", "هونەر", "وەرزش", "خۆراک", "گەشتیاری", "پەروەردە", "ئابووری"];

// Sample tags
const tags = ["کوردستان", "کولتوور", "مێژوو", "تەکنەلۆژیا", "ژینگە", "هونەر", "وەرزش", "خۆراک", "گەشتیاری", "پەروەردە"];

// Sample content (Kurdish Lorem Ipsum)
const loremIpsum = `
<p>لۆرێم ئیپسوم دۆلۆر سیت ئامێت، کۆنسێکتێتور ئادیپیسینگ ئێلیت، سێد دۆ ئێیوسمۆد تێمپۆر ئینسیدیدونت ئوت لابۆرێ ئێت دۆلۆرێ ماگنا ئالیکوا. ئوت ئێنیم ئاد مینیم ڤێنیام، کویس نۆسترود ئێکسێرسیتاتیۆن ئوللامکۆ لابۆریس نیسی ئوت ئالیکویپ ئێکس ئێئا کۆممۆدۆ کۆنسێکوات.</p>

<p>سێد ئوت پێرسپیسیاتیس ئوندێ ئۆمنیس ئیستێ ناتوس ئێررۆر سیت ڤۆلوپتاتێم ئاککوسانتیوم دۆلۆرێمکوێ لاودانتیوم، تۆتام رێم ئاپێریام، ئێئاکوێ ئیپسا کوائێ ئاب ئیللۆ ئینڤێنتۆرێ ڤێریتاتیس ئێت کواسی ئارکیتێکتۆ بێئاتائێ ڤیتائێ دیکتا سونت ئێکسپلیکابۆ.</p>
`;

// Sample image URLs (placeholder images)
const imageUrls = [
  "https://via.placeholder.com/800x600/8f5826/ffffff?text=Test+Post+1",
  "https://via.placeholder.com/800x600/8f5826/ffffff?text=Test+Post+2",
  "https://via.placeholder.com/800x600/8f5826/ffffff?text=Test+Post+3",
  "https://via.placeholder.com/800x600/8f5826/ffffff?text=Test+Post+4",
  "https://via.placeholder.com/800x600/8f5826/ffffff?text=Test+Post+5"
];

// Function to get random items from an array
function getRandomItems(array, count) {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

// Function to get a random date within the last month
function getRandomDate() {
  const now = new Date();
  const pastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
  const randomTime = pastMonth.getTime() + Math.random() * (now.getTime() - pastMonth.getTime());
  return new Date(randomTime);
}

// Function to create a test post
async function createTestPost(index) {
  try {
    const title = testTitles[index];
    const category = categories[index % categories.length];
    const postTags = getRandomItems(tags, 3);
    const imageUrl = imageUrls[index % imageUrls.length];
    const date = Timestamp.fromDate(getRandomDate());
    const viewCount = Math.floor(Math.random() * 100) + 10;
    const isPrimary = index < 3; // Make the first 3 posts primary

    // Create post document
    const postData = {
      title,
      content: loremIpsum,
      thumbnailImage: imageUrl,
      featuredImage: imageUrl,
      category,
      tags: postTags,
      date,
      viewCount,
      status: 'بڵاوکراوەتەوە',
      isPrimary
    };

    const docRef = await addDoc(collection(db, 'posts'), postData);
    console.log(`Test post ${index + 1} created with ID: ${docRef.id}`);
    return docRef.id;
  } catch (error) {
    console.error(`Error creating test post ${index + 1}:`, error);
    return null;
  }
}

// Main function to create 10 test posts
async function createTestPosts() {
  console.log('Starting to create 10 test posts...');

  for (let i = 0; i < 10; i++) {
    await createTestPost(i);
    // Add a small delay between posts
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  console.log('All 10 test posts created successfully!');
}

// Run the script
createTestPosts();
