'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { collection, getDocs, query, orderBy, limit, where } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import styles from './TagsSection.module.css';

interface Tag {
  id: string;
  name: string;
  count: number;
  slug?: string;
}

export default function TagsSection() {
  const [tags, setTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAll, setShowAll] = useState(false);

  useEffect(() => {
    async function fetchTags() {
      try {
        setIsLoading(true);

        // Try to get tags from the tags collection first
        const tagsCollection = collection(db, 'tags');
        const tagsSnapshot = await getDocs(tagsCollection);

        if (!tagsSnapshot.empty) {
          // If we have tags in the tags collection, use those
          const tagsList = tagsSnapshot.docs
            .map(doc => ({
              id: doc.id,
              name: doc.data().name || doc.id,
              slug: doc.data().slug || doc.id,
              count: doc.data().count || 0
            }))
            .filter(tag => tag.count > 0)
            .sort((a, b) => b.count - a.count);

          setTags(tagsList);
          setIsLoading(false);
          return;
        }

        // Fallback: Count tags from posts
        const postsCollection = collection(db, 'posts');
        const postsQuery = query(
          postsCollection,
          where('status', '==', 'بڵاوکراوەتەوە'),
          limit(100) // Limit to avoid processing too many posts
        );
        const postsSnapshot = await getDocs(postsQuery);

        // Count tags from published posts
        const tagCounts: Record<string, number> = {};

        postsSnapshot.docs.forEach(doc => {
          const post = doc.data();
          if (post.tags && Array.isArray(post.tags)) {
            post.tags.forEach((tag: string) => {
              if (tag) {
                tagCounts[tag] = (tagCounts[tag] || 0) + 1;
              }
            });
          }
        });

        // Convert to array and sort by count
        const tagsList = Object.entries(tagCounts)
          .map(([name, count]) => ({
            id: name,
            name,
            count
          }))
          .sort((a, b) => b.count - a.count);

        setTags(tagsList);
      } catch (err) {
        console.error('Error fetching tags:', err);
      } finally {
        setIsLoading(false);
      }
    }

    fetchTags();
  }, []);

  // Show only 4 tags initially, or all if showAll is true
  const allDisplayedTags = showAll ? tags : tags.slice(0, 4);

  // Split into featured (first) and regular tags
  const featuredTag = allDisplayedTags.length > 0 ? allDisplayedTags[0] : null;
  const regularTags = allDisplayedTags.length > 1 ? allDisplayedTags.slice(1) : [];

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-center items-center h-20">
          <div className="inline-block w-6 h-6 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  if (tags.length === 0) {
    return null; // Don't show the section if there are no tags
  }

  return (
    <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="flex justify-between items-center mb-8">
        <h2 className="text-2xl font-bold text-black">تاگەکان</h2>
        {tags.length > 4 && (
          <button
            onClick={() => setShowAll(!showAll)}
            className="text-[var(--primary-600)] hover:text-[var(--primary-800)] transition-colors"
            type="button"
          >
            {showAll ? 'پیشاندانی کەمتر' : 'پیشاندانی هەموو'}
          </button>
        )}
      </div>

      <div className={styles.tagsContainer}>
        <div className={styles.tagsRow}>
          {featuredTag && (
            <Link
              key={featuredTag.id}
              href={`/tag/${featuredTag.slug || featuredTag.name}`}
              className={styles.featuredTag}
            >
              <span className={styles.featuredTagName}>{featuredTag.name}</span>
              <span className={styles.featuredTagCount}>
                {featuredTag.count}
              </span>
            </Link>
          )}

          <div className={styles.regularTags}>
            {regularTags.map((tag) => (
              <Link
                key={tag.id}
                href={`/tag/${tag.slug || tag.name}`}
                className={styles.tagCard}
              >
                <span className="font-medium text-gray-800">{tag.name}</span>
                <span className="bg-[var(--primary-100)] text-[var(--primary-700)] px-2 py-1 text-xs font-semibold rounded-full">
                  {tag.count}
                </span>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
