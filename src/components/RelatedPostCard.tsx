import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Timestamp } from 'firebase/firestore';
import RamiyariPlaceholder from './RamiyariPlaceholder';

interface RelatedPostProps {
  id: string;
  title: string;
  thumbnailImage?: string;
  date?: Timestamp;
}

export default function RelatedPostCard({ id, title, thumbnailImage, date }: RelatedPostProps) {
  const [imgError, setImgError] = useState(false);

  // Check if the image URL is valid
  const isValidImageUrl = thumbnailImage &&
    (thumbnailImage.startsWith('http://') || thumbnailImage.startsWith('https://'));

  // Check if it's a Firebase Storage URL
  const isFirebaseStorageUrl = isValidImageUrl &&
    thumbnailImage && thumbnailImage.includes('firebasestorage.googleapis.com');

  // Check if we have a valid image to display
  const hasValidImage = isValidImageUrl && !imgError;

  return (
    <Link href={`/post/${id}`} className="group h-full">
      <div className="bg-[#f9f5f0] rounded-lg overflow-hidden border-2 border-[#8f5826] shadow-[0_4px_12px_rgba(143,88,38,0.2)] hover:shadow-[0_8px_20px_rgba(143,88,38,0.3)] transition-all duration-300 h-full flex flex-col">
        <div className="relative h-40 w-full bg-gray-100">
          {hasValidImage ? (
            <Image
              key={thumbnailImage} // Add key to force re-render when URL changes
              src={thumbnailImage!}
              alt={title}
              fill
              sizes="(max-width: 768px) 33vw, 300px"
              className="object-cover"
              loading="lazy"
              onError={() => {
                console.error('Error loading related post image:', thumbnailImage);
                setImgError(true);
              }}
              unoptimized={isFirebaseStorageUrl ? true : undefined} // Skip Next.js optimization for Firebase Storage URLs
            />
          ) : (
            <RamiyariPlaceholder />
          )}
        </div>
        <div className="p-4 flex flex-col flex-grow">
          <h4 className="text-md font-bold text-black mb-2 group-hover:text-[#8f5826] transition-colors line-clamp-2" translate="yes">
            {title}
          </h4>
          <div className="text-sm text-gray-500 mt-auto" translate="no">
            {date ? new Date(date.toDate()).toLocaleDateString('ku-IQ') : ''}
          </div>
        </div>
      </div>
    </Link>
  );
}
