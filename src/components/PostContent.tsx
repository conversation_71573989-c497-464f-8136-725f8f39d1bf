'use client';

import { useEffect, useRef } from 'react';

interface PostContentProps {
  content: string;
}

export default function PostContent({ content }: PostContentProps) {
  const contentRef = useRef<HTMLDivElement>(null);

  // Minimal DOM manipulation to avoid interfering with Google Translate
  useEffect(() => {
    if (contentRef.current) {
      const images = contentRef.current.querySelectorAll('img');

      images.forEach((img) => {
        // Only add loading="lazy" for performance - minimal DOM change
        if (!img.hasAttribute('loading')) {
          img.setAttribute('loading', 'lazy');
        }

        // Use CSS classes instead of direct style manipulation
        if (!img.classList.contains('blog-image-processed')) {
          img.classList.add('blog-image-processed');
        }
      });

      // Notify Google Translate that content has changed
      if (typeof window !== 'undefined' && (window as any).google && (window as any).google.translate) {
        setTimeout(() => {
          try {
            // Trigger Google Translate to re-scan the page for new content
            const translateEvent = new Event('DOMContentLoaded');
            document.dispatchEvent(translateEvent);
          } catch (e) {
            // Silently fail if Google Translate is not available
          }
        }, 500);
      }
    }
  }, [content]);

  return (
    <article
      ref={contentRef}
      className="prose prose-lg max-w-none blog-content"
      translate="yes"
      lang="ckb"
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
}
