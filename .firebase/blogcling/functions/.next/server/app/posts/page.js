(()=>{var e={};e.id=530,e.ids=[530],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5394:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var r=t(60687),i=t(43210),a=t(30474),n=t(85814),o=t.n(n),l=t(75535),d=t(71557),c=t(34148),p=t(51317);function x(){let[e,s]=(0,i.useState)([]),[t,n]=(0,i.useState)(null),[x,u]=(0,i.useState)(!0),[m,h]=(0,i.useState)(!1),[f,v]=(0,i.useState)(!0),j=async()=>{if(t&&!m)try{h(!0);let r=(0,l.rJ)(d.db,"posts"),i=(0,l.P)(r,(0,l.My)("date","desc"),(0,l.HM)(t),(0,l.AB)(18)),a=await (0,l.GG)(i),o=a.docs.map(e=>({id:e.id,...e.data()})).filter(e=>"بڵاوکراوەتەوە"===e.status).slice(0,9);s([...e,...o]),a.docs.length>0&&n(a.docs[a.docs.length-1]),v(9===o.length)}catch(e){console.error("Error loading more posts:",e)}finally{h(!1)}};return(0,r.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,r.jsx)(c.default,{}),(0,r.jsx)("main",{className:"flex-grow",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-black mb-8",children:"هەموو بابەتەکان"}),x?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin"})}):0===e.length?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("p",{className:"text-xl text-gray-500",children:"هیچ بابەتێک نەدۆزرایەوە"})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,r.jsx)("div",{className:"bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow",children:(0,r.jsxs)(o(),{href:`/post/${e.id}`,children:[(0,r.jsx)("div",{className:"relative h-48",children:(0,r.jsx)(a.default,{src:e.thumbnailImage||"/placeholder.jpg",alt:e.title,fill:!0,className:"object-cover",unoptimized:!0})}),(0,r.jsxs)("div",{className:"p-4",children:[e.category&&(0,r.jsx)("span",{className:"inline-block bg-[var(--primary-100)] text-[var(--primary-700)] px-2 py-1 text-xs font-semibold rounded-md mb-2",children:e.category}),(0,r.jsx)("h3",{className:"text-lg font-bold text-black mb-2 line-clamp-2",children:e.title}),(0,r.jsxs)("div",{className:"flex items-center text-gray-500 text-sm mb-2",children:[(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),e.date?new Date(e.date.toDate()).toLocaleDateString("ku-IQ"):""]}),(0,r.jsx)("span",{className:"mx-2",children:"•"}),(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),e.viewCount||0," بینین"]})]}),(0,r.jsx)("div",{className:"text-sm text-gray-600 line-clamp-2",children:e.content?(0,r.jsx)("div",{dangerouslySetInnerHTML:{__html:e.content.replace(/<[^>]*>/g,"").substring(0,150)+"..."}}):(0,r.jsx)("p",{children:"بێ ناوەڕۆک"})})]})]})},e.id))}),f&&(0,r.jsx)("div",{className:"mt-8 flex justify-center",children:(0,r.jsx)("button",{type:"button",onClick:j,disabled:m,className:`px-6 py-3 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors font-medium ${m?"opacity-70 cursor-not-allowed":"cursor-pointer"}`,children:m?(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"}),(0,r.jsx)("span",{children:"بارکردن..."})]}):"بابەتی زیاتر"})})]})]})}),(0,r.jsx)(p.default,{})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22051:(e,s,t)=>{Promise.resolve().then(t.bind(t,41320))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36123:(e,s,t)=>{Promise.resolve().then(t.bind(t,5394))},37366:e=>{"use strict";e.exports=require("dns")},41320:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/posts/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/posts/page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83056:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(65239),i=t(48088),a=t(88170),n=t.n(a),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let d={children:["",{children:["posts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,41320)),"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/posts/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/posts/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/posts/page",pathname:"/posts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,724,934,814,474,819],()=>t(83056));module.exports=r})();