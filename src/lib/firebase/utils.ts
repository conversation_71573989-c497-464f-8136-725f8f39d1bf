import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  DocumentData
} from 'firebase/firestore';
import { db } from './config';

/**
 * Get a document from Firestore by ID
 */
export async function getDocumentById(collectionName: string, id: string) {
  const docRef = doc(db, collectionName, id);
  const docSnap = await getDoc(docRef);

  if (docSnap.exists()) {
    return { id: docSnap.id, ...docSnap.data() };
  } else {
    return null;
  }
}

/**
 * Get all documents from a collection
 */
export async function getCollection(collectionName: string) {
  const querySnapshot = await getDocs(collection(db, collectionName));
  return querySnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data()
  }));
}

/**
 * Create or update a document in Firestore
 */
export async function setDocument(collectionName: string, id: string, data: DocumentData) {
  const docRef = doc(db, collectionName, id);
  return setDoc(docRef, data, { merge: true });
}

/**
 * Update a document in Firestore
 */
export async function updateDocument(collectionName: string, id: string, data: DocumentData) {
  const docRef = doc(db, collectionName, id);
  return updateDoc(docRef, data);
}

/**
 * Delete a document from Firestore
 */
export async function deleteDocument(collectionName: string, id: string) {
  const docRef = doc(db, collectionName, id);
  return deleteDoc(docRef);
}

/**
 * Query documents from a collection
 */
export async function queryDocuments(
  collectionName: string,
  conditions: { field: string; operator: string; value: any }[] = [],
  sortBy: { field: string; direction: 'asc' | 'desc' }[] = [],
  limitTo: number | null = null
) {
  const collectionRef = collection(db, collectionName);

  // Start with a base query
  let q = query(collectionRef);

  // Add where conditions
  if (conditions.length > 0) {
    conditions.forEach(condition => {
      q = query(q, where(condition.field, condition.operator as any, condition.value));
    });
  }

  // Add orderBy
  if (sortBy.length > 0) {
    sortBy.forEach(sort => {
      q = query(q, orderBy(sort.field, sort.direction));
    });
  }

  // Add limit
  if (limitTo) {
    q = query(q, limit(limitTo));
  }

  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data()
  }));
}
