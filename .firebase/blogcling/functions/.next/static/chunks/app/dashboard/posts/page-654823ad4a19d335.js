(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[261],{3899:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var a=r(5155),s=r(2115),n=r(5695),o=r(6874),i=r.n(o),l=r(7961),c=r(8997),d=r(5317),m=r(7039),h=r(5946);function p(){let{user:e,loading:t}=(0,l.A)(),r=(0,n.useRouter)(),[o,p]=(0,s.useState)([]),[u,x]=(0,s.useState)(!0),[g,f]=(0,s.useState)(null),w=async()=>{try{x(!0);let e=(0,d.rJ)(m.db,"posts"),t=(0,d.P)(e,(0,d.My)("createdAt","desc")),r=(await (0,d.GG)(t)).docs.map(e=>({id:e.id,...e.data()}));p(r),f(null)}catch(e){console.error("Error fetching posts:",e),f("هەڵە لە هێنانی بابەتەکان")}finally{x(!1)}};(0,s.useEffect)(()=>{e&&w()},[e]);let b=async e=>{if(confirm("ئایا دڵنیایت دەتەوێت ئەم بابەتە بسڕیتەوە؟"))try{let t=(0,d.H9)(m.db,"posts",e),r=await (0,d.x7)(t);if(r.exists()){let a=r.data(),s=[];a.featuredImage&&"string"==typeof a.featuredImage&&s.push((0,h.jn)(a.featuredImage)),a.thumbnailImage&&"string"==typeof a.thumbnailImage&&s.push((0,h.jn)(a.thumbnailImage)),await Promise.all(s),await (0,d.kd)(t),p(o.filter(t=>t.id!==e)),alert("بابەت بە سەرکەوتوویی سڕایەوە")}else throw Error("Post not found")}catch(e){console.error("Error deleting post:",e),f("هەڵە لە سڕینەوەی بابەت")}},v=e=>{r.push("/dashboard/posts/edit/".concat(e))};if((0,s.useEffect)(()=>{t||e||r.push("/login")},[e,t,r]),t)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("p",{className:"text-xl",children:"باری ڕاستاندنەکە بارکردن..."})});if(!e)return null;let y=e=>{if(!e)return"N/A";try{return e.toDate().toLocaleDateString("ku",{year:"numeric",month:"long",day:"numeric"})}catch(e){return console.error("Error formatting date:",e),"N/A"}};return(0,a.jsx)(c.A,{children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-black",children:"بابەتەکان"}),(0,a.jsx)(i(),{href:"/dashboard/posts/new",className:"px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors font-medium",style:{color:"white"},children:"بابەتی نوێ"})]}),g&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md",children:g}),u?(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[(0,a.jsx)("div",{className:"inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin mb-4"}),(0,a.jsx)("p",{className:"text-black",children:"بارکردنی بابەتەکان..."})]}):0===o.length?(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[(0,a.jsx)("div",{className:"text-5xl mb-4",children:"\uD83D\uDCDD"}),(0,a.jsx)("h3",{className:"text-xl font-bold text-black mb-2",children:"هیچ بابەتێک نییە"}),(0,a.jsx)("p",{className:"text-black mb-4",children:'دەتوانیت یەکەم بابەت دروست بکەیت بە کرتەکردن لە دوگمەی "بابەتی نوێ"'}),(0,a.jsx)(i(),{href:"/dashboard/posts/new",className:"inline-block px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors font-medium cursor-pointer",style:{color:"white"},children:"بابەتی نوێ"})]}):(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-[var(--primary-50)]",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider",children:"وێنە"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider",children:"ناونیشان"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider",children:"دۆخ"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider",children:"بەروار"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider",children:"کردارەکان"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:o.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"flex items-center justify-center",children:e.thumbnailImage?(0,a.jsx)("div",{className:"h-16 w-16 relative rounded-md overflow-hidden",children:(0,a.jsx)("img",{src:e.thumbnailImage,alt:e.title,className:"h-full w-full object-cover"})}):(0,a.jsx)("div",{className:"h-16 w-16 bg-gray-200 flex items-center justify-center rounded-md",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})})})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-black",children:e.title}),void 0!==e.viewCount&&(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:(0,a.jsxs)("span",{className:"inline-flex items-center",children:[(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3 w-3 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),e.viewCount," بینین"]})})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("بڵاوکراوەتەوە"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-black",children:y(e.date)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-3 space-x-reverse",children:[(0,a.jsx)("button",{type:"button",onClick:()=>v(e.id),className:"text-[var(--primary-600)] hover:text-[var(--primary-800)] transition-colors p-2 rounded-full hover:bg-[var(--primary-50)] cursor-pointer",title:"دەستکاری",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),(0,a.jsx)("button",{type:"button",onClick:()=>b(e.id),className:"text-[var(--danger-600)] hover:text-[var(--danger-800)] transition-colors p-2 rounded-full hover:bg-[var(--danger-50)] cursor-pointer",title:"سڕینەوە",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})})]},e.id))})]})})})]})})}},5946:(e,t,r)=>{"use strict";r.d(t,{jn:()=>d,If:()=>i,V1:()=>c,PX:()=>l});var a=r(858),s=r(7039);async function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1200,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1200,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.85;return new Promise((s,n)=>{try{let o=new Image;o.onload=()=>{let i=o.width,l=o.height;if(i>t||l>r){let e=i/l;i>t&&(l=(i=t)/e),l>r&&(i=(l=r)*e)}let c=document.createElement("canvas");c.width=i,c.height=l;let d=c.getContext("2d");if(!d)return void n(Error("Could not get canvas context"));d.drawImage(o,0,0,i,l);let m=e.split(";")[0].split(":")[1]||"image/jpeg",h=c.toDataURL(m,a);s(h)},o.onerror=()=>{console.error("Error loading image for optimization"),s(e)},o.src=e}catch(t){console.error("Error optimizing image:",t),s(e)}})}async function o(e,t,r,o,i,l){try{let c=e;if(o&&i)try{c=await n(e,o,i,l),console.log("Image optimized successfully")}catch(e){console.error("Error optimizing image:",e)}let d=c.match(/^data:([A-Za-z-+/]+);base64,(.+)$/);if(!d||3!==d.length)throw Error("Invalid base64 image format");let m=d[1],h=d[2],p="jpg";m.includes("png")?p="png":m.includes("gif")?p="gif":m.includes("webp")&&(p="webp");let u=r||"".concat(Date.now(),".").concat(p),x=(0,a.KR)(s.IG,"".concat(t,"/").concat(u));try{let e=await (0,a.ls)(x,h,"base64",{contentType:m,cacheControl:"public, max-age=31536000"}),t=await (0,a.qk)(e.ref);return console.log("Image uploaded successfully to Firebase Storage:",t),t}catch(e){return console.error("Firebase Storage upload failed:",e),e instanceof Error&&(e.message.includes("unauthorized")?(console.warn("Storage permission denied. Using base64 fallback. Please update Firebase Storage rules."),alert("تکایە ڕێساکانی Firebase Storage نوێ بکەرەوە بۆ ڕێگەدان بە بارکردنی وێنەکان")):e.message.includes("CORS")&&console.warn("CORS error detected. Using base64 fallback. Please configure CORS for Firebase Storage.")),console.warn("Falling back to storing image as base64 in Firestore"),c}}catch(t){return console.error("Error processing image:",t),console.warn("Error occurred, falling back to base64 storage"),e}}function i(e){let t=Date.now(),r=Math.random().toString(36).substring(2,8);if(e){let a=e.split(".").pop()||"jpg";return"".concat(t,"-").concat(r,".").concat(a)}return"".concat(t,"-").concat(r,".jpg")}async function l(e,t){return o(e,"posts/thumbnails",t,600,600,.7)}async function c(e,t){return o(e,"posts/featured",t,1600,1600,.85)}async function d(e){try{var t;if(!e||!e.includes("firebasestorage.googleapis.com"))return console.warn("Not a Firebase Storage URL:",e),!1;let r=new URL(e),n=decodeURIComponent(null==(t=r.pathname.split("/o/")[1])?void 0:t.split("?")[0]);if(!n)return console.warn("Could not extract path from URL:",e),!1;let o=(0,a.KR)(s.IG,n);return await (0,a.XR)(o),console.log("File deleted successfully:",n),!0}catch(e){return console.error("Error deleting file:",e),!1}}},8834:(e,t,r)=>{Promise.resolve().then(r.bind(r,3899))}},e=>{var t=t=>e(e.s=t);e.O(0,[992,118,288,244,997,441,684,358],()=>t(8834)),_N_E=e.O()}]);