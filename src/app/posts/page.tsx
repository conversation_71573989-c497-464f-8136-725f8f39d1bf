'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { collection, query, where, getDocs, orderBy, limit, startAfter, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';

interface Post {
  id: string;
  title: string;
  content: string;
  thumbnailImage: string;
  category?: string;
  date: Timestamp;
  viewCount: number;
  status: string;
}

const POSTS_PER_PAGE = 9;

export default function PostsPage() {
  const [posts, setPosts] = useState<Post[]>([]);
  const [lastVisible, setLastVisible] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    async function fetchPosts() {
      try {
        setIsLoading(true);
        const postsCollection = collection(db, 'posts');

        // Use a simpler query that doesn't require the composite index
        const fallbackQuery = query(
          postsCollection,
          orderBy('date', 'desc'),
          limit(POSTS_PER_PAGE * 2)
        );

        const fallbackSnapshot = await getDocs(fallbackQuery);
        const fallbackList = fallbackSnapshot.docs
          .map(doc => ({
            id: doc.id,
            ...doc.data()
          } as Post))
          .filter(post => post.status === 'بڵاوکراوەتەوە')
          .slice(0, POSTS_PER_PAGE);

        setPosts(fallbackList);
        setLastVisible(fallbackSnapshot.docs[fallbackSnapshot.docs.length - 1]);
        setHasMore(fallbackList.length === POSTS_PER_PAGE);
      } catch (err) {
        console.error('Error fetching posts:', err);
      } finally {
        setIsLoading(false);
      }
    }

    fetchPosts();
  }, []);

  const loadMorePosts = async () => {
    if (!lastVisible || isLoadingMore) return;

    try {
      setIsLoadingMore(true);
      const postsCollection = collection(db, 'posts');

      // Use a simpler query that doesn't require the composite index
      const fallbackQuery = query(
        postsCollection,
        orderBy('date', 'desc'),
        startAfter(lastVisible),
        limit(POSTS_PER_PAGE * 2)
      );

      const fallbackSnapshot = await getDocs(fallbackQuery);
      const fallbackList = fallbackSnapshot.docs
        .map(doc => ({
          id: doc.id,
          ...doc.data()
        } as Post))
        .filter(post => post.status === 'بڵاوکراوەتەوە')
        .slice(0, POSTS_PER_PAGE);

      setPosts([...posts, ...fallbackList]);

      if (fallbackSnapshot.docs.length > 0) {
        setLastVisible(fallbackSnapshot.docs[fallbackSnapshot.docs.length - 1]);
      }

      setHasMore(fallbackList.length === POSTS_PER_PAGE);
    } catch (err) {
      console.error('Error loading more posts:', err);
    } finally {
      setIsLoadingMore(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <h1 className="text-3xl font-bold text-black mb-8">هەموو بابەتەکان</h1>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin"></div>
            </div>
          ) : posts.length === 0 ? (
            <div className="flex justify-center items-center h-64">
              <p className="text-xl text-gray-500">هیچ بابەتێک نەدۆزرایەوە</p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {posts.map((post) => (
                  <div key={post.id} className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                    <Link href={`/post/${post.id}`}>
                      <div className="relative h-48">
                        <Image
                          src={post.thumbnailImage || '/placeholder.jpg'}
                          alt={post.title}
                          fill
                          className="object-cover"
                          unoptimized={true} // Skip Next.js optimization for all images
                        />
                      </div>
                      <div className="p-4">
                        {post.category && (
                          <span className="inline-block bg-[var(--primary-100)] text-[var(--primary-700)] px-2 py-1 text-xs font-semibold rounded-md mb-2">
                            {post.category}
                          </span>
                        )}
                        <h3 className="text-lg font-bold text-black mb-2 line-clamp-2">{post.title}</h3>
                        <div className="flex items-center text-gray-500 text-sm mb-2">
                          <span className="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            {post.date ? new Date(post.date.toDate()).toLocaleDateString('ku-IQ') : ''}
                          </span>
                          <span className="mx-2">•</span>
                          <span className="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            {post.viewCount || 0} بینین
                          </span>
                        </div>
                        <div className="text-sm text-gray-600 line-clamp-2">
                          {post.content ? (
                            <div dangerouslySetInnerHTML={{ __html: post.content.replace(/<[^>]*>/g, '').substring(0, 150) + '...' }} />
                          ) : (
                            <p>بێ ناوەڕۆک</p>
                          )}
                        </div>
                      </div>
                    </Link>
                  </div>
                ))}
              </div>

              {hasMore && (
                <div className="mt-8 flex justify-center">
                  <button
                    type="button"
                    onClick={loadMorePosts}
                    disabled={isLoadingMore}
                    className={`px-6 py-3 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors font-medium ${isLoadingMore ? 'opacity-70 cursor-not-allowed' : 'cursor-pointer'}`}
                  >
                    {isLoadingMore ? (
                      <span className="flex items-center">
                        <span className="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"></span>
                        <span>بارکردن...</span>
                      </span>
                    ) : (
                      'بابەتی زیاتر'
                    )}
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
}
