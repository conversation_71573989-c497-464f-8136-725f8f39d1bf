'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import styles from './CategoriesSection.module.css';

interface Category {
  id: string;
  name: string;
  slug: string;
  count: number;
}

export default function CategoriesSection() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAll, setShowAll] = useState(false);

  useEffect(() => {
    async function fetchCategories() {
      try {
        setIsLoading(true);

        // Fetch categories from Firestore
        const categoriesCollection = collection(db, 'categories');
        const categoriesSnapshot = await getDocs(categoriesCollection);

        if (!categoriesSnapshot.empty) {
          const categoriesList = categoriesSnapshot.docs
            .map(doc => ({
              id: doc.id,
              name: doc.data().name || doc.id,
              slug: doc.data().slug || doc.id,
              count: doc.data().count || 0
            }))
            .filter(category => category.count > 0)
            .sort((a, b) => b.count - a.count);

          setCategories(categoriesList);
        }
      } catch (err) {
        console.error('Error fetching categories:', err);
      } finally {
        setIsLoading(false);
      }
    }

    fetchCategories();
  }, []);

  // Show only 4 categories initially, or all if showAll is true
  const allDisplayedCategories = showAll ? categories : categories.slice(0, 4);
  
  // Split into featured (first) and regular categories
  const featuredCategory = allDisplayedCategories.length > 0 ? allDisplayedCategories[0] : null;
  const regularCategories = allDisplayedCategories.length > 1 ? allDisplayedCategories.slice(1) : [];

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-center items-center h-20">
          <div className="inline-block w-6 h-6 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  if (categories.length === 0) {
    return null; // Don't show the section if there are no categories
  }

  return (
    <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="flex justify-between items-center mb-8">
        <h2 className="text-2xl font-bold text-black">پۆلەکان</h2>
        {categories.length > 4 && (
          <button
            onClick={() => setShowAll(!showAll)}
            className="text-[var(--primary-600)] hover:text-[var(--primary-800)] transition-colors"
            type="button"
          >
            {showAll ? 'پیشاندانی کەمتر' : 'پیشاندانی هەموو'}
          </button>
        )}
      </div>

      <div className={styles.categoriesContainer}>
        <div className={styles.categoriesRow}>
          {featuredCategory && (
            <Link
              key={featuredCategory.id}
              href={`/category/${featuredCategory.slug}`}
              className={styles.featuredCategory}
            >
              <span className={styles.featuredCategoryName}>{featuredCategory.name}</span>
              <span className={styles.featuredCategoryCount}>
                {featuredCategory.count}
              </span>
            </Link>
          )}

          <div className={styles.regularCategories}>
            {regularCategories.map((category) => (
              <Link
                key={category.id}
                href={`/category/${category.slug}`}
                className={styles.categoryCard}
              >
                <span className="font-medium text-gray-800">{category.name}</span>
                <span className="bg-[var(--primary-100)] text-[var(--primary-700)] px-2 py-1 text-xs font-semibold rounded-full">
                  {category.count}
                </span>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
