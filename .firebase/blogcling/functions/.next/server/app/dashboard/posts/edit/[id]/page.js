(()=>{var e={};e.id=376,e.ids=[376],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21810:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/dashboard/posts/edit/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/dashboard/posts/edit/[id]/page.tsx","default")},21820:e=>{"use strict";e.exports=require("os")},24665:(e,r,t)=>{"use strict";t.d(r,{jn:()=>c,If:()=>l,V1:()=>d,PX:()=>n});var a=t(70146),s=t(71557);async function o(e,r=1200,t=1200,a=.85){return new Promise((s,o)=>{try{let i=new Image;i.onload=()=>{let l=i.width,n=i.height;if(l>r||n>t){let e=l/n;l>r&&(n=(l=r)/e),n>t&&(l=(n=t)*e)}let d=document.createElement("canvas");d.width=l,d.height=n;let c=d.getContext("2d");if(!c)return void o(Error("Could not get canvas context"));c.drawImage(i,0,0,l,n);let m=e.split(";")[0].split(":")[1]||"image/jpeg",p=d.toDataURL(m,a);s(p)},i.onerror=()=>{console.error("Error loading image for optimization"),s(e)},i.src=e}catch(r){console.error("Error optimizing image:",r),s(e)}})}async function i(e,r,t,i,l,n){try{let d=e;if(i&&l)try{d=await o(e,i,l,n),console.log("Image optimized successfully")}catch(e){console.error("Error optimizing image:",e)}let c=d.match(/^data:([A-Za-z-+/]+);base64,(.+)$/);if(!c||3!==c.length)throw Error("Invalid base64 image format");let m=c[1],p=c[2],u="jpg";m.includes("png")?u="png":m.includes("gif")?u="gif":m.includes("webp")&&(u="webp");let h=t||`${Date.now()}.${u}`,g=(0,a.KR)(s.IG,`${r}/${h}`);try{let e=await (0,a.ls)(g,p,"base64",{contentType:m,cacheControl:"public, max-age=31536000"}),r=await (0,a.qk)(e.ref);return console.log("Image uploaded successfully to Firebase Storage:",r),r}catch(e){return console.error("Firebase Storage upload failed:",e),e instanceof Error&&(e.message.includes("unauthorized")?(console.warn("Storage permission denied. Using base64 fallback. Please update Firebase Storage rules."),alert("تکایە ڕێساکانی Firebase Storage نوێ بکەرەوە بۆ ڕێگەدان بە بارکردنی وێنەکان")):e.message.includes("CORS")&&console.warn("CORS error detected. Using base64 fallback. Please configure CORS for Firebase Storage.")),console.warn("Falling back to storing image as base64 in Firestore"),d}}catch(r){return console.error("Error processing image:",r),console.warn("Error occurred, falling back to base64 storage"),e}}function l(e){let r=Date.now(),t=Math.random().toString(36).substring(2,8);if(e){let a=e.split(".").pop()||"jpg";return`${r}-${t}.${a}`}return`${r}-${t}.jpg`}async function n(e,r){return i(e,"posts/thumbnails",r,600,600,.7)}async function d(e,r){return i(e,"posts/featured",r,1600,1600,.85)}async function c(e){try{if(!e||!e.includes("firebasestorage.googleapis.com"))return console.warn("Not a Firebase Storage URL:",e),!1;let r=new URL(e),t=decodeURIComponent(r.pathname.split("/o/")[1]?.split("?")[0]);if(!t)return console.warn("Could not extract path from URL:",e),!1;let o=(0,a.KR)(s.IG,t);return await (0,a.XR)(o),console.log("File deleted successfully:",t),!0}catch(e){return console.error("Error deleting file:",e),!1}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32773:()=>{},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35201:(e,r,t)=>{Promise.resolve().then(t.bind(t,21810))},37366:e=>{"use strict";e.exports=require("dns")},48353:(e,r,t)=>{Promise.resolve().then(t.bind(t,50003))},50003:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var a=t(60687),s=t(43210),o=t(16189),i=t(30474),l=t(24157),n=t(31596),d=t(74932),c=t(75535),m=t(71557),p=t(24665);function u({id:e}){let{user:r,loading:t}=(0,l.A)(),u=(0,o.useRouter)(),[h,g]=(0,s.useState)(null),[x,b]=(0,s.useState)(""),[f,v]=(0,s.useState)(""),[y,j]=(0,s.useState)(""),[w,N]=(0,s.useState)(""),[k,C]=(0,s.useState)("draft"),[S,E]=(0,s.useState)(!1),[z,F]=(0,s.useState)(null),[I,P]=(0,s.useState)(null),[L,q]=(0,s.useState)(null),[A,R]=(0,s.useState)(null),[_,D]=(0,s.useState)(0),[M,U]=(0,s.useState)(new Date().toISOString().split("T")[0]),[W,$]=(0,s.useState)(!0),[B,G]=(0,s.useState)(!1),[K,O]=(0,s.useState)(null),[T,H]=(0,s.useState)([]),[V,X]=(0,s.useState)([]),[Z,J]=(0,s.useState)(!0),[Q,Y]=(0,s.useState)(!0),ee=(e,r)=>{let t=e.target.files?.[0];if(t){let e=new FileReader;e.onloadend=()=>{r(e.result)},e.readAsDataURL(t)}},er=async t=>{t.preventDefault(),G(!0),O(null);try{if(!x.trim())throw Error("تکایە ناونیشانێک بنووسە");if(!f.trim())throw Error("تکایە ناوەڕۆکێک بنووسە");if(!r)throw Error("تکایە دووبارە چوونەژوورەوە بکە");let t=w.split(",").map(e=>e.trim()).filter(e=>e.length>0),a=z,s=I;if(z!==L){if(L&&L.includes("firebasestorage.googleapis.com")&&z!==L)try{z&&z.startsWith("data:image")&&(await (0,p.jn)(L),console.log("Original featured image deleted from storage"))}catch(e){console.error("Error deleting original featured image:",e)}if(z&&z.startsWith("data:image"))try{let e=(0,p.If)("featured.jpg");a=await (0,p.V1)(z,e),console.log("Featured image uploaded and optimized:",a)}catch(e){throw console.error("Error uploading featured image:",e),Error("هەڵە لە بارکردنی وێنەی سەرەکی")}}if(I!==A){if(A&&A.includes("firebasestorage.googleapis.com")&&I!==A)try{I&&I.startsWith("data:image")&&(await (0,p.jn)(A),console.log("Original thumbnail image deleted from storage"))}catch(e){console.error("Error deleting original thumbnail image:",e)}if(I&&I.startsWith("data:image"))try{let e=(0,p.If)("thumbnail.jpg");s=await (0,p.PX)(I,e),console.log("Thumbnail image uploaded and optimized:",s)}catch(e){throw console.error("Error uploading thumbnail image:",e),Error("هەڵە لە بارکردنی وێنەی بچووک")}}console.log("Saving content with dimensions:",f);let o={title:x,content:f,category:y,tags:t,status:"publish"===k?"بڵاوکراوەتەوە":"پێشنووس",isPrimary:S,featuredImage:a,thumbnailImage:s,viewCount:_,date:c.Dc.fromDate(new Date(M)),updatedAt:c.Dc.now()},i=(0,c.H9)(m.db,"posts",e);await (0,c.mZ)(i,o),alert("بابەت بە سەرکەوتوویی نوێ کرایەوە!"),u.push("/dashboard/posts")}catch(e){console.error("Error updating post:",e),O(e.message||"هەڵەیەک ڕوویدا لە نوێکردنەوەی بابەت")}finally{G(!1)}};return t||W?(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"min-h-screen flex items-center justify-center",children:[(0,a.jsx)("div",{className:"inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin mb-4"}),(0,a.jsx)("p",{className:"text-xl mr-3",children:"بارکردنی بابەت..."})]})}):r?K&&!h?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"max-w-5xl mx-auto",children:(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 p-6 rounded-md",children:[(0,a.jsx)("h2",{className:"text-xl font-bold mb-2",children:"هەڵە"}),(0,a.jsx)("p",{children:K}),(0,a.jsx)("button",{type:"button",onClick:()=>u.push("/dashboard/posts"),className:"mt-4 px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors cursor-pointer",children:"گەڕانەوە بۆ لیستی بابەتەکان"})]})})}):(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"max-w-5xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-black",children:"دەستکاریکردنی بابەت"}),(0,a.jsx)("button",{type:"button",onClick:()=>u.push("/dashboard/posts"),className:"px-4 py-2 bg-[var(--gray-500)] text-white rounded-md hover:bg-[var(--gray-600)] transition-colors cursor-pointer",children:"گەڕانەوە"})]}),(0,a.jsxs)("div",{className:"bg-[var(--card-bg)] rounded-lg shadow-md border border-[var(--card-border)] p-6",children:[K&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md",children:K}),(0,a.jsxs)("form",{onSubmit:er,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"title",className:"block text-black text-sm font-bold mb-2",children:"ناونیشان"}),(0,a.jsx)("input",{type:"text",id:"title",value:x,onChange:e=>b(e.target.value),className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors",required:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-black text-sm font-bold mb-2",children:"وێنەی سەرەکی"}),(0,a.jsxs)("div",{className:"border-2 border-dashed border-[var(--gray-300)] rounded-md p-4 text-center",children:[z?(0,a.jsxs)("div",{className:"relative h-48 mb-2",children:[(0,a.jsx)(i.default,{src:z,alt:"Featured",fill:!0,style:{objectFit:"cover"},className:"rounded-md"}),(0,a.jsx)("button",{type:"button",onClick:async()=>{if(z&&z===L&&z.includes("firebasestorage.googleapis.com"))try{await (0,p.jn)(z),console.log("Featured image deleted from storage")}catch(e){console.error("Error deleting featured image:",e)}F(null)},className:"absolute top-2 left-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors cursor-pointer",title:"سڕینەوەی وێنە","aria-label":"سڕینەوەی وێنە",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}):(0,a.jsxs)("div",{className:"h-48 flex flex-col items-center justify-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-[var(--gray-400)]",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),(0,a.jsx)("p",{className:"mt-2 text-sm text-black",children:"کرتە بکە بۆ هەڵبژاردنی وێنە"})]}),(0,a.jsx)("input",{type:"file",id:"featuredImage",accept:"image/*",className:"hidden",onChange:e=>ee(e,F)}),(0,a.jsx)("label",{htmlFor:"featuredImage",className:"mt-2 inline-block px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors cursor-pointer",children:z?"گۆڕینی وێنە":"هەڵبژاردنی وێنە"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-black text-sm font-bold mb-2",children:"وێنەی بچووک"}),(0,a.jsxs)("div",{className:"border-2 border-dashed border-[var(--gray-300)] rounded-md p-4 text-center",children:[I?(0,a.jsxs)("div",{className:"relative h-48 mb-2",children:[(0,a.jsx)(i.default,{src:I,alt:"Thumbnail",fill:!0,style:{objectFit:"cover"},className:"rounded-md"}),(0,a.jsx)("button",{type:"button",onClick:async()=>{if(I&&I===A&&I.includes("firebasestorage.googleapis.com"))try{await (0,p.jn)(I),console.log("Thumbnail image deleted from storage")}catch(e){console.error("Error deleting thumbnail image:",e)}P(null)},className:"absolute top-2 left-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors cursor-pointer",title:"سڕینەوەی وێنە","aria-label":"سڕینەوەی وێنە",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}):(0,a.jsxs)("div",{className:"h-48 flex flex-col items-center justify-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-[var(--gray-400)]",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),(0,a.jsx)("p",{className:"mt-2 text-sm text-black",children:"کرتە بکە بۆ هەڵبژاردنی وێنە"})]}),(0,a.jsx)("input",{type:"file",id:"thumbnailImage",accept:"image/*",className:"hidden",onChange:e=>ee(e,P)}),(0,a.jsx)("label",{htmlFor:"thumbnailImage",className:"mt-2 inline-block px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors cursor-pointer",children:I?"گۆڕینی وێنە":"هەڵبژاردنی وێنە"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"content",className:"block text-black text-sm font-bold mb-2",children:"ناوەڕۆک"}),(0,a.jsx)(d.A,{value:f,onChange:e=>{console.log("Editor content updated"),v(e)},placeholder:"ناوەڕۆک بنووسە...",height:650,width:"100%",rtl:!0})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"category",className:"block text-black text-sm font-bold mb-2",children:"پۆل"}),Z?(0,a.jsxs)("div",{className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md bg-gray-50 flex items-center",children:[(0,a.jsx)("div",{className:"inline-block w-4 h-4 border-2 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin ml-2"}),(0,a.jsx)("span",{className:"text-gray-500",children:"بارکردنی پۆلەکان..."})]}):(0,a.jsxs)("select",{id:"category",value:y,onChange:e=>j(e.target.value),className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors cursor-pointer",children:[(0,a.jsx)("option",{value:"",children:"هەڵبژاردنی پۆل"}),T.map(e=>(0,a.jsx)("option",{value:e.slug,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"tags",className:"block text-black text-sm font-bold mb-2",children:"تاگەکان"}),Q?(0,a.jsxs)("div",{className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md bg-gray-50 flex items-center",children:[(0,a.jsx)("div",{className:"inline-block w-4 h-4 border-2 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin ml-2"}),(0,a.jsx)("span",{className:"text-gray-500",children:"بارکردنی تاگەکان..."})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:w.split(",").map(e=>e.trim()).filter(e=>e.length>0).map((e,r)=>(0,a.jsxs)("span",{className:"bg-[var(--primary-100)] text-[var(--primary-700)] px-2 py-1 rounded-md text-sm flex items-center",children:[e,(0,a.jsx)("button",{type:"button",onClick:()=>{let e=w.split(",").map(e=>e.trim()).filter(e=>e.length>0);e.splice(r,1),N(e.join(", "))},className:"ml-1 text-[var(--primary-500)] hover:text-[var(--primary-700)] transition-colors",title:"سڕینەوەی تاگ","aria-label":"سڕینەوەی تاگ",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]},r))}),(0,a.jsx)("div",{className:"flex",children:(0,a.jsxs)("select",{id:"tagSelector",name:"tagSelector","aria-label":"هەڵبژاردنی تاگ",title:"هەڵبژاردنی تاگ",className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors cursor-pointer",onChange:e=>{if(e.target.value){let r=e.target.value,t=w.split(",").map(e=>e.trim()).filter(e=>e.length>0);t.includes(r)||N([...t,r].join(", ")),e.target.value=""}},children:[(0,a.jsx)("option",{value:"",children:"هەڵبژاردنی تاگ"}),V.map(e=>(0,a.jsx)("option",{value:e.name,children:e.name},e.id))]})})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"publishDate",className:"block text-black text-sm font-bold mb-2",children:"بەرواری بڵاوکردنەوە"}),(0,a.jsx)("input",{type:"date",id:"publishDate",value:M,onChange:e=>U(e.target.value),className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors cursor-pointer"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"viewCount",className:"block text-black text-sm font-bold mb-2",children:"ژمارەی بینەران"}),(0,a.jsx)("input",{type:"number",id:"viewCount",value:_,onChange:e=>D(parseInt(e.target.value)||0),min:"0",className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-black text-sm font-bold mb-2",children:"دۆخ"}),(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",id:"draft",name:"status",value:"draft",checked:"draft"===k,onChange:()=>C("draft"),className:"ml-2 h-4 w-4 text-[var(--primary-600)] cursor-pointer"}),(0,a.jsx)("label",{htmlFor:"draft",className:"ml-2 text-black cursor-pointer hover:text-[var(--primary-600)] transition-colors",children:"پێشنووس"})]}),(0,a.jsxs)("div",{className:"flex items-center mr-6",children:[(0,a.jsx)("input",{type:"radio",id:"publish",name:"status",value:"publish",checked:"publish"===k,onChange:()=>C("publish"),className:"ml-2 h-4 w-4 text-[var(--primary-600)] cursor-pointer"}),(0,a.jsx)("label",{htmlFor:"publish",className:"ml-2 text-black cursor-pointer hover:text-[var(--primary-600)] transition-colors",children:"بڵاوکردنەوە"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-black text-sm font-bold mb-2",children:"بابەتی سەرەکی"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:"relative inline-block w-10 ml-2 align-middle select-none transition duration-200 ease-in",children:[(0,a.jsx)("input",{type:"checkbox",name:"isPrimary",id:"isPrimary",checked:S,onChange:()=>E(!S),className:"toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"}),(0,a.jsx)("label",{htmlFor:"isPrimary",className:`toggle-label block overflow-hidden h-6 rounded-full cursor-pointer ${S?"bg-[var(--primary-600)]":"bg-gray-300"}`})]}),(0,a.jsx)("label",{htmlFor:"isPrimary",className:"text-black cursor-pointer hover:text-[var(--primary-600)] transition-colors",children:S?"بەڵێ":"نەخێر"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-[var(--gray-200)]",children:[(0,a.jsx)("button",{type:"submit",disabled:B,className:`px-6 py-3 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors font-medium flex items-center cursor-pointer ${B?"opacity-70 cursor-not-allowed":""}`,children:B?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"}),(0,a.jsx)("span",{children:"نوێکردنەوە..."})]}):"نوێکردنەوە"}),(0,a.jsx)("button",{type:"button",onClick:()=>u.push("/dashboard/posts"),className:"px-6 py-3 bg-[var(--gray-500)] text-white rounded-md hover:bg-[var(--gray-600)] transition-colors font-medium cursor-pointer",children:"پاشگەزبوونەوە"})]})]})]})]})}):null}function h({params:e}){let r=(0,s.use)(e);return(0,a.jsx)(u,{id:r.id})}t(32773)},55511:e=>{"use strict";e.exports=require("crypto")},60240:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=t(65239),s=t(48088),o=t(88170),i=t.n(o),l=t(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);t.d(r,n);let d={children:["",{children:["dashboard",{children:["posts",{children:["edit",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,21810)),"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/dashboard/posts/edit/[id]/page.tsx"]}]},{}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/dashboard/posts/edit/[id]/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/posts/edit/[id]/page",pathname:"/dashboard/posts/edit/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74932:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var a=t(60687),s=t(76180),o=t.n(s),i=t(43210);let l=(0,t(30036).default)(async()=>{},{loadableGenerated:{modules:["components/JoditEditor/index.tsx -> jodit-react"]},ssr:!1}),n=({value:e,onChange:r,placeholder:t="ناوەڕۆک بنووسە...",height:s=650,width:n="100%",readonly:d=!1,toolbar:c=!0,language:m="en",rtl:p=!0})=>{let u=(0,i.useRef)(null),[h,g]=(0,i.useState)(e||"");(0,i.useEffect)(()=>{g(e)},[e]);let x=(0,i.useMemo)(()=>({readonly:d,placeholder:t,height:s,width:n,toolbar:c,language:m,direction:p?"rtl":"ltr",toolbarButtonSize:"middle",buttons:["source","|","bold","italic","underline","strikethrough","|","font","fontsize","brush","paragraph","|","align","indent","outdent","|","ul","ol","|","table","link","image","video","file","|","left","center","right","justify","imageCaption","|","hr","eraser","copyformat","|","superscript","subscript","|","selectall","cut","copy","paste","|","symbol","fullsize","print","about"],uploader:{insertImageAsBase64URI:!0},resizer:{min_width:10,min_height:10,showSize:!0},imageeditor:{crop:!0,resize:!0,resizeWidth:!0,resizeHeight:!0,ratio:!0},extraButtons:[{name:"imageCaption",tooltip:"Add caption to image",icon:"paragraph",exec:e=>{let r=e.selection.current();if(r&&"img"===r.nodeName.toLowerCase())try{let t=document.createElement("figure");t.className="image-with-caption",t.style.float="right",t.style.margin="10px 0 10px 15px",t.style.maxWidth="300px";let a=r.cloneNode(!0);a.style.float="none",a.style.margin="0",a.style.maxWidth="100%";let s=document.createElement("figcaption");s.innerHTML="وەسفی وێنە لێرە بنووسە...",s.style.textAlign="center",s.style.padding="5px",s.style.fontSize="14px",s.style.color="#666",t.appendChild(a),t.appendChild(s),e.selection.insertNode(t),r.parentNode&&r.parentNode.removeChild(r),e.selection.setCursorIn(s),console.log("Caption added successfully")}catch(e){console.error("Error adding caption:",e)}else alert("لطفا سەرەتا وێنەیەک دیاری بکە")}}],events:{afterInit:e=>{e.editor.style.direction="rtl",e.events.on("insertImage",e=>{e.style.float="right",e.style.margin="10px 0 10px 15px",e.style.maxWidth="300px"})}},style:{font:"Noto Kufi Arabic, sans-serif"}}),[d,t,s,n,c,m,p]);return(0,a.jsxs)("div",{className:"jsx-8cd3b8c15bb30d49 jodit-editor-wrapper",children:[(0,a.jsx)(l,{ref:u,value:h,config:x,tabIndex:1,onBlur:e=>{g(e),r(e)},onChange:e=>g(e)}),(0,a.jsx)(o(),{id:"8cd3b8c15bb30d49",children:'.jodit-editor-wrapper{margin-bottom:20px}.jodit-container{-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;font-family:"Noto Kufi Arabic",sans-serif;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px rgba(0,0,0,.1);box-shadow:0 1px 3px rgba(0,0,0,.1)}.jodit-wysiwyg{font-family:"Noto Kufi Arabic",sans-serif;padding:16px!important;font-size:16px!important;line-height:1.6!important}.jodit-toolbar__box{background-color:#f8f9fa;padding:8px!important}.jodit-toolbar-button{margin:0 3px;padding:6px!important}.jodit-toolbar-button__icon{width:20px!important;height:20px!important}.jodit-placeholder{font-family:"Noto Kufi Arabic",sans-serif;font-size:16px!important}.jodit-resizer{border:2px solid#8f5826!important}.jodit-resizer-point{background-color:#8f5826!important}.jodit-wysiwyg img{max-width:100%;height:auto;float:right;margin:10px 0 10px 15px}.jodit-wysiwyg p{overflow:auto}.jodit-wysiwyg p:after{content:"";display:table;clear:both}.jodit-wysiwyg{overflow:auto}.jodit-wysiwyg figure{display:inline-block;margin:0;max-width:100%}.jodit-wysiwyg figure.image-with-caption{float:right;margin:10px 0 10px 15px}.jodit-wysiwyg figure img{float:none;margin:0;display:block}.jodit-wysiwyg figcaption{text-align:center;font-size:14px;color:#666;padding:5px}'})]})}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,724,934,814,474,821,791],()=>t(60240));module.exports=a})();