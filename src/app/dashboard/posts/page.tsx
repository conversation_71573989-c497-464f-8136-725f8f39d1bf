'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '@/lib/firebase/auth-context';
import DashboardLayout from '@/components/DashboardLayout';
import { collection, getDocs, deleteDoc, doc, getDoc, Timestamp, query, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { deleteFileFromStorage } from '@/lib/firebase/storage';

// Define post type
interface Post {
  id: string;
  title: string;
  status: string;
  date: Timestamp;
  author: string;
  content?: string;
  category?: string;
  tags?: string[];
  viewCount?: number;
  featuredImage?: string;
  thumbnailImage?: string;
}

export default function PostsPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch posts from Firebase
  const fetchPosts = async () => {
    try {
      setIsLoading(true);
      const postsCollection = collection(db, 'posts');
      // Add orderBy to sort by createdAt in descending order (newest first)
      const postsQuery = query(
        postsCollection,
        orderBy('createdAt', 'desc')
      );
      const postsSnapshot = await getDocs(postsQuery);
      const postsList = postsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Post[];

      setPosts(postsList);
      setError(null);
    } catch (err) {
      console.error('Error fetching posts:', err);
      setError('هەڵە لە هێنانی بابەتەکان');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchPosts();
    }
  }, [user]);

  // Handle delete post
  const handleDeletePost = async (id: string) => {
    if (confirm('ئایا دڵنیایت دەتەوێت ئەم بابەتە بسڕیتەوە؟')) {
      try {
        // First, get the post to access its media URLs
        const postRef = doc(db, 'posts', id);
        const postSnap = await getDoc(postRef);

        if (postSnap.exists()) {
          const postData = postSnap.data();

          // Delete associated media files
          const mediaDeletePromises = [];

          // Delete featured image if exists
          if (postData.featuredImage && typeof postData.featuredImage === 'string') {
            mediaDeletePromises.push(deleteFileFromStorage(postData.featuredImage));
          }

          // Delete thumbnail image if exists
          if (postData.thumbnailImage && typeof postData.thumbnailImage === 'string') {
            mediaDeletePromises.push(deleteFileFromStorage(postData.thumbnailImage));
          }

          // Wait for all media deletions to complete
          await Promise.all(mediaDeletePromises);

          // Delete the post document from Firestore
          await deleteDoc(postRef);

          // Update local state
          setPosts(posts.filter(post => post.id !== id));

          // Show success message
          alert('بابەت بە سەرکەوتوویی سڕایەوە');
        } else {
          throw new Error('Post not found');
        }
      } catch (err) {
        console.error('Error deleting post:', err);
        setError('هەڵە لە سڕینەوەی بابەت');
      }
    }
  };

  // Handle edit post
  const handleEditPost = (id: string) => {
    router.push(`/dashboard/posts/edit/${id}`);
  };

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p className="text-xl">باری ڕاستاندنەکە بارکردن...</p>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  // Function to format date
  const formatDate = (timestamp: Timestamp | undefined) => {
    if (!timestamp) return 'N/A';
    try {
      const date = timestamp.toDate();
      return date.toLocaleDateString('ku', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (err) {
      console.error('Error formatting date:', err);
      return 'N/A';
    }
  };

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-black">بابەتەکان</h1>
          <Link
            href="/dashboard/posts/new"
            className="px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors font-medium"
            style={{ color: 'white' }} // Ensure white text with inline style
          >
            بابەتی نوێ
          </Link>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md">
            {error}
          </div>
        )}

        {isLoading ? (
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <div className="inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin mb-4"></div>
            <p className="text-black">بارکردنی بابەتەکان...</p>
          </div>
        ) : posts.length === 0 ? (
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <div className="text-5xl mb-4">📝</div>
            <h3 className="text-xl font-bold text-black mb-2">هیچ بابەتێک نییە</h3>
            <p className="text-black mb-4">دەتوانیت یەکەم بابەت دروست بکەیت بە کرتەکردن لە دوگمەی "بابەتی نوێ"</p>
            <Link
              href="/dashboard/posts/new"
              className="inline-block px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors font-medium cursor-pointer"
              style={{ color: 'white' }} // Ensure white text with inline style
            >
              بابەتی نوێ
            </Link>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-[var(--primary-50)]">
                  <tr>
                    <th scope="col" className="px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider">
                      وێنە
                    </th>
                    <th scope="col" className="px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider">
                      ناونیشان
                    </th>
                    <th scope="col" className="px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider">
                      دۆخ
                    </th>
                    <th scope="col" className="px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider">
                      بەروار
                    </th>
                    <th scope="col" className="px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider">
                      کردارەکان
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {posts.map((post) => (
                    <tr key={post.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center justify-center">
                          {post.thumbnailImage ? (
                            <div className="h-16 w-16 relative rounded-md overflow-hidden">
                              <img
                                src={post.thumbnailImage}
                                alt={post.title}
                                className="h-full w-full object-cover"
                              />
                            </div>
                          ) : (
                            <div className="h-16 w-16 bg-gray-200 flex items-center justify-center rounded-md">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-black">{post.title}</div>
                        {post.viewCount !== undefined && (
                          <div className="text-xs text-gray-500 mt-1">
                            <span className="inline-flex items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                              </svg>
                              {post.viewCount} بینین
                            </span>
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          post.status === 'بڵاوکراوەتەوە' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {post.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-black">
                        {formatDate(post.date)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center justify-end space-x-3 space-x-reverse">
                          <button
                            type="button"
                            onClick={() => handleEditPost(post.id)}
                            className="text-[var(--primary-600)] hover:text-[var(--primary-800)] transition-colors p-2 rounded-full hover:bg-[var(--primary-50)] cursor-pointer"
                            title="دەستکاری"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>
                          <button
                            type="button"
                            onClick={() => handleDeletePost(post.id)}
                            className="text-[var(--danger-600)] hover:text-[var(--danger-800)] transition-colors p-2 rounded-full hover:bg-[var(--danger-50)] cursor-pointer"
                            title="سڕینەوە"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
