'use client';

import { useState, useEffect } from 'react';
import PostCard from '@/components/PostCard';
import { collection, query, where, getDocs, orderBy, Timestamp, startAfter, limit, QueryDocumentSnapshot } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import FeaturedPost from '@/components/FeaturedPost';

interface Post {
  id: string;
  title: string;
  content: string;
  thumbnailImage: string;
  tags?: string[];
  category?: string | string[];
  date: Timestamp;
  viewCount: number;
  status: string;
}

const POSTS_PER_PAGE = 13; // Show 13 posts per page (1 featured + 12 regular)

interface TagPageClientProps {
  slug: string;
}

export default function TagPageClient({ slug: tagSlug }: TagPageClientProps) {
  const [posts, setPosts] = useState<Post[]>([]);
  const [tagName, setTagName] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [lastVisible, setLastVisible] = useState<QueryDocumentSnapshot | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  useEffect(() => {
    async function fetchTagPosts() {
      try {
        setIsLoading(true);
        setPosts([]);
        setLastVisible(null);
        setHasMore(true);

        // First, get the tag name
        const tagsCollection = collection(db, 'tags');
        const tagQuery = query(tagsCollection, where('slug', '==', tagSlug));
        const tagSnapshot = await getDocs(tagQuery);

        console.log(`Searching for tag with slug "${tagSlug}"`);
        console.log(`Found ${tagSnapshot.docs.length} matching tags`);

        if (tagSnapshot.empty) {
          console.log('No tag found with this slug');
          setTagName('تاگی نەدۆزراوەتەوە');
          setPosts([]);
          setIsLoading(false);
          return;
        }

        const tagData = tagSnapshot.docs[0].data();
        setTagName(tagData.name);

        console.log('Tag found:', {
          id: tagSnapshot.docs[0].id,
          name: tagData.name,
          slug: tagData.slug
        });

        // Get all published posts
        const postsCollection = collection(db, 'posts');

        try {
          // Use a simpler query that doesn't require a composite index
          const postsQuery = query(
            postsCollection,
            where('tags', 'array-contains', tagData.name), // Use tag name instead of slug
            limit(POSTS_PER_PAGE * 3) // Get more to account for filtering
          );

          const postsSnapshot = await getDocs(postsQuery);
          console.log(`Found ${postsSnapshot.docs.length} posts with tag match`);

          if (!postsSnapshot.empty) {
            // Save the last visible document for pagination
            setLastVisible(postsSnapshot.docs[postsSnapshot.docs.length - 1]);

            // Map the documents to Post objects
            const allPosts = postsSnapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            })) as Post[];

            // Filter for published posts
            const taggedPosts = allPosts.filter(post => post.status === 'بڵاوکراوەتەوە');

            // Take only the first page
            const firstPagePosts = taggedPosts.slice(0, POSTS_PER_PAGE);

            // Set hasMore based on whether we got a full page
            setHasMore(taggedPosts.length > POSTS_PER_PAGE);

            // Set the posts
            setPosts(firstPagePosts);
            setIsLoading(false);
            return;
          }
        } catch (error) {
          console.error('Error with array-contains query:', error);
          // Fall back to getting all posts and filtering
        }

        // If we get here, the first query failed or returned no results
        // Fall back to getting all posts and filtering
        console.log('Falling back to client-side filtering');

        // Get all posts and filter client-side
        const fallbackQuery = query(
          postsCollection,
          orderBy('date', 'desc'),
          limit(POSTS_PER_PAGE * 3) // Get more to account for filtering
        );

        const fallbackSnapshot = await getDocs(fallbackQuery);
        console.log(`Found ${fallbackSnapshot.docs.length} published posts total`);

        if (fallbackSnapshot.empty) {
          setHasMore(false);
          setIsLoading(false);
          return;
        }

        // Save the last visible document for pagination
        setLastVisible(fallbackSnapshot.docs[fallbackSnapshot.docs.length - 1]);

        // Map and filter client-side
        const allPosts = fallbackSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Post[];

        // Filter posts for published status and this tag
        const filteredPosts = allPosts.filter(post =>
          post.status === 'بڵاوکراوەتەوە' &&
          post.tags &&
          Array.isArray(post.tags) &&
          post.tags.includes(tagSlug)
        );

        console.log(`Filtered to ${filteredPosts.length} posts for tag ${tagData.name}`);

        // Take only the first page
        const firstPagePosts = filteredPosts.slice(0, POSTS_PER_PAGE);

        // Set hasMore based on whether we have more posts than the page size
        setHasMore(filteredPosts.length > POSTS_PER_PAGE);

        // Set the posts
        setPosts(firstPagePosts);
      } catch (err) {
        console.error('Error fetching tag posts:', err);
      } finally {
        setIsLoading(false);
      }
    }

    if (tagSlug) {
      fetchTagPosts();
    }
  }, [tagSlug]);

  const handleLoadMore = async () => {
    if (!lastVisible || !hasMore || isLoadingMore) return;

    setIsLoadingMore(true);

    try {
      // Get more posts starting after the last visible document
      const postsCollection = collection(db, 'posts');

      try {
        // Get the tag name first
        const tagsCollection = collection(db, 'tags');
        const tagQuery = query(tagsCollection, where('slug', '==', tagSlug));
        const tagSnapshot = await getDocs(tagQuery);

        if (tagSnapshot.empty) {
          setHasMore(false);
          return;
        }

        const tagData = tagSnapshot.docs[0].data();

        // Use a simpler query that doesn't require a composite index
        const postsQuery = query(
          postsCollection,
          where('tags', 'array-contains', tagData.name),
          limit(POSTS_PER_PAGE * 3) // Get more to account for filtering
        );

        const postsSnapshot = await getDocs(postsQuery);
        console.log(`Loaded ${postsSnapshot.docs.length} more posts with tag match`);

        if (!postsSnapshot.empty) {
          // Save the last visible document for pagination
          setLastVisible(postsSnapshot.docs[postsSnapshot.docs.length - 1]);

          // Map the documents to Post objects
          const allMorePosts = postsSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as Post[];

          // Filter for published posts and exclude posts we already have
          const morePosts = allMorePosts
            .filter(post => post.status === 'بڵاوکراوەتەوە')
            .filter(newPost => !posts.some(existingPost => existingPost.id === newPost.id));

          // Take only up to POSTS_PER_PAGE
          const nextPagePosts = morePosts.slice(0, POSTS_PER_PAGE);

          // Set hasMore based on whether we got enough posts after filtering
          setHasMore(morePosts.length > POSTS_PER_PAGE);

          // Add the new posts to the existing ones
          setPosts(prevPosts => [...prevPosts, ...nextPagePosts]);
          setIsLoadingMore(false);
          return;
        }
      } catch (error) {
        console.error('Error with array-contains query:', error);
        // Fall back to a simpler query
      }

      // If we get here, the first query failed or returned no results
      // Fall back to a simpler query
      const fallbackQuery = query(
        postsCollection,
        orderBy('date', 'desc'),
        startAfter(lastVisible),
        limit(POSTS_PER_PAGE * 3) // Get more to account for filtering
      );

      const fallbackSnapshot = await getDocs(fallbackQuery);
      console.log(`Loaded ${fallbackSnapshot.docs.length} more posts total`);

      if (fallbackSnapshot.empty) {
        setHasMore(false);
        setIsLoadingMore(false);
        return;
      }

      // Save the last visible document for pagination
      setLastVisible(fallbackSnapshot.docs[fallbackSnapshot.docs.length - 1]);

      // Map the documents to Post objects
      const morePosts = fallbackSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Post[];

      // Filter posts for published status and this tag
      const filteredPosts = morePosts.filter(post =>
        post.status === 'بڵاوکراوەتەوە' &&
        post.tags &&
        Array.isArray(post.tags) &&
        post.tags.includes(tagSlug)
      );

      console.log(`Filtered to ${filteredPosts.length} more posts for this tag`);

      // Take only up to POSTS_PER_PAGE
      const nextPagePosts = filteredPosts.slice(0, POSTS_PER_PAGE);

      // Set hasMore based on whether we got enough posts after filtering
      setHasMore(nextPagePosts.length >= POSTS_PER_PAGE);

      // Add the new posts to the existing ones
      setPosts(prevPosts => [...prevPosts, ...nextPagePosts]);
    } catch (error) {
      console.error('Error loading more posts:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <h1 className="text-3xl font-bold text-black mb-4 relative pr-6 inline-block after:content-[''] after:absolute after:top-0 after:bottom-0 after:right-0 after:w-2 after:bg-[#8f5826] after:rounded-full">{tagName}</h1>
          <div className="h-1 bg-[#8f5826] w-full my-6 mb-8 rounded-full opacity-20"></div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin"></div>
            </div>
          ) : posts.length === 0 ? (
            <div className="flex justify-center items-center h-64">
              <p className="text-xl text-gray-500">هیچ بابەتێک نەدۆزرایەوە بۆ ئەم تاگە</p>
            </div>
          ) : (
            <>
              {/* Featured Post */}
              {posts.length > 0 && (
                <>
                  {console.log('Featured post in tag page:', posts[0])}
                  <FeaturedPost post={posts[0]} />
                </>
              )}

              {/* Regular Posts Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {posts.slice(1).map((post) => (
                  <PostCard
                    key={post.id}
                    id={post.id}
                    title={post.title}
                    thumbnailImage={post.thumbnailImage}
                    date={post.date}
                    category={Array.isArray(post.category) ? post.category[0] : post.category}
                    viewCount={post.viewCount}
                  />
                ))}
              </div>

              {/* Load More Button */}
              {hasMore && (
                <div className="mt-12 flex justify-center">
                  <button
                    onClick={handleLoadMore}
                    disabled={isLoadingMore}
                    className={`px-6 py-3 rounded-md bg-[var(--primary-600)] text-white hover:bg-[var(--primary-700)] transition-colors ${
                      isLoadingMore ? 'opacity-70 cursor-not-allowed' : ''
                    }`}
                    type="button"
                  >
                    {isLoadingMore ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        چاوەڕوانبە...
                      </span>
                    ) : (
                      'بینینی زیاتر'
                    )}
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
}
