const { chromium } = require('playwright');

(async () => {
  // Launch the browser
  const browser = await chromium.launch({ 
    headless: false, // Show the browser
    slowMo: 100 // Slow down operations for visibility
  });
  
  // Create a new browser context
  const context = await browser.newContext({
    viewport: { width: 1280, height: 800 }
  });
  
  // Open a new page
  const page = await context.newPage();
  
  try {
    // Set up console log capture
    page.on('console', msg => {
      console.log(`PAGE LOG: ${msg.text()}`);
    });
    
    // Navigate to the login page
    console.log('Navigating to the login page...');
    await page.goto('http://localhost:3000/login');
    
    // Wait for the login form to load
    console.log('Waiting for the login form to load...');
    await page.waitForSelector('form', { timeout: 10000 });
    
    // Fill in the login form with the provided credentials
    console.log('Filling in login credentials...');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Azhee2001');
    
    // Submit the form
    console.log('Submitting login form...');
    await page.click('button[type="submit"]');
    
    // Wait for navigation after login
    console.log('Waiting for navigation after login...');
    await page.waitForNavigation({ timeout: 30000 });
    
    // Navigate to the new post page
    console.log('Navigating to the new post page...');
    await page.goto('http://localhost:3000/dashboard/posts/new');
    
    // Wait for the editor to load
    console.log('Waiting for the editor to load...');
    await page.waitForSelector('.lexical-editor-container', { timeout: 10000 });
    
    // Click on the image upload button
    console.log('Clicking on the image upload button...');
    const uploadButton = await page.locator('button[aria-label="Insert Image"]');
    await uploadButton.click();
    
    // Wait for the file input to be available
    console.log('Setting up file input...');
    
    // Create a file input for uploading
    const fileChooserPromise = page.waitForEvent('filechooser');
    await uploadButton.click();
    const fileChooser = await fileChooserPromise;
    
    // Upload a test image
    console.log('Uploading test image...');
    await fileChooser.setFiles('./test-image.jpg');
    
    // Wait for the upload to complete and image to appear
    console.log('Waiting for upload to complete...');
    await page.waitForSelector('.editor-image', { timeout: 30000 });
    
    // Take a screenshot after image upload
    console.log('Taking a screenshot after image upload...');
    await page.screenshot({ path: 'after-upload.png', fullPage: true });
    
    // Click on the image to select it
    console.log('Clicking on the image to select it...');
    await page.click('.editor-image');
    
    // Wait for resize handles to appear
    console.log('Waiting for resize handles to appear...');
    await page.waitForSelector('.image-resizer-se', { timeout: 5000 });
    
    // Take a screenshot with resize handles visible
    console.log('Taking a screenshot with resize handles...');
    await page.screenshot({ path: 'with-resize-handles.png', fullPage: true });
    
    // Attempt to resize the image using the southeast handle
    console.log('Attempting to resize the image...');
    const seHandle = await page.locator('.image-resizer-se');
    
    // Get the current position of the handle
    const handleBox = await seHandle.boundingBox();
    
    // Perform the drag operation to resize
    await page.mouse.move(handleBox.x + handleBox.width / 2, handleBox.y + handleBox.height / 2);
    await page.mouse.down();
    await page.mouse.move(handleBox.x + 100, handleBox.y + 100, { steps: 10 }); // Move 100px right and down
    await page.mouse.up();
    
    // Wait a moment for the resize to complete
    await page.waitForTimeout(1000);
    
    // Take a final screenshot after resizing
    console.log('Taking a final screenshot after resizing...');
    await page.screenshot({ path: 'after-resize.png', fullPage: true });
    
    console.log('Test completed successfully!');
    
    // Keep the browser open for manual inspection
    console.log('Browser will remain open for manual inspection. Press Ctrl+C to close.');
    await new Promise(() => {}); // This promise never resolves
    
  } catch (error) {
    console.error('Error:', error);
    await page.screenshot({ path: 'error-screenshot.png', fullPage: true });
  }
})();
