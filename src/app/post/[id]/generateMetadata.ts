import { Metadata } from 'next';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

type Params = Promise<{ id: string }>;

export async function generateMetadata({ params }: { params: Params }): Promise<Metadata> {
  // Await the params to get the id
  const { id: postId } = await params;
  
  // Default metadata
  let title = 'ڕامیاری';
  let description = 'ڕامیاری، سەرچاوەی هەواڵ و زانیاری ڕامیاری و کۆمەڵایەتی';
  
  try {
    // Fetch the post data
    const postRef = doc(db, 'posts', postId);
    const postSnap = await getDoc(postRef);
    
    if (postSnap.exists()) {
      const postData = postSnap.data();
      
      // Use post title for the page title
      title = `${postData.title} | ڕامیاری`;
      
      // Create a description from the post content
      if (postData.content) {
        // Strip HTML tags and limit to 160 characters
        const plainText = postData.content.replace(/<[^>]*>/g, '');
        description = plainText.substring(0, 160) + (plainText.length > 160 ? '...' : '');
      }
    }
  } catch (error) {
    console.error('Error fetching post for metadata:', error);
  }
  
  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'article',
    },
    other: {
      'google': 'translate',
      'content-language': 'ku',
      'language': 'Kurdish',
    },
  };
}

export default generateMetadata;
