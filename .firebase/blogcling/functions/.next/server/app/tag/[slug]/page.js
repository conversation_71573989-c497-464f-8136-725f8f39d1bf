(()=>{var e={};e.id=55,e.ids=[55],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4875:e=>{e.exports={featuredPost:"FeaturedPost_featuredPost__s1v6r",imageContainer:"FeaturedPost_imageContainer__tN_ZP",contentContainer:"FeaturedPost_contentContainer__ii0wc",title:"FeaturedPost_title__pyXoN",meta:"FeaturedPost_meta__I_SqY",metaItem:"FeaturedPost_metaItem__AoiLn",excerpt:"FeaturedPost_excerpt__A0L8I",readMore:"FeaturedPost_readMore__X6wyR"}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},26232:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(60687),a=s(43210),i=s(30474),o=s(85814),n=s.n(o),l=s(4875),d=s.n(l),c=s(32481);function m({post:e}){console.log("FeaturedPost rendering with post:",e),console.log("FeaturedPost image URL:",e.thumbnailImage);let[t,s]=(0,a.useState)(!1),o=e.thumbnailImage&&(e.thumbnailImage.startsWith("http://")||e.thumbnailImage.startsWith("https://")),l=o&&e.thumbnailImage.includes("firebasestorage.googleapis.com");return(0,r.jsxs)("div",{className:d().featuredPost,children:[(0,r.jsx)("div",{className:d().imageContainer,children:(0,r.jsx)(n(),{href:`/post/${e.id}`,style:{display:"block",height:"100%",position:"relative"},children:o&&!t?(0,r.jsx)(i.default,{src:e.thumbnailImage,alt:e.title,fill:!0,sizes:"(max-width: 768px) 100vw, 40vw",className:"object-cover",loading:"eager",priority:!0,quality:90,onError:()=>{console.error("Error loading image:",e.thumbnailImage),s(!0)},unoptimized:!!l||void 0},e.thumbnailImage):(0,r.jsx)(c.A,{})})}),(0,r.jsxs)("div",{className:d().contentContainer,children:[(0,r.jsx)("h2",{className:d().title,children:e.title}),(0,r.jsxs)("div",{className:d().meta,children:[(0,r.jsxs)("span",{className:d().metaItem,children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),e.date?new Date(e.date.toDate()).toLocaleDateString("ku-IQ"):""]}),(0,r.jsxs)("span",{className:d().metaItem,children:[(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),e.viewCount||0," بینین"]})]}),e.content&&(0,r.jsx)("div",{className:d().excerpt,children:(0,r.jsx)("div",{dangerouslySetInnerHTML:{__html:e.content.replace(/<[^>]*>/g,"").substring(0,150)+"..."}})}),(0,r.jsx)(n(),{href:`/post/${e.id}`,className:d().readMore,children:"خوێندنەوەی زیاتر"})]})]})}},26580:(e,t,s)=>{Promise.resolve().then(s.bind(s,83236))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29851:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(37413),a=s(83236);async function i({params:e}){let{slug:t}=await e;return(0,r.jsx)(a.default,{slug:t})}},31618:(e,t,s)=>{"use strict";s.d(t,{default:()=>m});var r=s(60687),a=s(43210),i=s(78404),o=s(75535),n=s(71557),l=s(34148),d=s(51317),c=s(26232);function m({slug:e}){let[t,s]=(0,a.useState)([]),[m,u]=(0,a.useState)(""),[p,x]=(0,a.useState)(!0),[h,g]=(0,a.useState)(null),[f,v]=(0,a.useState)(!0),[j,w]=(0,a.useState)(!1),b=async()=>{if(h&&f&&!j){w(!0);try{let r=(0,o.rJ)(n.db,"posts");try{let a=(0,o.rJ)(n.db,"tags"),i=(0,o.P)(a,(0,o._M)("slug","==",e)),l=await (0,o.GG)(i);if(l.empty)return void v(!1);let d=l.docs[0].data(),c=(0,o.P)(r,(0,o._M)("tags","array-contains",d.name),(0,o.AB)(39)),m=await (0,o.GG)(c);if(console.log(`Loaded ${m.docs.length} more posts with tag match`),!m.empty){g(m.docs[m.docs.length-1]);let e=m.docs.map(e=>({id:e.id,...e.data()})).filter(e=>"بڵاوکراوەتەوە"===e.status).filter(e=>!t.some(t=>t.id===e.id)),r=e.slice(0,13);v(e.length>13),s(e=>[...e,...r]),w(!1);return}}catch(e){console.error("Error with array-contains query:",e)}let a=(0,o.P)(r,(0,o.My)("date","desc"),(0,o.HM)(h),(0,o.AB)(39)),i=await (0,o.GG)(a);if(console.log(`Loaded ${i.docs.length} more posts total`),i.empty){v(!1),w(!1);return}g(i.docs[i.docs.length-1]);let l=i.docs.map(e=>({id:e.id,...e.data()})).filter(t=>"بڵاوکراوەتەوە"===t.status&&t.tags&&Array.isArray(t.tags)&&t.tags.includes(e));console.log(`Filtered to ${l.length} more posts for this tag`);let d=l.slice(0,13);v(d.length>=13),s(e=>[...e,...d])}catch(e){console.error("Error loading more posts:",e)}finally{w(!1)}}};return(0,r.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,r.jsx)(l.default,{}),(0,r.jsx)("main",{className:"flex-grow",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-black mb-4 relative pr-6 inline-block after:content-[''] after:absolute after:top-0 after:bottom-0 after:right-0 after:w-2 after:bg-[#8f5826] after:rounded-full",children:m}),(0,r.jsx)("div",{className:"h-1 bg-[#8f5826] w-full my-6 mb-8 rounded-full opacity-20"}),p?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin"})}):0===t.length?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("p",{className:"text-xl text-gray-500",children:"هیچ بابەتێک نەدۆزرایەوە بۆ ئەم تاگە"})}):(0,r.jsxs)(r.Fragment,{children:[t.length>0&&(0,r.jsxs)(r.Fragment,{children:[console.log("Featured post in tag page:",t[0]),(0,r.jsx)(c.A,{post:t[0]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:t.slice(1).map(e=>(0,r.jsx)(i.A,{id:e.id,title:e.title,thumbnailImage:e.thumbnailImage,date:e.date,category:Array.isArray(e.category)?e.category[0]:e.category,viewCount:e.viewCount},e.id))}),f&&(0,r.jsx)("div",{className:"mt-12 flex justify-center",children:(0,r.jsx)("button",{onClick:b,disabled:j,className:`px-6 py-3 rounded-md bg-[var(--primary-600)] text-white hover:bg-[var(--primary-700)] transition-colors ${j?"opacity-70 cursor-not-allowed":""}`,type:"button",children:j?(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"چاوەڕوانبە..."]}):"بینینی زیاتر"})})]})]})}),(0,r.jsx)(d.default,{})]})}},32481:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(60687);function a({className:e="",style:t={}}){return(0,r.jsx)("div",{className:`flex items-center justify-center bg-gradient-to-br from-[#f9f5f0] to-[#e6d7c3] ${e}`,style:{width:"100%",height:"100%",position:"absolute",top:0,left:0,...t},children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-[#8f5826] font-bold text-2xl md:text-3xl lg:text-4xl",children:"Ramiyari.com        "}),(0,r.jsx)("div",{className:"text-[#8f5826] opacity-70 text-sm md:text-base mt-2",children:"ئازادی لە دەبرینی نارەزایی        "})]})})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64712:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),o=s.n(i),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let d={children:["",{children:["tag",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,29851)),"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/tag/[slug]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/tag/[slug]/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/tag/[slug]/page",pathname:"/tag/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},78404:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var r=s(60687),a=s(43210),i=s(30474),o=s(85814),n=s.n(o),l=s(32481);function d({id:e,title:t,thumbnailImage:s,date:o,category:d,viewCount:c}){let[m,u]=(0,a.useState)(!1),p=s&&(s.startsWith("http://")||s.startsWith("https://")),x=p&&s&&s.includes("firebasestorage.googleapis.com");return(0,r.jsx)("div",{className:"bg-[#f9f5f0] rounded-lg overflow-hidden border-2 border-[#8f5826] shadow-[0_4px_12px_rgba(143,88,38,0.2)] hover:shadow-[0_8px_20px_rgba(143,88,38,0.3)] transition-all duration-300 h-full flex flex-col",children:(0,r.jsxs)(n(),{href:`/post/${e}`,className:"block h-full",children:[(0,r.jsx)("div",{className:"relative h-48 bg-gray-100",children:p&&!m?(0,r.jsx)(i.default,{src:s,alt:t,fill:!0,sizes:"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw",className:"object-cover",loading:"lazy",quality:75,onError:()=>{console.error("Error loading image:",s),u(!0)},unoptimized:!!x||void 0},s):(0,r.jsx)(l.A,{})}),(0,r.jsxs)("div",{className:"p-4 flex flex-col flex-grow",children:[(0,r.jsx)("h3",{className:"text-lg font-bold text-black mb-2 line-clamp-2",children:t}),(0,r.jsxs)("div",{className:"flex items-center text-gray-500 text-sm mb-2",children:[(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),o?new Date(o.toDate()).toLocaleDateString("ku-IQ"):""]}),(0,r.jsx)("span",{className:"mx-2",children:"•"}),(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),c||0," بینین"]})]}),d&&(0,r.jsx)("span",{className:"inline-block bg-[var(--primary-100)] text-[var(--primary-700)] px-2 py-1 text-xs font-semibold rounded-md mt-auto",children:d})]})]})})}},79316:(e,t,s)=>{Promise.resolve().then(s.bind(s,31618))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83236:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/tag/[slug]/TagPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/tag/[slug]/TagPageClient.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,724,934,814,474,819],()=>s(64712));module.exports=r})();