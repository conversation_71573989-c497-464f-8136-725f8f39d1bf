(()=>{var e={};e.id=588,e.ids=[588],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18882:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/dashboard/posts/new/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/dashboard/posts/new/page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},24665:(e,r,t)=>{"use strict";t.d(r,{jn:()=>c,If:()=>l,V1:()=>d,PX:()=>n});var s=t(70146),a=t(71557);async function o(e,r=1200,t=1200,s=.85){return new Promise((a,o)=>{try{let i=new Image;i.onload=()=>{let l=i.width,n=i.height;if(l>r||n>t){let e=l/n;l>r&&(n=(l=r)/e),n>t&&(l=(n=t)*e)}let d=document.createElement("canvas");d.width=l,d.height=n;let c=d.getContext("2d");if(!c)return void o(Error("Could not get canvas context"));c.drawImage(i,0,0,l,n);let p=e.split(";")[0].split(":")[1]||"image/jpeg",m=d.toDataURL(p,s);a(m)},i.onerror=()=>{console.error("Error loading image for optimization"),a(e)},i.src=e}catch(r){console.error("Error optimizing image:",r),a(e)}})}async function i(e,r,t,i,l,n){try{let d=e;if(i&&l)try{d=await o(e,i,l,n),console.log("Image optimized successfully")}catch(e){console.error("Error optimizing image:",e)}let c=d.match(/^data:([A-Za-z-+/]+);base64,(.+)$/);if(!c||3!==c.length)throw Error("Invalid base64 image format");let p=c[1],m=c[2],u="jpg";p.includes("png")?u="png":p.includes("gif")?u="gif":p.includes("webp")&&(u="webp");let h=t||`${Date.now()}.${u}`,x=(0,s.KR)(a.IG,`${r}/${h}`);try{let e=await (0,s.ls)(x,m,"base64",{contentType:p,cacheControl:"public, max-age=31536000"}),r=await (0,s.qk)(e.ref);return console.log("Image uploaded successfully to Firebase Storage:",r),r}catch(e){return console.error("Firebase Storage upload failed:",e),e instanceof Error&&(e.message.includes("unauthorized")?(console.warn("Storage permission denied. Using base64 fallback. Please update Firebase Storage rules."),alert("تکایە ڕێساکانی Firebase Storage نوێ بکەرەوە بۆ ڕێگەدان بە بارکردنی وێنەکان")):e.message.includes("CORS")&&console.warn("CORS error detected. Using base64 fallback. Please configure CORS for Firebase Storage.")),console.warn("Falling back to storing image as base64 in Firestore"),d}}catch(r){return console.error("Error processing image:",r),console.warn("Error occurred, falling back to base64 storage"),e}}function l(e){let r=Date.now(),t=Math.random().toString(36).substring(2,8);if(e){let s=e.split(".").pop()||"jpg";return`${r}-${t}.${s}`}return`${r}-${t}.jpg`}async function n(e,r){return i(e,"posts/thumbnails",r,600,600,.7)}async function d(e,r){return i(e,"posts/featured",r,1600,1600,.85)}async function c(e){try{if(!e||!e.includes("firebasestorage.googleapis.com"))return console.warn("Not a Firebase Storage URL:",e),!1;let r=new URL(e),t=decodeURIComponent(r.pathname.split("/o/")[1]?.split("?")[0]);if(!t)return console.warn("Could not extract path from URL:",e),!1;let o=(0,s.KR)(a.IG,t);return await (0,s.XR)(o),console.log("File deleted successfully:",t),!0}catch(e){return console.error("Error deleting file:",e),!1}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30684:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(60687),a=t(43210),o=t(16189),i=t(30474),l=t(24157),n=t(31596),d=t(74932),c=t(75535),p=t(71557),m=t(24665);function u(){let{user:e,loading:r}=(0,l.A)(),t=(0,o.useRouter)(),[u,h]=(0,a.useState)(""),[x,g]=(0,a.useState)(""),[b,f]=(0,a.useState)(""),[v,y]=(0,a.useState)(""),[j,w]=(0,a.useState)("draft"),[N,k]=(0,a.useState)(!1),[C,S]=(0,a.useState)(null),[z,E]=(0,a.useState)(null),[F,I]=(0,a.useState)(0),[P,L]=(0,a.useState)(new Date().toISOString().split("T")[0]),[q,R]=(0,a.useState)([]),[_,A]=(0,a.useState)([]),[D,M]=(0,a.useState)(!0),[U,$]=(0,a.useState)(!0),B=(e,r)=>{let t=e.target.files?.[0];if(t){let e=new FileReader;e.onloadend=()=>{r(e.result)},e.readAsDataURL(t)}},[G,W]=(0,a.useState)(!1),[K,T]=(0,a.useState)(null),O=async r=>{r.preventDefault(),W(!0),T(null);try{if(!u.trim())throw Error("تکایە ناونیشانێک بنووسە");if(!x.trim())throw Error("تکایە ناوەڕۆکێک بنووسە");if(!e)throw Error("تکایە دووبارە چوونەژوورەوە بکە");let r=v.split(",").map(e=>e.trim()).filter(e=>e.length>0),s=null,a=null;if(C)try{let e=(0,m.If)("featured.jpg");s=await (0,m.V1)(C,e),console.log("Featured image uploaded and optimized:",s)}catch(e){throw console.error("Error uploading featured image:",e),Error("هەڵە لە بارکردنی وێنەی سەرەکی")}if(z)try{let e=(0,m.If)("thumbnail.jpg");a=await (0,m.PX)(z,e),console.log("Thumbnail image uploaded and optimized:",a)}catch(e){throw console.error("Error uploading thumbnail image:",e),Error("هەڵە لە بارکردنی وێنەی بچووک")}console.log("Saving content with dimensions:",x);let o={title:u,content:x,category:b,tags:r,status:"publish"===j?"بڵاوکراوەتەوە":"پێشنووس",isPrimary:N,featuredImage:s,thumbnailImage:a,viewCount:F,date:c.Dc.fromDate(new Date(P)),author:e?.email||"ئەدمین",createdAt:c.Dc.now(),updatedAt:c.Dc.now()};console.log("Saving post to Firestore...");let i=(0,c.rJ)(p.db,"posts"),l=await (0,c.gS)(i,o);console.log("Post saved successfully with ID:",l.id),alert("بابەت بە سەرکەوتوویی پاشەکەوت کرا!"),t.push("/dashboard/posts")}catch(e){console.error("Error saving post:",e),T(e.message||"هەڵەیەک ڕوویدا لە پاشەکەوتکردنی بابەت")}finally{W(!1)}};return r?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("p",{className:"text-xl",children:"باری ڕاستاندنەکە بارکردن..."})}):e?(0,s.jsx)(n.A,{children:(0,s.jsxs)("div",{className:"max-w-5xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-black",children:"بابەتی نوێ"}),(0,s.jsx)("button",{type:"button",onClick:()=>t.push("/dashboard/posts"),className:"px-4 py-2 bg-[var(--gray-500)] text-white rounded-md hover:bg-[var(--gray-600)] transition-colors cursor-pointer",children:"گەڕانەوە"})]}),(0,s.jsxs)("div",{className:"bg-[var(--card-bg)] rounded-lg shadow-md border border-[var(--card-border)] p-6",children:[K&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md",children:K}),(0,s.jsxs)("form",{onSubmit:O,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"title",className:"block text-black text-sm font-bold mb-2",children:"ناونیشان"}),(0,s.jsx)("input",{type:"text",id:"title",value:u,onChange:e=>h(e.target.value),className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors",required:!0})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-black text-sm font-bold mb-2",children:"وێنەی سەرەکی"}),(0,s.jsxs)("div",{className:"border-2 border-dashed border-[var(--gray-300)] rounded-md p-4 text-center",children:[C?(0,s.jsxs)("div",{className:"relative h-48 mb-2",children:[(0,s.jsx)(i.default,{src:C,alt:"Featured",fill:!0,style:{objectFit:"cover"},className:"rounded-md"}),(0,s.jsx)("button",{type:"button",onClick:()=>S(null),className:"absolute top-2 left-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors cursor-pointer",title:"سڕینەوەی وێنە","aria-label":"سڕینەوەی وێنە",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}):(0,s.jsxs)("div",{className:"h-48 flex flex-col items-center justify-center",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-[var(--gray-400)]",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),(0,s.jsx)("p",{className:"mt-2 text-sm text-black",children:"کرتە بکە بۆ هەڵبژاردنی وێنە"})]}),(0,s.jsx)("input",{type:"file",id:"featuredImage",accept:"image/*",className:"hidden",onChange:e=>B(e,S)}),(0,s.jsx)("label",{htmlFor:"featuredImage",className:"mt-2 inline-block px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors cursor-pointer",children:C?"گۆڕینی وێنە":"هەڵبژاردنی وێنە"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-black text-sm font-bold mb-2",children:"وێنەی بچووک"}),(0,s.jsxs)("div",{className:"border-2 border-dashed border-[var(--gray-300)] rounded-md p-4 text-center",children:[z?(0,s.jsxs)("div",{className:"relative h-48 mb-2",children:[(0,s.jsx)(i.default,{src:z,alt:"Thumbnail",fill:!0,style:{objectFit:"cover"},className:"rounded-md"}),(0,s.jsx)("button",{type:"button",onClick:()=>E(null),className:"absolute top-2 left-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors cursor-pointer",title:"سڕینەوەی وێنە","aria-label":"سڕینەوەی وێنە",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}):(0,s.jsxs)("div",{className:"h-48 flex flex-col items-center justify-center",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-12 w-12 text-[var(--gray-400)]",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),(0,s.jsx)("p",{className:"mt-2 text-sm text-black",children:"کرتە بکە بۆ هەڵبژاردنی وێنە"})]}),(0,s.jsx)("input",{type:"file",id:"thumbnailImage",accept:"image/*",className:"hidden",onChange:e=>B(e,E)}),(0,s.jsx)("label",{htmlFor:"thumbnailImage",className:"mt-2 inline-block px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors cursor-pointer",children:z?"گۆڕینی وێنە":"هەڵبژاردنی وێنە"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"content",className:"block text-black text-sm font-bold mb-2",children:"ناوەڕۆک"}),(0,s.jsx)(d.A,{value:x,onChange:e=>{console.log("Editor content updated"),g(e)},placeholder:"ناوەڕۆک بنووسە...",height:650,width:"100%",rtl:!0})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"category",className:"block text-black text-sm font-bold mb-2",children:"پۆل"}),D?(0,s.jsxs)("div",{className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md bg-gray-50 flex items-center",children:[(0,s.jsx)("div",{className:"inline-block w-4 h-4 border-2 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin ml-2"}),(0,s.jsx)("span",{className:"text-gray-500",children:"بارکردنی پۆلەکان..."})]}):(0,s.jsxs)("select",{id:"category",value:b,onChange:e=>f(e.target.value),className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors cursor-pointer",children:[(0,s.jsx)("option",{value:"",children:"هەڵبژاردنی پۆل"}),q.map(e=>(0,s.jsx)("option",{value:e.slug,children:e.name},e.id))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"tags",className:"block text-black text-sm font-bold mb-2",children:"تاگەکان"}),U?(0,s.jsxs)("div",{className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md bg-gray-50 flex items-center",children:[(0,s.jsx)("div",{className:"inline-block w-4 h-4 border-2 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin ml-2"}),(0,s.jsx)("span",{className:"text-gray-500",children:"بارکردنی تاگەکان..."})]}):(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:v.split(",").map(e=>e.trim()).filter(e=>e.length>0).map((e,r)=>(0,s.jsxs)("span",{className:"bg-[var(--primary-100)] text-[var(--primary-700)] px-2 py-1 rounded-md text-sm flex items-center",children:[e,(0,s.jsx)("button",{type:"button",onClick:()=>{let e=v.split(",").map(e=>e.trim()).filter(e=>e.length>0);e.splice(r,1),y(e.join(", "))},className:"ml-1 text-[var(--primary-500)] hover:text-[var(--primary-700)] transition-colors cursor-pointer",title:"سڕینەوەی تاگ","aria-label":"سڕینەوەی تاگ",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]},r))}),(0,s.jsx)("div",{className:"flex",children:(0,s.jsxs)("select",{id:"tagSelector",name:"tagSelector","aria-label":"هەڵبژاردنی تاگ",title:"هەڵبژاردنی تاگ",className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors cursor-pointer",onChange:e=>{if(e.target.value){let r=e.target.value,t=v.split(",").map(e=>e.trim()).filter(e=>e.length>0);t.includes(r)||y([...t,r].join(", ")),e.target.value=""}},children:[(0,s.jsx)("option",{value:"",children:"هەڵبژاردنی تاگ"}),_.map(e=>(0,s.jsx)("option",{value:e.name,children:e.name},e.id))]})})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"publishDate",className:"block text-black text-sm font-bold mb-2",children:"بەرواری بڵاوکردنەوە"}),(0,s.jsx)("input",{type:"date",id:"publishDate",value:P,onChange:e=>L(e.target.value),className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors cursor-pointer"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"viewCount",className:"block text-black text-sm font-bold mb-2",children:"ژمارەی بینەران"}),(0,s.jsx)("input",{type:"number",id:"viewCount",value:F,onChange:e=>I(parseInt(e.target.value)||0),min:"0",className:"w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-black text-sm font-bold mb-2",children:"دۆخ"}),(0,s.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",id:"draft",name:"status",value:"draft",checked:"draft"===j,onChange:()=>w("draft"),className:"ml-2 h-4 w-4 text-[var(--primary-600)] cursor-pointer"}),(0,s.jsx)("label",{htmlFor:"draft",className:"ml-2 text-black cursor-pointer hover:text-[var(--primary-600)] transition-colors",children:"پێشنووس"})]}),(0,s.jsxs)("div",{className:"flex items-center mr-6",children:[(0,s.jsx)("input",{type:"radio",id:"publish",name:"status",value:"publish",checked:"publish"===j,onChange:()=>w("publish"),className:"ml-2 h-4 w-4 text-[var(--primary-600)] cursor-pointer"}),(0,s.jsx)("label",{htmlFor:"publish",className:"ml-2 text-black cursor-pointer hover:text-[var(--primary-600)] transition-colors",children:"بڵاوکردنەوە"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-black text-sm font-bold mb-2",children:"بابەتی سەرەکی"}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsxs)("div",{className:"relative inline-block w-10 ml-2 align-middle select-none transition duration-200 ease-in",children:[(0,s.jsx)("input",{type:"checkbox",name:"isPrimary",id:"isPrimary",checked:N,onChange:()=>k(!N),className:"toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"}),(0,s.jsx)("label",{htmlFor:"isPrimary",className:`toggle-label block overflow-hidden h-6 rounded-full cursor-pointer ${N?"bg-[var(--primary-600)]":"bg-gray-300"}`})]}),(0,s.jsx)("label",{htmlFor:"isPrimary",className:"text-black cursor-pointer hover:text-[var(--primary-600)] transition-colors",children:N?"بەڵێ":"نەخێر"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-[var(--gray-200)]",children:[(0,s.jsx)("button",{type:"submit",disabled:G,className:`px-6 py-3 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors font-medium flex items-center cursor-pointer ${G?"opacity-70 cursor-not-allowed":""}`,children:G?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{className:"inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ml-2"}),(0,s.jsx)("span",{children:"پاشەکەوتکردن..."})]}):"پاشەکەوتکردن"}),(0,s.jsx)("button",{type:"button",disabled:G,onClick:()=>{h(""),g(""),f(""),y(""),w("draft"),S(null),E(null),I(0),L(new Date().toISOString().split("T")[0]),window.location.reload()},className:`px-6 py-3 bg-[var(--gray-500)] text-white rounded-md hover:bg-[var(--gray-600)] transition-colors font-medium cursor-pointer ${G?"opacity-70 cursor-not-allowed":""}`,children:"پاککردنەوە"})]})]})]})]})}):null}t(33701)},33701:()=>{},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36880:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(65239),a=t(48088),o=t(88170),i=t.n(o),l=t(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);t.d(r,n);let d={children:["",{children:["dashboard",{children:["posts",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,18882)),"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/dashboard/posts/new/page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/dashboard/posts/new/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/posts/new/page",pathname:"/dashboard/posts/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},37366:e=>{"use strict";e.exports=require("dns")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65881:(e,r,t)=>{Promise.resolve().then(t.bind(t,18882))},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},74932:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var s=t(60687),a=t(76180),o=t.n(a),i=t(43210);let l=(0,t(30036).default)(async()=>{},{loadableGenerated:{modules:["components/JoditEditor/index.tsx -> jodit-react"]},ssr:!1}),n=({value:e,onChange:r,placeholder:t="ناوەڕۆک بنووسە...",height:a=650,width:n="100%",readonly:d=!1,toolbar:c=!0,language:p="en",rtl:m=!0})=>{let u=(0,i.useRef)(null),[h,x]=(0,i.useState)(e||"");(0,i.useEffect)(()=>{x(e)},[e]);let g=(0,i.useMemo)(()=>({readonly:d,placeholder:t,height:a,width:n,toolbar:c,language:p,direction:m?"rtl":"ltr",toolbarButtonSize:"middle",buttons:["source","|","bold","italic","underline","strikethrough","|","font","fontsize","brush","paragraph","|","align","indent","outdent","|","ul","ol","|","table","link","image","video","file","|","left","center","right","justify","imageCaption","|","hr","eraser","copyformat","|","superscript","subscript","|","selectall","cut","copy","paste","|","symbol","fullsize","print","about"],uploader:{insertImageAsBase64URI:!0},resizer:{min_width:10,min_height:10,showSize:!0},imageeditor:{crop:!0,resize:!0,resizeWidth:!0,resizeHeight:!0,ratio:!0},extraButtons:[{name:"imageCaption",tooltip:"Add caption to image",icon:"paragraph",exec:e=>{let r=e.selection.current();if(r&&"img"===r.nodeName.toLowerCase())try{let t=document.createElement("figure");t.className="image-with-caption",t.style.float="right",t.style.margin="10px 0 10px 15px",t.style.maxWidth="300px";let s=r.cloneNode(!0);s.style.float="none",s.style.margin="0",s.style.maxWidth="100%";let a=document.createElement("figcaption");a.innerHTML="وەسفی وێنە لێرە بنووسە...",a.style.textAlign="center",a.style.padding="5px",a.style.fontSize="14px",a.style.color="#666",t.appendChild(s),t.appendChild(a),e.selection.insertNode(t),r.parentNode&&r.parentNode.removeChild(r),e.selection.setCursorIn(a),console.log("Caption added successfully")}catch(e){console.error("Error adding caption:",e)}else alert("لطفا سەرەتا وێنەیەک دیاری بکە")}}],events:{afterInit:e=>{e.editor.style.direction="rtl",e.events.on("insertImage",e=>{e.style.float="right",e.style.margin="10px 0 10px 15px",e.style.maxWidth="300px"})}},style:{font:"Noto Kufi Arabic, sans-serif"}}),[d,t,a,n,c,p,m]);return(0,s.jsxs)("div",{className:"jsx-8cd3b8c15bb30d49 jodit-editor-wrapper",children:[(0,s.jsx)(l,{ref:u,value:h,config:g,tabIndex:1,onBlur:e=>{x(e),r(e)},onChange:e=>x(e)}),(0,s.jsx)(o(),{id:"8cd3b8c15bb30d49",children:'.jodit-editor-wrapper{margin-bottom:20px}.jodit-container{-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;font-family:"Noto Kufi Arabic",sans-serif;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px rgba(0,0,0,.1);box-shadow:0 1px 3px rgba(0,0,0,.1)}.jodit-wysiwyg{font-family:"Noto Kufi Arabic",sans-serif;padding:16px!important;font-size:16px!important;line-height:1.6!important}.jodit-toolbar__box{background-color:#f8f9fa;padding:8px!important}.jodit-toolbar-button{margin:0 3px;padding:6px!important}.jodit-toolbar-button__icon{width:20px!important;height:20px!important}.jodit-placeholder{font-family:"Noto Kufi Arabic",sans-serif;font-size:16px!important}.jodit-resizer{border:2px solid#8f5826!important}.jodit-resizer-point{background-color:#8f5826!important}.jodit-wysiwyg img{max-width:100%;height:auto;float:right;margin:10px 0 10px 15px}.jodit-wysiwyg p{overflow:auto}.jodit-wysiwyg p:after{content:"";display:table;clear:both}.jodit-wysiwyg{overflow:auto}.jodit-wysiwyg figure{display:inline-block;margin:0;max-width:100%}.jodit-wysiwyg figure.image-with-caption{float:right;margin:10px 0 10px 15px}.jodit-wysiwyg figure img{float:none;margin:0;display:block}.jodit-wysiwyg figcaption{text-align:center;font-size:14px;color:#666;padding:5px}'})]})}},79033:(e,r,t)=>{Promise.resolve().then(t.bind(t,30684))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,724,934,814,474,821,791],()=>t(36880));module.exports=s})();