"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[992],{7015:(e,t,n)=>{n.d(t,{AB:()=>l2,Dc:()=>G,GG:()=>uu,H9:()=>ls,HM:()=>l4,My:()=>l0,P:()=>lH,_M:()=>lJ,aU:()=>lh,c4:()=>uv,gS:()=>ud,kd:()=>uc,mZ:()=>uh,rJ:()=>li,x7:()=>uo});var r,i,s,a,o=n(2612),l=n(6391),u=n(796),h=n(9887),c=n(2107),d=n(927),f=n(9509),m=n(9641).Buffer;let g="@firebase/firestore",p="4.7.11";class y{constructor(e){this.uid=e}isAuthenticated(){return null!=this.uid}toKey(){return this.isAuthenticated()?"uid:"+this.uid:"anonymous-user"}isEqual(e){return e.uid===this.uid}}y.UNAUTHENTICATED=new y(null),y.GOOGLE_CREDENTIALS=new y("google-credentials-uid"),y.FIRST_PARTY=new y("first-party-uid"),y.MOCK_USER=new y("mock-user");let w="11.6.1",v=new u.Vy("@firebase/firestore");function I(){return v.logLevel}function T(e,...t){if(v.logLevel<=u.$b.DEBUG){let n=t.map(_);v.debug(`Firestore (${w}): ${e}`,...n)}}function E(e,...t){if(v.logLevel<=u.$b.ERROR){let n=t.map(_);v.error(`Firestore (${w}): ${e}`,...n)}}function b(e,...t){if(v.logLevel<=u.$b.WARN){let n=t.map(_);v.warn(`Firestore (${w}): ${e}`,...n)}}function _(e){if("string"==typeof e)return e;try{return JSON.stringify(e)}catch(t){return e}}function S(e,t,n){let r="Unexpected state";"string"==typeof t?r=t:n=t,x(e,r,n)}function x(e,t,n){let r=`FIRESTORE (${w}) INTERNAL ASSERTION FAILED: ${t} (ID: ${e.toString(16)})`;if(void 0!==n)try{r+=" CONTEXT: "+JSON.stringify(n)}catch(e){r+=" CONTEXT: "+n}throw E(r),Error(r)}function N(e,t,n,r){let i="Unexpected state";"string"==typeof n?i=n:r=n,e||x(t,i,r)}let C={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"};class D extends h.g{constructor(e,t){super(e,t),this.code=e,this.message=t,this.toString=()=>`${this.name}: [code=${this.code}]: ${this.message}`}}class A{constructor(){this.promise=new Promise((e,t)=>{this.resolve=e,this.reject=t})}}class k{constructor(e,t){this.user=t,this.type="OAuth",this.headers=new Map,this.headers.set("Authorization",`Bearer ${e}`)}}class R{getToken(){return Promise.resolve(null)}invalidateToken(){}start(e,t){e.enqueueRetryable(()=>t(y.UNAUTHENTICATED))}shutdown(){}}class V{constructor(e){this.token=e,this.changeListener=null}getToken(){return Promise.resolve(this.token)}invalidateToken(){}start(e,t){this.changeListener=t,e.enqueueRetryable(()=>t(this.token.user))}shutdown(){this.changeListener=null}}class M{constructor(e){this.t=e,this.currentUser=y.UNAUTHENTICATED,this.i=0,this.forceRefresh=!1,this.auth=null}start(e,t){N(void 0===this.o,42304);let n=this.i,r=e=>this.i!==n?(n=this.i,t(e)):Promise.resolve(),i=new A;this.o=()=>{this.i++,this.currentUser=this.u(),i.resolve(),i=new A,e.enqueueRetryable(()=>r(this.currentUser))};let s=()=>{let t=i;e.enqueueRetryable(async()=>{await t.promise,await r(this.currentUser)})},a=e=>{T("FirebaseAuthCredentialsProvider","Auth detected"),this.auth=e,this.o&&(this.auth.addAuthTokenListener(this.o),s())};this.t.onInit(e=>a(e)),setTimeout(()=>{if(!this.auth){let e=this.t.getImmediate({optional:!0});e?a(e):(T("FirebaseAuthCredentialsProvider","Auth not yet detected"),i.resolve(),i=new A)}},0),s()}getToken(){let e=this.i,t=this.forceRefresh;return this.forceRefresh=!1,this.auth?this.auth.getToken(t).then(t=>this.i!==e?(T("FirebaseAuthCredentialsProvider","getToken aborted due to token change."),this.getToken()):t?(N("string"==typeof t.accessToken,31837,{l:t}),new k(t.accessToken,this.currentUser)):null):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.auth&&this.o&&this.auth.removeAuthTokenListener(this.o),this.o=void 0}u(){let e=this.auth&&this.auth.getUid();return N(null===e||"string"==typeof e,2055,{h:e}),new y(e)}}class O{constructor(e,t,n){this.P=e,this.T=t,this.I=n,this.type="FirstParty",this.user=y.FIRST_PARTY,this.A=new Map}R(){return this.I?this.I():null}get headers(){this.A.set("X-Goog-AuthUser",this.P);let e=this.R();return e&&this.A.set("Authorization",e),this.T&&this.A.set("X-Goog-Iam-Authorization-Token",this.T),this.A}}class F{constructor(e,t,n){this.P=e,this.T=t,this.I=n}getToken(){return Promise.resolve(new O(this.P,this.T,this.I))}start(e,t){e.enqueueRetryable(()=>t(y.FIRST_PARTY))}shutdown(){}invalidateToken(){}}class L{constructor(e){this.value=e,this.type="AppCheck",this.headers=new Map,e&&e.length>0&&this.headers.set("x-firebase-appcheck",this.value)}}class P{constructor(e,t){this.V=t,this.forceRefresh=!1,this.appCheck=null,this.m=null,this.p=null,(0,o.xZ)(e)&&e.settings.appCheckToken&&(this.p=e.settings.appCheckToken)}start(e,t){N(void 0===this.o,3512);let n=e=>{null!=e.error&&T("FirebaseAppCheckTokenProvider",`Error getting App Check token; using placeholder token instead. Error: ${e.error.message}`);let n=e.token!==this.m;return this.m=e.token,T("FirebaseAppCheckTokenProvider",`Received ${n?"new":"existing"} token.`),n?t(e.token):Promise.resolve()};this.o=t=>{e.enqueueRetryable(()=>n(t))};let r=e=>{T("FirebaseAppCheckTokenProvider","AppCheck detected"),this.appCheck=e,this.o&&this.appCheck.addTokenListener(this.o)};this.V.onInit(e=>r(e)),setTimeout(()=>{if(!this.appCheck){let e=this.V.getImmediate({optional:!0});e?r(e):T("FirebaseAppCheckTokenProvider","AppCheck not yet detected")}},0)}getToken(){if(this.p)return Promise.resolve(new L(this.p));let e=this.forceRefresh;return this.forceRefresh=!1,this.appCheck?this.appCheck.getToken(e).then(e=>e?(N("string"==typeof e.token,44558,{tokenResult:e}),this.m=e.token,new L(e.token)):null):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.appCheck&&this.o&&this.appCheck.removeTokenListener(this.o),this.o=void 0}}function U(){return new TextEncoder}class q{static newId(){let e=62*Math.floor(256/62),t="";for(;t.length<20;){let n=function(e){let t="undefined"!=typeof self&&(self.crypto||self.msCrypto),n=new Uint8Array(40);if(t&&"function"==typeof t.getRandomValues)t.getRandomValues(n);else for(let e=0;e<40;e++)n[e]=Math.floor(256*Math.random());return n}(40);for(let r=0;r<n.length;++r)t.length<20&&n[r]<e&&(t+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(n[r]%62))}return t}}function B(e,t){return e<t?-1:+(e>t)}function z(e,t){let n=0;for(;n<e.length&&n<t.length;){let r=e.codePointAt(n),i=t.codePointAt(n);if(r!==i){if(r<128&&i<128)return B(r,i);{let s=U(),a=function(e,t){for(let n=0;n<e.length&&n<t.length;++n)if(e[n]!==t[n])return B(e[n],t[n]);return B(e.length,t.length)}(s.encode(K(e,n)),s.encode(K(t,n)));return 0!==a?a:B(r,i)}}n+=r>65535?2:1}return B(e.length,t.length)}function K(e,t){return e.codePointAt(t)>65535?e.substring(t,t+2):e.substring(t,t+1)}function $(e,t,n){return e.length===t.length&&e.every((e,r)=>n(e,t[r]))}class G{static now(){return G.fromMillis(Date.now())}static fromDate(e){return G.fromMillis(e.getTime())}static fromMillis(e){let t=Math.floor(e/1e3),n=Math.floor((e-1e3*t)*1e6);return new G(t,n)}constructor(e,t){if(this.seconds=e,this.nanoseconds=t,t<0||t>=1e9)throw new D(C.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+t);if(e<-0xe7791f700||e>=0x3afff44180)throw new D(C.INVALID_ARGUMENT,"Timestamp seconds out of range: "+e)}toDate(){return new Date(this.toMillis())}toMillis(){return 1e3*this.seconds+this.nanoseconds/1e6}_compareTo(e){return this.seconds===e.seconds?B(this.nanoseconds,e.nanoseconds):B(this.seconds,e.seconds)}isEqual(e){return e.seconds===this.seconds&&e.nanoseconds===this.nanoseconds}toString(){return"Timestamp(seconds="+this.seconds+", nanoseconds="+this.nanoseconds+")"}toJSON(){return{seconds:this.seconds,nanoseconds:this.nanoseconds}}valueOf(){return String(this.seconds- -0xe7791f700).padStart(12,"0")+"."+String(this.nanoseconds).padStart(9,"0")}}class j{static fromTimestamp(e){return new j(e)}static min(){return new j(new G(0,0))}static max(){return new j(new G(0x3afff4417f,0x3b9ac9ff))}constructor(e){this.timestamp=e}compareTo(e){return this.timestamp._compareTo(e.timestamp)}isEqual(e){return this.timestamp.isEqual(e.timestamp)}toMicroseconds(){return 1e6*this.timestamp.seconds+this.timestamp.nanoseconds/1e3}toString(){return"SnapshotVersion("+this.timestamp.toString()+")"}toTimestamp(){return this.timestamp}}let Q="__name__";class W{constructor(e,t,n){void 0===t?t=0:t>e.length&&S(637,{offset:t,range:e.length}),void 0===n?n=e.length-t:n>e.length-t&&S(1746,{length:n,range:e.length-t}),this.segments=e,this.offset=t,this.len=n}get length(){return this.len}isEqual(e){return 0===W.comparator(this,e)}child(e){let t=this.segments.slice(this.offset,this.limit());return e instanceof W?e.forEach(e=>{t.push(e)}):t.push(e),this.construct(t)}limit(){return this.offset+this.length}popFirst(e){return e=void 0===e?1:e,this.construct(this.segments,this.offset+e,this.length-e)}popLast(){return this.construct(this.segments,this.offset,this.length-1)}firstSegment(){return this.segments[this.offset]}lastSegment(){return this.get(this.length-1)}get(e){return this.segments[this.offset+e]}isEmpty(){return 0===this.length}isPrefixOf(e){if(e.length<this.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}isImmediateParentOf(e){if(this.length+1!==e.length)return!1;for(let t=0;t<this.length;t++)if(this.get(t)!==e.get(t))return!1;return!0}forEach(e){for(let t=this.offset,n=this.limit();t<n;t++)e(this.segments[t])}toArray(){return this.segments.slice(this.offset,this.limit())}static comparator(e,t){let n=Math.min(e.length,t.length);for(let r=0;r<n;r++){let n=W.compareSegments(e.get(r),t.get(r));if(0!==n)return n}return B(e.length,t.length)}static compareSegments(e,t){let n=W.isNumericId(e),r=W.isNumericId(t);return n&&!r?-1:!n&&r?1:n&&r?W.extractNumericId(e).compare(W.extractNumericId(t)):z(e,t)}static isNumericId(e){return e.startsWith("__id")&&e.endsWith("__")}static extractNumericId(e){return c.jz.fromString(e.substring(4,e.length-2))}}class H extends W{construct(e,t,n){return new H(e,t,n)}canonicalString(){return this.toArray().join("/")}toString(){return this.canonicalString()}toUriEncodedString(){return this.toArray().map(encodeURIComponent).join("/")}static fromString(...e){let t=[];for(let n of e){if(n.indexOf("//")>=0)throw new D(C.INVALID_ARGUMENT,`Invalid segment (${n}). Paths must not contain // in them.`);t.push(...n.split("/").filter(e=>e.length>0))}return new H(t)}static emptyPath(){return new H([])}}let Y=/^[_a-zA-Z][_a-zA-Z0-9]*$/;class J extends W{construct(e,t,n){return new J(e,t,n)}static isValidIdentifier(e){return Y.test(e)}canonicalString(){return this.toArray().map(e=>(e=e.replace(/\\/g,"\\\\").replace(/`/g,"\\`"),J.isValidIdentifier(e)||(e="`"+e+"`"),e)).join(".")}toString(){return this.canonicalString()}isKeyField(){return 1===this.length&&this.get(0)===Q}static keyField(){return new J([Q])}static fromServerFormat(e){let t=[],n="",r=0,i=()=>{if(0===n.length)throw new D(C.INVALID_ARGUMENT,`Invalid field path (${e}). Paths must not be empty, begin with '.', end with '.', or contain '..'`);t.push(n),n=""},s=!1;for(;r<e.length;){let t=e[r];if("\\"===t){if(r+1===e.length)throw new D(C.INVALID_ARGUMENT,"Path has trailing escape character: "+e);let t=e[r+1];if("\\"!==t&&"."!==t&&"`"!==t)throw new D(C.INVALID_ARGUMENT,"Path has invalid escape sequence: "+e);n+=t,r+=2}else"`"===t?s=!s:"."!==t||s?n+=t:i(),r++}if(i(),s)throw new D(C.INVALID_ARGUMENT,"Unterminated ` in path: "+e);return new J(t)}static emptyPath(){return new J([])}}class X{constructor(e){this.path=e}static fromPath(e){return new X(H.fromString(e))}static fromName(e){return new X(H.fromString(e).popFirst(5))}static empty(){return new X(H.emptyPath())}get collectionGroup(){return this.path.popLast().lastSegment()}hasCollectionId(e){return this.path.length>=2&&this.path.get(this.path.length-2)===e}getCollectionGroup(){return this.path.get(this.path.length-2)}getCollectionPath(){return this.path.popLast()}isEqual(e){return null!==e&&0===H.comparator(this.path,e.path)}toString(){return this.path.toString()}static comparator(e,t){return H.comparator(e.path,t.path)}static isDocumentKey(e){return e.length%2==0}static fromSegments(e){return new X(new H(e.slice()))}}class Z{constructor(e,t,n,r){this.indexId=e,this.collectionGroup=t,this.fields=n,this.indexState=r}}function ee(e){return e.fields.find(e=>2===e.kind)}function et(e){return e.fields.filter(e=>2!==e.kind)}Z.UNKNOWN_ID=-1;class en{constructor(e,t){this.fieldPath=e,this.kind=t}}class er{constructor(e,t){this.sequenceNumber=e,this.offset=t}static empty(){return new er(0,ea.min())}}function ei(e,t){let n=e.toTimestamp().seconds,r=e.toTimestamp().nanoseconds+1;return new ea(j.fromTimestamp(1e9===r?new G(n+1,0):new G(n,r)),X.empty(),t)}function es(e){return new ea(e.readTime,e.key,-1)}class ea{constructor(e,t,n){this.readTime=e,this.documentKey=t,this.largestBatchId=n}static min(){return new ea(j.min(),X.empty(),-1)}static max(){return new ea(j.max(),X.empty(),-1)}}function eo(e,t){let n=e.readTime.compareTo(t.readTime);return 0!==n||0!==(n=X.comparator(e.documentKey,t.documentKey))?n:B(e.largestBatchId,t.largestBatchId)}let el="The current tab is not in the required state to perform this operation. It might be necessary to refresh the browser tab.";class eu{constructor(){this.onCommittedListeners=[]}addOnCommittedListener(e){this.onCommittedListeners.push(e)}raiseOnCommittedEvent(){this.onCommittedListeners.forEach(e=>e())}}async function eh(e){if(e.code!==C.FAILED_PRECONDITION||e.message!==el)throw e;T("LocalStore","Unexpectedly lost primary lease")}class ec{constructor(e){this.nextCallback=null,this.catchCallback=null,this.result=void 0,this.error=void 0,this.isDone=!1,this.callbackAttached=!1,e(e=>{this.isDone=!0,this.result=e,this.nextCallback&&this.nextCallback(e)},e=>{this.isDone=!0,this.error=e,this.catchCallback&&this.catchCallback(e)})}catch(e){return this.next(void 0,e)}next(e,t){return this.callbackAttached&&S(59440),this.callbackAttached=!0,this.isDone?this.error?this.wrapFailure(t,this.error):this.wrapSuccess(e,this.result):new ec((n,r)=>{this.nextCallback=t=>{this.wrapSuccess(e,t).next(n,r)},this.catchCallback=e=>{this.wrapFailure(t,e).next(n,r)}})}toPromise(){return new Promise((e,t)=>{this.next(e,t)})}wrapUserFunction(e){try{let t=e();return t instanceof ec?t:ec.resolve(t)}catch(e){return ec.reject(e)}}wrapSuccess(e,t){return e?this.wrapUserFunction(()=>e(t)):ec.resolve(t)}wrapFailure(e,t){return e?this.wrapUserFunction(()=>e(t)):ec.reject(t)}static resolve(e){return new ec((t,n)=>{t(e)})}static reject(e){return new ec((t,n)=>{n(e)})}static waitFor(e){return new ec((t,n)=>{let r=0,i=0,s=!1;e.forEach(e=>{++r,e.next(()=>{++i,s&&i===r&&t()},e=>n(e))}),s=!0,i===r&&t()})}static or(e){let t=ec.resolve(!1);for(let n of e)t=t.next(e=>e?ec.resolve(e):n());return t}static forEach(e,t){let n=[];return e.forEach((e,r)=>{n.push(t.call(this,e,r))}),this.waitFor(n)}static mapArray(e,t){return new ec((n,r)=>{let i=e.length,s=Array(i),a=0;for(let o=0;o<i;o++){let l=o;t(e[l]).next(e=>{s[l]=e,++a===i&&n(s)},e=>r(e))}})}static doWhile(e,t){return new ec((n,r)=>{let i=()=>{!0===e()?t().next(()=>{i()},r):n()};i()})}}let ed="SimpleDb";class ef{static open(e,t,n,r){try{return new ef(t,e.transaction(r,n))}catch(e){throw new ey(t,e)}}constructor(e,t){this.action=e,this.transaction=t,this.aborted=!1,this.S=new A,this.transaction.oncomplete=()=>{this.S.resolve()},this.transaction.onabort=()=>{t.error?this.S.reject(new ey(e,t.error)):this.S.resolve()},this.transaction.onerror=t=>{let n=eE(t.target.error);this.S.reject(new ey(e,n))}}get D(){return this.S.promise}abort(e){e&&this.S.reject(e),this.aborted||(T(ed,"Aborting transaction:",e?e.message:"Client-initiated abort"),this.aborted=!0,this.transaction.abort())}v(){let e=this.transaction;this.aborted||"function"!=typeof e.commit||e.commit()}store(e){return new ev(this.transaction.objectStore(e))}}class em{static delete(e){return T(ed,"Removing database:",e),eI((0,h.mS)().indexedDB.deleteDatabase(e)).toPromise()}static C(){if(!(0,h.zW)())return!1;if(em.F())return!0;let e=(0,h.ZQ)(),t=em.M(e),n=eg(e);return!(e.indexOf("MSIE ")>0||e.indexOf("Trident/")>0||e.indexOf("Edge/")>0||0<t&&t<10||0<n&&n<4.5)}static F(){var e;return void 0!==f&&"YES"===(null==(e=f.__PRIVATE_env)?void 0:e.O)}static N(e,t){return e.store(t)}static M(e){let t=e.match(/i(?:phone|pad|pod) os ([\d_]+)/i);return Number(t?t[1].split("_").slice(0,2).join("."):"-1")}constructor(e,t,n){this.name=e,this.version=t,this.B=n,this.L=null,12.2===em.M((0,h.ZQ)())&&E("Firestore persistence suffers from a bug in iOS 12.2 Safari that may cause your app to stop working. See https://stackoverflow.com/q/56496296/110915 for details and a potential workaround.")}async k(e){return this.db||(T(ed,"Opening database:",this.name),this.db=await new Promise((t,n)=>{let r=indexedDB.open(this.name,this.version);r.onsuccess=e=>{t(e.target.result)},r.onblocked=()=>{n(new ey(e,"Cannot upgrade IndexedDB schema while another tab is open. Close all tabs that access Firestore and reload this page to proceed."))},r.onerror=t=>{let r=t.target.error;"VersionError"===r.name?n(new D(C.FAILED_PRECONDITION,"A newer version of the Firestore SDK was previously used and so the persisted data is not compatible with the version of the SDK you are now using. The SDK will operate with persistence disabled. If you need persistence, please re-upgrade to a newer version of the SDK or else clear the persisted IndexedDB data for your app to start fresh.")):"InvalidStateError"===r.name?n(new D(C.FAILED_PRECONDITION,"Unable to open an IndexedDB connection. This could be due to running in a private browsing session on a browser whose private browsing sessions do not support IndexedDB: "+r)):n(new ey(e,r))},r.onupgradeneeded=e=>{T(ed,'Database "'+this.name+'" requires upgrade from version:',e.oldVersion);let t=e.target.result;if(null!==this.L&&this.L!==e.oldVersion)throw Error(`refusing to open IndexedDB database due to potential corruption of the IndexedDB database data; this corruption could be caused by clicking the "clear site data" button in a web browser; try reloading the web page to re-initialize the IndexedDB database: lastClosedDbVersion=${this.L}, event.oldVersion=${e.oldVersion}, event.newVersion=${e.newVersion}, db.version=${t.version}`);this.B.q(t,r.transaction,e.oldVersion,this.version).next(()=>{T(ed,"Database upgrade to version "+this.version+" complete")})}}),this.db.addEventListener("close",e=>{let t=e.target;this.L=t.version},{passive:!0})),this.$&&(this.db.onversionchange=e=>this.$(e)),this.db}U(e){this.$=e,this.db&&(this.db.onversionchange=t=>e(t))}async runTransaction(e,t,n,r){let i="readonly"===t,s=0;for(;;){++s;try{this.db=await this.k(e);let t=ef.open(this.db,e,i?"readonly":"readwrite",n),s=r(t).next(e=>(t.v(),e)).catch(e=>(t.abort(e),ec.reject(e))).toPromise();return s.catch(()=>{}),await t.D,s}catch(t){let e="FirebaseError"!==t.name&&s<3;if(T(ed,"Transaction failed with error:",t.message,"Retrying:",e),this.close(),!e)return Promise.reject(t)}}}close(){this.db&&this.db.close(),this.db=void 0}}function eg(e){let t=e.match(/Android ([\d.]+)/i);return Number(t?t[1].split(".").slice(0,2).join("."):"-1")}class ep{constructor(e){this.K=e,this.W=!1,this.G=null}get isDone(){return this.W}get j(){return this.G}set cursor(e){this.K=e}done(){this.W=!0}H(e){this.G=e}delete(){return eI(this.K.delete())}}class ey extends D{constructor(e,t){super(C.UNAVAILABLE,`IndexedDB transaction '${e}' failed: ${t}`),this.name="IndexedDbTransactionError"}}function ew(e){return"IndexedDbTransactionError"===e.name}class ev{constructor(e){this.store=e}put(e,t){let n;return void 0!==t?(T(ed,"PUT",this.store.name,e,t),n=this.store.put(t,e)):(T(ed,"PUT",this.store.name,"<auto-key>",e),n=this.store.put(e)),eI(n)}add(e){return T(ed,"ADD",this.store.name,e,e),eI(this.store.add(e))}get(e){return eI(this.store.get(e)).next(t=>(void 0===t&&(t=null),T(ed,"GET",this.store.name,e,t),t))}delete(e){return T(ed,"DELETE",this.store.name,e),eI(this.store.delete(e))}count(){return T(ed,"COUNT",this.store.name),eI(this.store.count())}J(e,t){let n=this.options(e,t),r=n.index?this.store.index(n.index):this.store;if("function"==typeof r.getAll){let e=r.getAll(n.range);return new ec((t,n)=>{e.onerror=e=>{n(e.target.error)},e.onsuccess=e=>{t(e.target.result)}})}{let e=this.cursor(n),t=[];return this.Y(e,(e,n)=>{t.push(n)}).next(()=>t)}}Z(e,t){let n=this.store.getAll(e,null===t?void 0:t);return new ec((e,t)=>{n.onerror=e=>{t(e.target.error)},n.onsuccess=t=>{e(t.target.result)}})}X(e,t){T(ed,"DELETE ALL",this.store.name);let n=this.options(e,t);n.ee=!1;let r=this.cursor(n);return this.Y(r,(e,t,n)=>n.delete())}te(e,t){let n;t?n=e:(n={},t=e);let r=this.cursor(n);return this.Y(r,t)}ne(e){let t=this.cursor({});return new ec((n,r)=>{t.onerror=e=>{r(eE(e.target.error))},t.onsuccess=t=>{let r=t.target.result;r?e(r.primaryKey,r.value).next(e=>{e?r.continue():n()}):n()}})}Y(e,t){let n=[];return new ec((r,i)=>{e.onerror=e=>{i(e.target.error)},e.onsuccess=e=>{let i=e.target.result;if(!i)return void r();let s=new ep(i),a=t(i.primaryKey,i.value,s);if(a instanceof ec){let e=a.catch(e=>(s.done(),ec.reject(e)));n.push(e)}s.isDone?r():null===s.j?i.continue():i.continue(s.j)}}).next(()=>ec.waitFor(n))}options(e,t){let n;return void 0!==e&&("string"==typeof e?n=e:t=e),{index:n,range:t}}cursor(e){let t="next";if(e.reverse&&(t="prev"),e.index){let n=this.store.index(e.index);return e.ee?n.openKeyCursor(e.range,t):n.openCursor(e.range,t)}return this.store.openCursor(e.range,t)}}function eI(e){return new ec((t,n)=>{e.onsuccess=e=>{t(e.target.result)},e.onerror=e=>{n(eE(e.target.error))}})}let eT=!1;function eE(e){let t=em.M((0,h.ZQ)());if(t>=12.2&&t<13){let t="An internal error was encountered in the Indexed Database server";if(e.message.indexOf(t)>=0){let e=new D("internal",`IOS_INDEXEDDB_BUG1: IndexedDb has thrown '${t}'. This is likely due to an unavoidable bug in iOS. See https://stackoverflow.com/q/56496296/110915 for details and a potential workaround.`);return eT||(eT=!0,setTimeout(()=>{throw e},0)),e}}return e}let eb="IndexBackfiller";class e_{constructor(e,t){this.asyncQueue=e,this.re=t,this.task=null}start(){this.ie(15e3)}stop(){this.task&&(this.task.cancel(),this.task=null)}get started(){return null!==this.task}ie(e){T(eb,`Scheduled in ${e}ms`),this.task=this.asyncQueue.enqueueAfterDelay("index_backfill",e,async()=>{this.task=null;try{let e=await this.re.se();T(eb,`Documents written: ${e}`)}catch(e){ew(e)?T(eb,"Ignoring IndexedDB error during index backfill: ",e):await eh(e)}await this.ie(6e4)})}}class eS{constructor(e,t){this.localStore=e,this.persistence=t}async se(e=50){return this.persistence.runTransaction("Backfill Indexes","readwrite-primary",t=>this.oe(t,e))}oe(e,t){let n=new Set,r=t,i=!0;return ec.doWhile(()=>!0===i&&r>0,()=>this.localStore.indexManager.getNextCollectionGroupToUpdate(e).next(t=>{if(null!==t&&!n.has(t))return T(eb,`Processing collection: ${t}`),this._e(e,t,r).next(e=>{r-=e,n.add(t)});i=!1})).next(()=>t-r)}_e(e,t,n){return this.localStore.indexManager.getMinOffsetFromCollectionGroup(e,t).next(r=>this.localStore.localDocuments.getNextDocuments(e,t,r,n).next(n=>{let i=n.changes;return this.localStore.indexManager.updateIndexEntries(e,i).next(()=>this.ae(r,n)).next(n=>(T(eb,`Updating offset: ${n}`),this.localStore.indexManager.updateCollectionGroup(e,t,n))).next(()=>i.size)}))}ae(e,t){let n=e;return t.changes.forEach((e,t)=>{let r=es(t);eo(r,n)>0&&(n=r)}),new ea(n.readTime,n.documentKey,Math.max(t.batchId,e.largestBatchId))}}class ex{constructor(e,t){this.previousValue=e,t&&(t.sequenceNumberHandler=e=>this.ue(e),this.ce=e=>t.writeSequenceNumber(e))}ue(e){return this.previousValue=Math.max(e,this.previousValue),this.previousValue}next(){let e=++this.previousValue;return this.ce&&this.ce(e),e}}function eN(e){return null==e}function eC(e){return 0===e&&1/e==-1/0}function eD(e){return"number"==typeof e&&Number.isInteger(e)&&!eC(e)&&e<=Number.MAX_SAFE_INTEGER&&e>=Number.MIN_SAFE_INTEGER}function eA(e){let t="";for(let n=0;n<e.length;n++)t.length>0&&(t+="\x01\x01"),t=function(e,t){let n=t,r=e.length;for(let t=0;t<r;t++){let r=e.charAt(t);switch(r){case"\0":n+="\x01\x10";break;case"\x01":n+="\x01\x11";break;default:n+=r}}return n}(e.get(n),t);return t+"\x01\x01"}ex.le=-1;function ek(e){let t=e.length;if(N(t>=2,64408,{path:e}),2===t)return N("\x01"===e.charAt(0)&&"\x01"===e.charAt(1),56145,{path:e}),H.emptyPath();let n=t-2,r=[],i="";for(let s=0;s<t;){let t=e.indexOf("\x01",s);switch((t<0||t>n)&&S(50515,{path:e}),e.charAt(t+1)){case"\x01":let a,o=e.substring(s,t);0===i.length?a=o:(i+=o,a=i,i=""),r.push(a);break;case"\x10":i+=e.substring(s,t),i+="\0";break;case"\x11":i+=e.substring(s,t+1);break;default:S(61167,{path:e})}s=t+2}return new H(r)}let eR="remoteDocuments",eV="owner",eM="owner",eO="mutationQueues",eF="mutations",eL="batchId",eP="userMutationsIndex",eU=["userId","batchId"],eq={},eB="documentMutations",ez="remoteDocumentsV14",eK=["prefixPath","collectionGroup","readTime","documentId"],e$="documentKeyIndex",eG=["prefixPath","collectionGroup","documentId"],ej="collectionGroupIndex",eQ=["collectionGroup","readTime","prefixPath","documentId"],eW="remoteDocumentGlobal",eH="remoteDocumentGlobalKey",eY="targets",eJ="queryTargetsIndex",eX=["canonicalId","targetId"],eZ="targetDocuments",e0=["targetId","path"],e1="documentTargetsIndex",e2=["path","targetId"],e5="targetGlobalKey",e4="targetGlobal",e3="collectionParents",e6=["collectionId","parent"],e8="clientMetadata",e9="bundles",e7="namedQueries",te="indexConfiguration",tt="collectionGroupIndex",tn="indexState",tr=["indexId","uid"],ti="sequenceNumberIndex",ts=["uid","sequenceNumber"],ta="indexEntries",to=["indexId","uid","arrayValue","directionalValue","orderedDocumentKey","documentKey"],tl="documentKeyIndex",tu=["indexId","uid","orderedDocumentKey"],th="documentOverlays",tc=["userId","collectionPath","documentId"],td="collectionPathOverlayIndex",tf=["userId","collectionPath","largestBatchId"],tm="collectionGroupOverlayIndex",tg=["userId","collectionGroup","largestBatchId"],tp="globals",ty=[eO,eF,eB,eR,eY,eV,e4,eZ,e8,eW,e3,e9,e7],tw=[...ty,th],tv=[eO,eF,eB,ez,eY,eV,e4,eZ,e8,eW,e3,e9,e7,th],tI=[...tv,te,tn,ta],tT=[...tI,tp];class tE extends eu{constructor(e,t){super(),this.he=e,this.currentSequenceNumber=t}}function tb(e,t){return em.N(e.he,t)}function t_(e){let t=0;for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&t++;return t}function tS(e,t){for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&t(n,e[n])}function tx(e){for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t))return!1;return!0}class tN{constructor(e,t){this.comparator=e,this.root=t||tD.EMPTY}insert(e,t){return new tN(this.comparator,this.root.insert(e,t,this.comparator).copy(null,null,tD.BLACK,null,null))}remove(e){return new tN(this.comparator,this.root.remove(e,this.comparator).copy(null,null,tD.BLACK,null,null))}get(e){let t=this.root;for(;!t.isEmpty();){let n=this.comparator(e,t.key);if(0===n)return t.value;n<0?t=t.left:n>0&&(t=t.right)}return null}indexOf(e){let t=0,n=this.root;for(;!n.isEmpty();){let r=this.comparator(e,n.key);if(0===r)return t+n.left.size;r<0?n=n.left:(t+=n.left.size+1,n=n.right)}return -1}isEmpty(){return this.root.isEmpty()}get size(){return this.root.size}minKey(){return this.root.minKey()}maxKey(){return this.root.maxKey()}inorderTraversal(e){return this.root.inorderTraversal(e)}forEach(e){this.inorderTraversal((t,n)=>(e(t,n),!1))}toString(){let e=[];return this.inorderTraversal((t,n)=>(e.push(`${t}:${n}`),!1)),`{${e.join(", ")}}`}reverseTraversal(e){return this.root.reverseTraversal(e)}getIterator(){return new tC(this.root,null,this.comparator,!1)}getIteratorFrom(e){return new tC(this.root,e,this.comparator,!1)}getReverseIterator(){return new tC(this.root,null,this.comparator,!0)}getReverseIteratorFrom(e){return new tC(this.root,e,this.comparator,!0)}}class tC{constructor(e,t,n,r){this.isReverse=r,this.nodeStack=[];let i=1;for(;!e.isEmpty();)if(i=t?n(e.key,t):1,t&&r&&(i*=-1),i<0)e=this.isReverse?e.left:e.right;else{if(0===i){this.nodeStack.push(e);break}this.nodeStack.push(e),e=this.isReverse?e.right:e.left}}getNext(){let e=this.nodeStack.pop(),t={key:e.key,value:e.value};if(this.isReverse)for(e=e.left;!e.isEmpty();)this.nodeStack.push(e),e=e.right;else for(e=e.right;!e.isEmpty();)this.nodeStack.push(e),e=e.left;return t}hasNext(){return this.nodeStack.length>0}peek(){if(0===this.nodeStack.length)return null;let e=this.nodeStack[this.nodeStack.length-1];return{key:e.key,value:e.value}}}class tD{constructor(e,t,n,r,i){this.key=e,this.value=t,this.color=null!=n?n:tD.RED,this.left=null!=r?r:tD.EMPTY,this.right=null!=i?i:tD.EMPTY,this.size=this.left.size+1+this.right.size}copy(e,t,n,r,i){return new tD(null!=e?e:this.key,null!=t?t:this.value,null!=n?n:this.color,null!=r?r:this.left,null!=i?i:this.right)}isEmpty(){return!1}inorderTraversal(e){return this.left.inorderTraversal(e)||e(this.key,this.value)||this.right.inorderTraversal(e)}reverseTraversal(e){return this.right.reverseTraversal(e)||e(this.key,this.value)||this.left.reverseTraversal(e)}min(){return this.left.isEmpty()?this:this.left.min()}minKey(){return this.min().key}maxKey(){return this.right.isEmpty()?this.key:this.right.maxKey()}insert(e,t,n){let r=this,i=n(e,r.key);return(r=i<0?r.copy(null,null,null,r.left.insert(e,t,n),null):0===i?r.copy(null,t,null,null,null):r.copy(null,null,null,null,r.right.insert(e,t,n))).fixUp()}removeMin(){if(this.left.isEmpty())return tD.EMPTY;let e=this;return e.left.isRed()||e.left.left.isRed()||(e=e.moveRedLeft()),(e=e.copy(null,null,null,e.left.removeMin(),null)).fixUp()}remove(e,t){let n,r=this;if(0>t(e,r.key))r.left.isEmpty()||r.left.isRed()||r.left.left.isRed()||(r=r.moveRedLeft()),r=r.copy(null,null,null,r.left.remove(e,t),null);else{if(r.left.isRed()&&(r=r.rotateRight()),r.right.isEmpty()||r.right.isRed()||r.right.left.isRed()||(r=r.moveRedRight()),0===t(e,r.key)){if(r.right.isEmpty())return tD.EMPTY;n=r.right.min(),r=r.copy(n.key,n.value,null,null,r.right.removeMin())}r=r.copy(null,null,null,null,r.right.remove(e,t))}return r.fixUp()}isRed(){return this.color}fixUp(){let e=this;return e.right.isRed()&&!e.left.isRed()&&(e=e.rotateLeft()),e.left.isRed()&&e.left.left.isRed()&&(e=e.rotateRight()),e.left.isRed()&&e.right.isRed()&&(e=e.colorFlip()),e}moveRedLeft(){let e=this.colorFlip();return e.right.left.isRed()&&(e=(e=(e=e.copy(null,null,null,null,e.right.rotateRight())).rotateLeft()).colorFlip()),e}moveRedRight(){let e=this.colorFlip();return e.left.left.isRed()&&(e=(e=e.rotateRight()).colorFlip()),e}rotateLeft(){let e=this.copy(null,null,tD.RED,null,this.right.left);return this.right.copy(null,null,this.color,e,null)}rotateRight(){let e=this.copy(null,null,tD.RED,this.left.right,null);return this.left.copy(null,null,this.color,null,e)}colorFlip(){let e=this.left.copy(null,null,!this.left.color,null,null),t=this.right.copy(null,null,!this.right.color,null,null);return this.copy(null,null,!this.color,e,t)}checkMaxDepth(){return Math.pow(2,this.check())<=this.size+1}check(){if(this.isRed()&&this.left.isRed())throw S(43730,{key:this.key,value:this.value});if(this.right.isRed())throw S(14113,{key:this.key,value:this.value});let e=this.left.check();if(e!==this.right.check())throw S(27949);return e+ +!this.isRed()}}tD.EMPTY=null,tD.RED=!0,tD.BLACK=!1,tD.EMPTY=new class{constructor(){this.size=0}get key(){throw S(57766)}get value(){throw S(16141)}get color(){throw S(16727)}get left(){throw S(29726)}get right(){throw S(36894)}copy(e,t,n,r,i){return this}insert(e,t,n){return new tD(e,t)}remove(e,t){return this}isEmpty(){return!0}inorderTraversal(e){return!1}reverseTraversal(e){return!1}minKey(){return null}maxKey(){return null}isRed(){return!1}checkMaxDepth(){return!0}check(){return 0}};class tA{constructor(e){this.comparator=e,this.data=new tN(this.comparator)}has(e){return null!==this.data.get(e)}first(){return this.data.minKey()}last(){return this.data.maxKey()}get size(){return this.data.size}indexOf(e){return this.data.indexOf(e)}forEach(e){this.data.inorderTraversal((t,n)=>(e(t),!1))}forEachInRange(e,t){let n=this.data.getIteratorFrom(e[0]);for(;n.hasNext();){let r=n.getNext();if(this.comparator(r.key,e[1])>=0)return;t(r.key)}}forEachWhile(e,t){let n;for(n=void 0!==t?this.data.getIteratorFrom(t):this.data.getIterator();n.hasNext();)if(!e(n.getNext().key))return}firstAfterOrEqual(e){let t=this.data.getIteratorFrom(e);return t.hasNext()?t.getNext().key:null}getIterator(){return new tk(this.data.getIterator())}getIteratorFrom(e){return new tk(this.data.getIteratorFrom(e))}add(e){return this.copy(this.data.remove(e).insert(e,!0))}delete(e){return this.has(e)?this.copy(this.data.remove(e)):this}isEmpty(){return this.data.isEmpty()}unionWith(e){let t=this;return t.size<e.size&&(t=e,e=this),e.forEach(e=>{t=t.add(e)}),t}isEqual(e){if(!(e instanceof tA)||this.size!==e.size)return!1;let t=this.data.getIterator(),n=e.data.getIterator();for(;t.hasNext();){let e=t.getNext().key,r=n.getNext().key;if(0!==this.comparator(e,r))return!1}return!0}toArray(){let e=[];return this.forEach(t=>{e.push(t)}),e}toString(){let e=[];return this.forEach(t=>e.push(t)),"SortedSet("+e.toString()+")"}copy(e){let t=new tA(this.comparator);return t.data=e,t}}class tk{constructor(e){this.iter=e}getNext(){return this.iter.getNext().key}hasNext(){return this.iter.hasNext()}}function tR(e){return e.hasNext()?e.getNext():void 0}class tV{constructor(e){this.fields=e,e.sort(J.comparator)}static empty(){return new tV([])}unionWith(e){let t=new tA(J.comparator);for(let e of this.fields)t=t.add(e);for(let n of e)t=t.add(n);return new tV(t.toArray())}covers(e){for(let t of this.fields)if(t.isPrefixOf(e))return!0;return!1}isEqual(e){return $(this.fields,e.fields,(e,t)=>e.isEqual(t))}}class tM extends Error{constructor(){super(...arguments),this.name="Base64DecodeError"}}class tO{constructor(e){this.binaryString=e}static fromBase64String(e){return new tO(function(e){try{return atob(e)}catch(e){throw"undefined"!=typeof DOMException&&e instanceof DOMException?new tM("Invalid base64 string: "+e):e}}(e))}static fromUint8Array(e){return new tO(function(e){let t="";for(let n=0;n<e.length;++n)t+=String.fromCharCode(e[n]);return t}(e))}[Symbol.iterator](){let e=0;return{next:()=>e<this.binaryString.length?{value:this.binaryString.charCodeAt(e++),done:!1}:{value:void 0,done:!0}}}toBase64(){return btoa(this.binaryString)}toUint8Array(){var e=this.binaryString;let t=new Uint8Array(e.length);for(let n=0;n<e.length;n++)t[n]=e.charCodeAt(n);return t}approximateByteSize(){return 2*this.binaryString.length}compareTo(e){return B(this.binaryString,e.binaryString)}isEqual(e){return this.binaryString===e.binaryString}}tO.EMPTY_BYTE_STRING=new tO("");let tF=new RegExp(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(?:\.(\d+))?Z$/);function tL(e){if(N(!!e,39018),"string"==typeof e){let t=0,n=tF.exec(e);if(N(!!n,46558,{timestamp:e}),n[1]){let e=n[1];t=Number(e=(e+"000000000").substr(0,9))}return{seconds:Math.floor(new Date(e).getTime()/1e3),nanos:t}}return{seconds:tP(e.seconds),nanos:tP(e.nanos)}}function tP(e){return"number"==typeof e?e:"string"==typeof e?Number(e):0}function tU(e){return"string"==typeof e?tO.fromBase64String(e):tO.fromUint8Array(e)}let tq="server_timestamp",tB="__type__",tz="__previous_value__",tK="__local_write_time__";function t$(e){var t,n;return(null==(n=((null==(t=null==e?void 0:e.mapValue)?void 0:t.fields)||{})[tB])?void 0:n.stringValue)===tq}function tG(e){let t=e.mapValue.fields[tz];return t$(t)?tG(t):t}function tj(e){let t=tL(e.mapValue.fields[tK].timestampValue);return new G(t.seconds,t.nanos)}class tQ{constructor(e,t,n,r,i,s,a,o,l){this.databaseId=e,this.appId=t,this.persistenceKey=n,this.host=r,this.ssl=i,this.forceLongPolling=s,this.autoDetectLongPolling=a,this.longPollingOptions=o,this.useFetchStreams=l}}let tW="(default)";class tH{constructor(e,t){this.projectId=e,this.database=t||tW}static empty(){return new tH("","")}get isDefaultDatabase(){return this.database===tW}isEqual(e){return e instanceof tH&&e.projectId===this.projectId&&e.database===this.database}}let tY="__type__",tJ="__max__",tX={mapValue:{fields:{__type__:{stringValue:tJ}}}},tZ="__vector__",t0="value",t1={nullValue:"NULL_VALUE"};function t2(e){return"nullValue"in e?0:"booleanValue"in e?1:"integerValue"in e||"doubleValue"in e?2:"timestampValue"in e?3:"stringValue"in e?5:"bytesValue"in e?6:"referenceValue"in e?7:"geoPointValue"in e?8:"arrayValue"in e?9:"mapValue"in e?t$(e)?4:no(e)?0x1fffffffffffff:ns(e)?10:11:S(28295,{value:e})}function t5(e,t){if(e===t)return!0;let n=t2(e);if(n!==t2(t))return!1;switch(n){case 0:case 0x1fffffffffffff:return!0;case 1:return e.booleanValue===t.booleanValue;case 4:return tj(e).isEqual(tj(t));case 3:if("string"==typeof e.timestampValue&&"string"==typeof t.timestampValue&&e.timestampValue.length===t.timestampValue.length)return e.timestampValue===t.timestampValue;let r=tL(e.timestampValue),i=tL(t.timestampValue);return r.seconds===i.seconds&&r.nanos===i.nanos;case 5:return e.stringValue===t.stringValue;case 6:return tU(e.bytesValue).isEqual(tU(t.bytesValue));case 7:return e.referenceValue===t.referenceValue;case 8:return tP(e.geoPointValue.latitude)===tP(t.geoPointValue.latitude)&&tP(e.geoPointValue.longitude)===tP(t.geoPointValue.longitude);case 2:if("integerValue"in e&&"integerValue"in t)return tP(e.integerValue)===tP(t.integerValue);if("doubleValue"in e&&"doubleValue"in t){let n=tP(e.doubleValue),r=tP(t.doubleValue);return n===r?eC(n)===eC(r):isNaN(n)&&isNaN(r)}return!1;case 9:return $(e.arrayValue.values||[],t.arrayValue.values||[],t5);case 10:case 11:let s=e.mapValue.fields||{},a=t.mapValue.fields||{};if(t_(s)!==t_(a))return!1;for(let e in s)if(s.hasOwnProperty(e)&&(void 0===a[e]||!t5(s[e],a[e])))return!1;return!0;default:return S(52216,{left:e})}}function t4(e,t){return void 0!==(e.values||[]).find(e=>t5(e,t))}function t3(e,t){if(e===t)return 0;let n=t2(e),r=t2(t);if(n!==r)return B(n,r);switch(n){case 0:case 0x1fffffffffffff:return 0;case 1:return B(e.booleanValue,t.booleanValue);case 2:let i=tP(e.integerValue||e.doubleValue),s=tP(t.integerValue||t.doubleValue);return i<s?-1:i>s?1:i===s?0:isNaN(i)?isNaN(s)?0:-1:1;case 3:return t6(e.timestampValue,t.timestampValue);case 4:return t6(tj(e),tj(t));case 5:return z(e.stringValue,t.stringValue);case 6:return function(e,t){let n=tU(e),r=tU(t);return n.compareTo(r)}(e.bytesValue,t.bytesValue);case 7:return function(e,t){let n=e.split("/"),r=t.split("/");for(let e=0;e<n.length&&e<r.length;e++){let t=B(n[e],r[e]);if(0!==t)return t}return B(n.length,r.length)}(e.referenceValue,t.referenceValue);case 8:return function(e,t){let n=B(tP(e.latitude),tP(t.latitude));return 0!==n?n:B(tP(e.longitude),tP(t.longitude))}(e.geoPointValue,t.geoPointValue);case 9:return t8(e.arrayValue,t.arrayValue);case 10:return function(e,t){var n,r,i,s;let a=e.fields||{},o=t.fields||{},l=null==(n=a[t0])?void 0:n.arrayValue,u=null==(r=o[t0])?void 0:r.arrayValue,h=B((null==(i=null==l?void 0:l.values)?void 0:i.length)||0,(null==(s=null==u?void 0:u.values)?void 0:s.length)||0);return 0!==h?h:t8(l,u)}(e.mapValue,t.mapValue);case 11:return function(e,t){if(e===tX.mapValue&&t===tX.mapValue)return 0;if(e===tX.mapValue)return 1;if(t===tX.mapValue)return -1;let n=e.fields||{},r=Object.keys(n),i=t.fields||{},s=Object.keys(i);r.sort(),s.sort();for(let e=0;e<r.length&&e<s.length;++e){let t=z(r[e],s[e]);if(0!==t)return t;let a=t3(n[r[e]],i[s[e]]);if(0!==a)return a}return B(r.length,s.length)}(e.mapValue,t.mapValue);default:throw S(23264,{Pe:n})}}function t6(e,t){if("string"==typeof e&&"string"==typeof t&&e.length===t.length)return B(e,t);let n=tL(e),r=tL(t),i=B(n.seconds,r.seconds);return 0!==i?i:B(n.nanos,r.nanos)}function t8(e,t){let n=e.values||[],r=t.values||[];for(let e=0;e<n.length&&e<r.length;++e){let t=t3(n[e],r[e]);if(t)return t}return B(n.length,r.length)}function t9(e){var t,n;return"nullValue"in e?"null":"booleanValue"in e?""+e.booleanValue:"integerValue"in e?""+e.integerValue:"doubleValue"in e?""+e.doubleValue:"timestampValue"in e?function(e){let t=tL(e);return`time(${t.seconds},${t.nanos})`}(e.timestampValue):"stringValue"in e?e.stringValue:"bytesValue"in e?tU(e.bytesValue).toBase64():"referenceValue"in e?(t=e.referenceValue,X.fromName(t).toString()):"geoPointValue"in e?(n=e.geoPointValue,`geo(${n.latitude},${n.longitude})`):"arrayValue"in e?function(e){let t="[",n=!0;for(let r of e.values||[])n?n=!1:t+=",",t+=t9(r);return t+"]"}(e.arrayValue):"mapValue"in e?function(e){let t=Object.keys(e.fields||{}).sort(),n="{",r=!0;for(let i of t)r?r=!1:n+=",",n+=`${i}:${t9(e.fields[i])}`;return n+"}"}(e.mapValue):S(61005,{value:e})}function t7(e,t){return{referenceValue:`projects/${e.projectId}/databases/${e.database}/documents/${t.path.canonicalString()}`}}function ne(e){return!!e&&"integerValue"in e}function nt(e){return!!e&&"arrayValue"in e}function nn(e){return!!e&&"nullValue"in e}function nr(e){return!!e&&"doubleValue"in e&&isNaN(Number(e.doubleValue))}function ni(e){return!!e&&"mapValue"in e}function ns(e){var t,n;return(null==(n=((null==(t=null==e?void 0:e.mapValue)?void 0:t.fields)||{})[tY])?void 0:n.stringValue)===tZ}function na(e){if(e.geoPointValue)return{geoPointValue:Object.assign({},e.geoPointValue)};if(e.timestampValue&&"object"==typeof e.timestampValue)return{timestampValue:Object.assign({},e.timestampValue)};if(e.mapValue){let t={mapValue:{fields:{}}};return tS(e.mapValue.fields,(e,n)=>t.mapValue.fields[e]=na(n)),t}if(e.arrayValue){let t={arrayValue:{values:[]}};for(let n=0;n<(e.arrayValue.values||[]).length;++n)t.arrayValue.values[n]=na(e.arrayValue.values[n]);return t}return Object.assign({},e)}function no(e){return(((e.mapValue||{}).fields||{}).__type__||{}).stringValue===tJ}let nl={mapValue:{fields:{[tY]:{stringValue:tZ},[t0]:{arrayValue:{}}}}};function nu(e,t){let n=t3(e.value,t.value);return 0!==n?n:e.inclusive&&!t.inclusive?-1:!e.inclusive&&t.inclusive?1:0}function nh(e,t){let n=t3(e.value,t.value);return 0!==n?n:e.inclusive&&!t.inclusive?1:!e.inclusive&&t.inclusive?-1:0}class nc{constructor(e){this.value=e}static empty(){return new nc({mapValue:{}})}field(e){if(e.isEmpty())return this.value;{let t=this.value;for(let n=0;n<e.length-1;++n)if(!ni(t=(t.mapValue.fields||{})[e.get(n)]))return null;return(t=(t.mapValue.fields||{})[e.lastSegment()])||null}}set(e,t){this.getFieldsMap(e.popLast())[e.lastSegment()]=na(t)}setAll(e){let t=J.emptyPath(),n={},r=[];e.forEach((e,i)=>{if(!t.isImmediateParentOf(i)){let e=this.getFieldsMap(t);this.applyChanges(e,n,r),n={},r=[],t=i.popLast()}e?n[i.lastSegment()]=na(e):r.push(i.lastSegment())});let i=this.getFieldsMap(t);this.applyChanges(i,n,r)}delete(e){let t=this.field(e.popLast());ni(t)&&t.mapValue.fields&&delete t.mapValue.fields[e.lastSegment()]}isEqual(e){return t5(this.value,e.value)}getFieldsMap(e){let t=this.value;t.mapValue.fields||(t.mapValue={fields:{}});for(let n=0;n<e.length;++n){let r=t.mapValue.fields[e.get(n)];ni(r)&&r.mapValue.fields||(r={mapValue:{fields:{}}},t.mapValue.fields[e.get(n)]=r),t=r}return t.mapValue.fields}applyChanges(e,t,n){for(let r of(tS(t,(t,n)=>e[t]=n),n))delete e[r]}clone(){return new nc(na(this.value))}}class nd{constructor(e,t,n,r,i,s,a){this.key=e,this.documentType=t,this.version=n,this.readTime=r,this.createTime=i,this.data=s,this.documentState=a}static newInvalidDocument(e){return new nd(e,0,j.min(),j.min(),j.min(),nc.empty(),0)}static newFoundDocument(e,t,n,r){return new nd(e,1,t,j.min(),n,r,0)}static newNoDocument(e,t){return new nd(e,2,t,j.min(),j.min(),nc.empty(),0)}static newUnknownDocument(e,t){return new nd(e,3,t,j.min(),j.min(),nc.empty(),2)}convertToFoundDocument(e,t){return this.createTime.isEqual(j.min())&&(2===this.documentType||0===this.documentType)&&(this.createTime=e),this.version=e,this.documentType=1,this.data=t,this.documentState=0,this}convertToNoDocument(e){return this.version=e,this.documentType=2,this.data=nc.empty(),this.documentState=0,this}convertToUnknownDocument(e){return this.version=e,this.documentType=3,this.data=nc.empty(),this.documentState=2,this}setHasCommittedMutations(){return this.documentState=2,this}setHasLocalMutations(){return this.documentState=1,this.version=j.min(),this}setReadTime(e){return this.readTime=e,this}get hasLocalMutations(){return 1===this.documentState}get hasCommittedMutations(){return 2===this.documentState}get hasPendingWrites(){return this.hasLocalMutations||this.hasCommittedMutations}isValidDocument(){return 0!==this.documentType}isFoundDocument(){return 1===this.documentType}isNoDocument(){return 2===this.documentType}isUnknownDocument(){return 3===this.documentType}isEqual(e){return e instanceof nd&&this.key.isEqual(e.key)&&this.version.isEqual(e.version)&&this.documentType===e.documentType&&this.documentState===e.documentState&&this.data.isEqual(e.data)}mutableCopy(){return new nd(this.key,this.documentType,this.version,this.readTime,this.createTime,this.data.clone(),this.documentState)}toString(){return`Document(${this.key}, ${this.version}, ${JSON.stringify(this.data.value)}, {createTime: ${this.createTime}}), {documentType: ${this.documentType}}), {documentState: ${this.documentState}})`}}class nf{constructor(e,t){this.position=e,this.inclusive=t}}function nm(e,t,n){let r=0;for(let i=0;i<e.position.length;i++){let s=t[i],a=e.position[i];if(r=s.field.isKeyField()?X.comparator(X.fromName(a.referenceValue),n.key):t3(a,n.data.field(s.field)),"desc"===s.dir&&(r*=-1),0!==r)break}return r}function ng(e,t){if(null===e)return null===t;if(null===t||e.inclusive!==t.inclusive||e.position.length!==t.position.length)return!1;for(let n=0;n<e.position.length;n++)if(!t5(e.position[n],t.position[n]))return!1;return!0}class np{constructor(e,t="asc"){this.field=e,this.dir=t}}class ny{}class nw extends ny{constructor(e,t,n){super(),this.field=e,this.op=t,this.value=n}static create(e,t,n){return e.isKeyField()?"in"===t||"not-in"===t?this.createKeyFieldInFilter(e,t,n):new nS(e,t,n):"array-contains"===t?new nD(e,n):"in"===t?new nA(e,n):"not-in"===t?new nk(e,n):"array-contains-any"===t?new nR(e,n):new nw(e,t,n)}static createKeyFieldInFilter(e,t,n){return"in"===t?new nx(e,n):new nN(e,n)}matches(e){let t=e.data.field(this.field);return"!="===this.op?null!==t&&void 0===t.nullValue&&this.matchesComparison(t3(t,this.value)):null!==t&&t2(this.value)===t2(t)&&this.matchesComparison(t3(t,this.value))}matchesComparison(e){switch(this.op){case"<":return e<0;case"<=":return e<=0;case"==":return 0===e;case"!=":return 0!==e;case">":return e>0;case">=":return e>=0;default:return S(47266,{operator:this.op})}}isInequality(){return["<","<=",">",">=","!=","not-in"].indexOf(this.op)>=0}getFlattenedFilters(){return[this]}getFilters(){return[this]}}class nv extends ny{constructor(e,t){super(),this.filters=e,this.op=t,this.Te=null}static create(e,t){return new nv(e,t)}matches(e){return nI(this)?void 0===this.filters.find(t=>!t.matches(e)):void 0!==this.filters.find(t=>t.matches(e))}getFlattenedFilters(){return null!==this.Te||(this.Te=this.filters.reduce((e,t)=>e.concat(t.getFlattenedFilters()),[])),this.Te}getFilters(){return Object.assign([],this.filters)}}function nI(e){return"and"===e.op}function nT(e){return"or"===e.op}function nE(e){return nb(e)&&nI(e)}function nb(e){for(let t of e.filters)if(t instanceof nv)return!1;return!0}function n_(e,t){let n=e.filters.concat(t);return nv.create(n,e.op)}class nS extends nw{constructor(e,t,n){super(e,t,n),this.key=X.fromName(n.referenceValue)}matches(e){let t=X.comparator(e.key,this.key);return this.matchesComparison(t)}}class nx extends nw{constructor(e,t){super(e,"in",t),this.keys=nC("in",t)}matches(e){return this.keys.some(t=>t.isEqual(e.key))}}class nN extends nw{constructor(e,t){super(e,"not-in",t),this.keys=nC("not-in",t)}matches(e){return!this.keys.some(t=>t.isEqual(e.key))}}function nC(e,t){var n;return((null==(n=t.arrayValue)?void 0:n.values)||[]).map(e=>X.fromName(e.referenceValue))}class nD extends nw{constructor(e,t){super(e,"array-contains",t)}matches(e){let t=e.data.field(this.field);return nt(t)&&t4(t.arrayValue,this.value)}}class nA extends nw{constructor(e,t){super(e,"in",t)}matches(e){let t=e.data.field(this.field);return null!==t&&t4(this.value.arrayValue,t)}}class nk extends nw{constructor(e,t){super(e,"not-in",t)}matches(e){if(t4(this.value.arrayValue,{nullValue:"NULL_VALUE"}))return!1;let t=e.data.field(this.field);return null!==t&&void 0===t.nullValue&&!t4(this.value.arrayValue,t)}}class nR extends nw{constructor(e,t){super(e,"array-contains-any",t)}matches(e){let t=e.data.field(this.field);return!(!nt(t)||!t.arrayValue.values)&&t.arrayValue.values.some(e=>t4(this.value.arrayValue,e))}}class nV{constructor(e,t=null,n=[],r=[],i=null,s=null,a=null){this.path=e,this.collectionGroup=t,this.orderBy=n,this.filters=r,this.limit=i,this.startAt=s,this.endAt=a,this.Ie=null}}function nM(e,t=null,n=[],r=[],i=null,s=null,a=null){return new nV(e,t,n,r,i,s,a)}function nO(e){if(null===e.Ie){let t=e.path.canonicalString();null!==e.collectionGroup&&(t+="|cg:"+e.collectionGroup),t+="|f:",t+=e.filters.map(e=>(function e(t){if(t instanceof nw)return t.field.canonicalString()+t.op.toString()+t9(t.value);if(nE(t))return t.filters.map(t=>e(t)).join(",");{let n=t.filters.map(t=>e(t)).join(",");return`${t.op}(${n})`}})(e)).join(","),t+="|ob:",t+=e.orderBy.map(e=>e.field.canonicalString()+e.dir).join(","),eN(e.limit)||(t+="|l:",t+=e.limit),e.startAt&&(t+="|lb:",t+=e.startAt.inclusive?"b:":"a:",t+=e.startAt.position.map(e=>t9(e)).join(",")),e.endAt&&(t+="|ub:",t+=e.endAt.inclusive?"a:":"b:",t+=e.endAt.position.map(e=>t9(e)).join(",")),e.Ie=t}return e.Ie}function nF(e,t){if(e.limit!==t.limit||e.orderBy.length!==t.orderBy.length)return!1;for(let i=0;i<e.orderBy.length;i++){var n,r;if(n=e.orderBy[i],r=t.orderBy[i],!(n.dir===r.dir&&n.field.isEqual(r.field)))return!1}if(e.filters.length!==t.filters.length)return!1;for(let n=0;n<e.filters.length;n++)if(!function e(t,n){return t instanceof nw?n instanceof nw&&t.op===n.op&&t.field.isEqual(n.field)&&t5(t.value,n.value):t instanceof nv?n instanceof nv&&t.op===n.op&&t.filters.length===n.filters.length&&t.filters.reduce((t,r,i)=>t&&e(r,n.filters[i]),!0):void S(19439)}(e.filters[n],t.filters[n]))return!1;return e.collectionGroup===t.collectionGroup&&!!e.path.isEqual(t.path)&&!!ng(e.startAt,t.startAt)&&ng(e.endAt,t.endAt)}function nL(e){return X.isDocumentKey(e.path)&&null===e.collectionGroup&&0===e.filters.length}function nP(e,t){return e.filters.filter(e=>e instanceof nw&&e.field.isEqual(t))}function nU(e,t,n){let r=t1,i=!0;for(let n of nP(e,t)){let e=t1,t=!0;switch(n.op){case"<":case"<=":var s;e="nullValue"in(s=n.value)?t1:"booleanValue"in s?{booleanValue:!1}:"integerValue"in s||"doubleValue"in s?{doubleValue:NaN}:"timestampValue"in s?{timestampValue:{seconds:Number.MIN_SAFE_INTEGER}}:"stringValue"in s?{stringValue:""}:"bytesValue"in s?{bytesValue:""}:"referenceValue"in s?t7(tH.empty(),X.empty()):"geoPointValue"in s?{geoPointValue:{latitude:-90,longitude:-180}}:"arrayValue"in s?{arrayValue:{}}:"mapValue"in s?ns(s)?nl:{mapValue:{}}:S(35942,{value:s});break;case"==":case"in":case">=":e=n.value;break;case">":e=n.value,t=!1;break;case"!=":case"not-in":e=t1}0>nu({value:r,inclusive:i},{value:e,inclusive:t})&&(r=e,i=t)}if(null!==n){for(let s=0;s<e.orderBy.length;++s)if(e.orderBy[s].field.isEqual(t)){let e=n.position[s];0>nu({value:r,inclusive:i},{value:e,inclusive:n.inclusive})&&(r=e,i=n.inclusive);break}}return{value:r,inclusive:i}}function nq(e,t,n){let r=tX,i=!0;for(let n of nP(e,t)){let e=tX,t=!0;switch(n.op){case">=":case">":var s;e="nullValue"in(s=n.value)?{booleanValue:!1}:"booleanValue"in s?{doubleValue:NaN}:"integerValue"in s||"doubleValue"in s?{timestampValue:{seconds:Number.MIN_SAFE_INTEGER}}:"timestampValue"in s?{stringValue:""}:"stringValue"in s?{bytesValue:""}:"bytesValue"in s?t7(tH.empty(),X.empty()):"referenceValue"in s?{geoPointValue:{latitude:-90,longitude:-180}}:"geoPointValue"in s?{arrayValue:{}}:"arrayValue"in s?nl:"mapValue"in s?ns(s)?{mapValue:{}}:tX:S(61959,{value:s}),t=!1;break;case"==":case"in":case"<=":e=n.value;break;case"<":e=n.value,t=!1;break;case"!=":case"not-in":e=tX}nh({value:r,inclusive:i},{value:e,inclusive:t})>0&&(r=e,i=t)}if(null!==n){for(let s=0;s<e.orderBy.length;++s)if(e.orderBy[s].field.isEqual(t)){let e=n.position[s];nh({value:r,inclusive:i},{value:e,inclusive:n.inclusive})>0&&(r=e,i=n.inclusive);break}}return{value:r,inclusive:i}}class nB{constructor(e,t=null,n=[],r=[],i=null,s="F",a=null,o=null){this.path=e,this.collectionGroup=t,this.explicitOrderBy=n,this.filters=r,this.limit=i,this.limitType=s,this.startAt=a,this.endAt=o,this.Ee=null,this.de=null,this.Ae=null,this.startAt,this.endAt}}function nz(e){return new nB(e)}function nK(e){return 0===e.filters.length&&null===e.limit&&null==e.startAt&&null==e.endAt&&(0===e.explicitOrderBy.length||1===e.explicitOrderBy.length&&e.explicitOrderBy[0].field.isKeyField())}function n$(e){return null!==e.collectionGroup}function nG(e){if(null===e.Ee){let t;e.Ee=[];let n=new Set;for(let t of e.explicitOrderBy)e.Ee.push(t),n.add(t.field.canonicalString());let r=e.explicitOrderBy.length>0?e.explicitOrderBy[e.explicitOrderBy.length-1].dir:"asc";(t=new tA(J.comparator),e.filters.forEach(e=>{e.getFlattenedFilters().forEach(e=>{e.isInequality()&&(t=t.add(e.field))})}),t).forEach(t=>{n.has(t.canonicalString())||t.isKeyField()||e.Ee.push(new np(t,r))}),n.has(J.keyField().canonicalString())||e.Ee.push(new np(J.keyField(),r))}return e.Ee}function nj(e){return e.de||(e.de=nQ(e,nG(e))),e.de}function nQ(e,t){if("F"===e.limitType)return nM(e.path,e.collectionGroup,t,e.filters,e.limit,e.startAt,e.endAt);{t=t.map(e=>{let t="desc"===e.dir?"asc":"desc";return new np(e.field,t)});let n=e.endAt?new nf(e.endAt.position,e.endAt.inclusive):null,r=e.startAt?new nf(e.startAt.position,e.startAt.inclusive):null;return nM(e.path,e.collectionGroup,t,e.filters,e.limit,n,r)}}function nW(e,t){let n=e.filters.concat([t]);return new nB(e.path,e.collectionGroup,e.explicitOrderBy.slice(),n,e.limit,e.limitType,e.startAt,e.endAt)}function nH(e,t,n){return new nB(e.path,e.collectionGroup,e.explicitOrderBy.slice(),e.filters.slice(),t,n,e.startAt,e.endAt)}function nY(e,t){return nF(nj(e),nj(t))&&e.limitType===t.limitType}function nJ(e){return`${nO(nj(e))}|lt:${e.limitType}`}function nX(e){var t;let n;return`Query(target=${n=(t=nj(e)).path.canonicalString(),null!==t.collectionGroup&&(n+=" collectionGroup="+t.collectionGroup),t.filters.length>0&&(n+=`, filters: [${t.filters.map(e=>(function e(t){return t instanceof nw?`${t.field.canonicalString()} ${t.op} ${t9(t.value)}`:t instanceof nv?t.op.toString()+" {"+t.getFilters().map(e).join(" ,")+"}":"Filter"})(e)).join(", ")}]`),eN(t.limit)||(n+=", limit: "+t.limit),t.orderBy.length>0&&(n+=`, orderBy: [${t.orderBy.map(e=>`${e.field.canonicalString()} (${e.dir})`).join(", ")}]`),t.startAt&&(n+=", startAt: ",n+=t.startAt.inclusive?"b:":"a:",n+=t.startAt.position.map(e=>t9(e)).join(",")),t.endAt&&(n+=", endAt: ",n+=t.endAt.inclusive?"a:":"b:",n+=t.endAt.position.map(e=>t9(e)).join(",")),`Target(${n})`}; limitType=${e.limitType})`}function nZ(e,t){return t.isFoundDocument()&&function(e,t){let n=t.key.path;return null!==e.collectionGroup?t.key.hasCollectionId(e.collectionGroup)&&e.path.isPrefixOf(n):X.isDocumentKey(e.path)?e.path.isEqual(n):e.path.isImmediateParentOf(n)}(e,t)&&function(e,t){for(let n of nG(e))if(!n.field.isKeyField()&&null===t.data.field(n.field))return!1;return!0}(e,t)&&function(e,t){for(let n of e.filters)if(!n.matches(t))return!1;return!0}(e,t)&&(!e.startAt||!!function(e,t,n){let r=nm(e,t,n);return e.inclusive?r<=0:r<0}(e.startAt,nG(e),t))&&(!e.endAt||!!function(e,t,n){let r=nm(e,t,n);return e.inclusive?r>=0:r>0}(e.endAt,nG(e),t))}function n0(e){return e.collectionGroup||(e.path.length%2==1?e.path.lastSegment():e.path.get(e.path.length-2))}function n1(e){return(t,n)=>{let r=!1;for(let i of nG(e)){let e=function(e,t,n){let r=e.field.isKeyField()?X.comparator(t.key,n.key):function(e,t,n){let r=t.data.field(e),i=n.data.field(e);return null!==r&&null!==i?t3(r,i):S(42886)}(e.field,t,n);switch(e.dir){case"asc":return r;case"desc":return -1*r;default:return S(19790,{direction:e.dir})}}(i,t,n);if(0!==e)return e;r=r||i.field.isKeyField()}return 0}}class n2{constructor(e,t){this.mapKeyFn=e,this.equalsFn=t,this.inner={},this.innerSize=0}get(e){let t=this.mapKeyFn(e),n=this.inner[t];if(void 0!==n){for(let[t,r]of n)if(this.equalsFn(t,e))return r}}has(e){return void 0!==this.get(e)}set(e,t){let n=this.mapKeyFn(e),r=this.inner[n];if(void 0===r)return this.inner[n]=[[e,t]],void this.innerSize++;for(let n=0;n<r.length;n++)if(this.equalsFn(r[n][0],e))return void(r[n]=[e,t]);r.push([e,t]),this.innerSize++}delete(e){let t=this.mapKeyFn(e),n=this.inner[t];if(void 0===n)return!1;for(let r=0;r<n.length;r++)if(this.equalsFn(n[r][0],e))return 1===n.length?delete this.inner[t]:n.splice(r,1),this.innerSize--,!0;return!1}forEach(e){tS(this.inner,(t,n)=>{for(let[t,r]of n)e(t,r)})}isEmpty(){return tx(this.inner)}size(){return this.innerSize}}let n5=new tN(X.comparator),n4=new tN(X.comparator);function n3(...e){let t=n4;for(let n of e)t=t.insert(n.key,n);return t}function n6(e){let t=n4;return e.forEach((e,n)=>t=t.insert(e,n.overlayedDocument)),t}function n8(){return new n2(e=>e.toString(),(e,t)=>e.isEqual(t))}let n9=new tN(X.comparator),n7=new tA(X.comparator);function re(...e){let t=n7;for(let n of e)t=t.add(n);return t}let rt=new tA(B);function rn(e,t){if(e.useProto3Json){if(isNaN(t))return{doubleValue:"NaN"};if(t===1/0)return{doubleValue:"Infinity"};if(t===-1/0)return{doubleValue:"-Infinity"}}return{doubleValue:eC(t)?"-0":t}}function rr(e){return{integerValue:""+e}}function ri(e,t){return eD(t)?rr(t):rn(e,t)}class rs{constructor(){this._=void 0}}function ra(e,t){return e instanceof rd?ne(t)||t&&"doubleValue"in t?t:{integerValue:0}:null}class ro extends rs{}class rl extends rs{constructor(e){super(),this.elements=e}}function ru(e,t){let n=rm(t);for(let t of e.elements)n.some(e=>t5(e,t))||n.push(t);return{arrayValue:{values:n}}}class rh extends rs{constructor(e){super(),this.elements=e}}function rc(e,t){let n=rm(t);for(let t of e.elements)n=n.filter(e=>!t5(e,t));return{arrayValue:{values:n}}}class rd extends rs{constructor(e,t){super(),this.serializer=e,this.Re=t}}function rf(e){return tP(e.integerValue||e.doubleValue)}function rm(e){return nt(e)&&e.arrayValue.values?e.arrayValue.values.slice():[]}class rg{constructor(e,t){this.field=e,this.transform=t}}class rp{constructor(e,t){this.version=e,this.transformResults=t}}class ry{constructor(e,t){this.updateTime=e,this.exists=t}static none(){return new ry}static exists(e){return new ry(void 0,e)}static updateTime(e){return new ry(e)}get isNone(){return void 0===this.updateTime&&void 0===this.exists}isEqual(e){return this.exists===e.exists&&(this.updateTime?!!e.updateTime&&this.updateTime.isEqual(e.updateTime):!e.updateTime)}}function rw(e,t){return void 0!==e.updateTime?t.isFoundDocument()&&t.version.isEqual(e.updateTime):void 0===e.exists||e.exists===t.isFoundDocument()}class rv{}function rI(e,t){if(!e.hasLocalMutations||t&&0===t.fields.length)return null;if(null===t)return e.isNoDocument()?new rC(e.key,ry.none()):new rb(e.key,e.data,ry.none());{let n=e.data,r=nc.empty(),i=new tA(J.comparator);for(let e of t.fields)if(!i.has(e)){let t=n.field(e);null===t&&e.length>1&&(e=e.popLast(),t=n.field(e)),null===t?r.delete(e):r.set(e,t),i=i.add(e)}return new r_(e.key,r,new tV(i.toArray()),ry.none())}}function rT(e,t,n,r){return e instanceof rb?function(e,t,n,r){if(!rw(e.precondition,t))return n;let i=e.value.clone(),s=rN(e.fieldTransforms,r,t);return i.setAll(s),t.convertToFoundDocument(t.version,i).setHasLocalMutations(),null}(e,t,n,r):e instanceof r_?function(e,t,n,r){if(!rw(e.precondition,t))return n;let i=rN(e.fieldTransforms,r,t),s=t.data;return(s.setAll(rS(e)),s.setAll(i),t.convertToFoundDocument(t.version,s).setHasLocalMutations(),null===n)?null:n.unionWith(e.fieldMask.fields).unionWith(e.fieldTransforms.map(e=>e.field))}(e,t,n,r):rw(e.precondition,t)?(t.convertToNoDocument(t.version).setHasLocalMutations(),null):n}function rE(e,t){var n,r;return e.type===t.type&&!!e.key.isEqual(t.key)&&!!e.precondition.isEqual(t.precondition)&&(n=e.fieldTransforms,r=t.fieldTransforms,!!(void 0===n&&void 0===r||!(!n||!r)&&$(n,r,(e,t)=>{var n,r;return e.field.isEqual(t.field)&&(n=e.transform,r=t.transform,n instanceof rl&&r instanceof rl||n instanceof rh&&r instanceof rh?$(n.elements,r.elements,t5):n instanceof rd&&r instanceof rd?t5(n.Re,r.Re):n instanceof ro&&r instanceof ro)})))&&(0===e.type?e.value.isEqual(t.value):1!==e.type||e.data.isEqual(t.data)&&e.fieldMask.isEqual(t.fieldMask))}class rb extends rv{constructor(e,t,n,r=[]){super(),this.key=e,this.value=t,this.precondition=n,this.fieldTransforms=r,this.type=0}getFieldMask(){return null}}class r_ extends rv{constructor(e,t,n,r,i=[]){super(),this.key=e,this.data=t,this.fieldMask=n,this.precondition=r,this.fieldTransforms=i,this.type=1}getFieldMask(){return this.fieldMask}}function rS(e){let t=new Map;return e.fieldMask.fields.forEach(n=>{if(!n.isEmpty()){let r=e.data.field(n);t.set(n,r)}}),t}function rx(e,t,n){let r=new Map;N(e.length===n.length,32656,{Ve:n.length,me:e.length});for(let s=0;s<n.length;s++){var i;let a=e[s],o=a.transform,l=t.data.field(a.field);r.set(a.field,(i=n[s],o instanceof rl?ru(o,l):o instanceof rh?rc(o,l):i))}return r}function rN(e,t,n){let r=new Map;for(let i of e){let e=i.transform,s=n.data.field(i.field);r.set(i.field,e instanceof ro?function(e,t){let n={fields:{[tB]:{stringValue:tq},[tK]:{timestampValue:{seconds:e.seconds,nanos:e.nanoseconds}}}};return t&&t$(t)&&(t=tG(t)),t&&(n.fields[tz]=t),{mapValue:n}}(t,s):e instanceof rl?ru(e,s):e instanceof rh?rc(e,s):function(e,t){let n=ra(e,t),r=rf(n)+rf(e.Re);return ne(n)&&ne(e.Re)?rr(r):rn(e.serializer,r)}(e,s))}return r}class rC extends rv{constructor(e,t){super(),this.key=e,this.precondition=t,this.type=2,this.fieldTransforms=[]}getFieldMask(){return null}}class rD extends rv{constructor(e,t){super(),this.key=e,this.precondition=t,this.type=3,this.fieldTransforms=[]}getFieldMask(){return null}}class rA{constructor(e,t,n,r){this.batchId=e,this.localWriteTime=t,this.baseMutations=n,this.mutations=r}applyToRemoteDocument(e,t){let n=t.mutationResults;for(let t=0;t<this.mutations.length;t++){let r=this.mutations[t];r.key.isEqual(e.key)&&function(e,t,n){e instanceof rb?function(e,t,n){let r=e.value.clone(),i=rx(e.fieldTransforms,t,n.transformResults);r.setAll(i),t.convertToFoundDocument(n.version,r).setHasCommittedMutations()}(e,t,n):e instanceof r_?function(e,t,n){if(!rw(e.precondition,t))return t.convertToUnknownDocument(n.version);let r=rx(e.fieldTransforms,t,n.transformResults),i=t.data;i.setAll(rS(e)),i.setAll(r),t.convertToFoundDocument(n.version,i).setHasCommittedMutations()}(e,t,n):t.convertToNoDocument(n.version).setHasCommittedMutations()}(r,e,n[t])}}applyToLocalView(e,t){for(let n of this.baseMutations)n.key.isEqual(e.key)&&(t=rT(n,e,t,this.localWriteTime));for(let n of this.mutations)n.key.isEqual(e.key)&&(t=rT(n,e,t,this.localWriteTime));return t}applyToLocalDocumentSet(e,t){let n=n8();return this.mutations.forEach(r=>{let i=e.get(r.key),s=i.overlayedDocument,a=this.applyToLocalView(s,i.mutatedFields),o=rI(s,a=t.has(r.key)?null:a);null!==o&&n.set(r.key,o),s.isValidDocument()||s.convertToNoDocument(j.min())}),n}keys(){return this.mutations.reduce((e,t)=>e.add(t.key),re())}isEqual(e){return this.batchId===e.batchId&&$(this.mutations,e.mutations,(e,t)=>rE(e,t))&&$(this.baseMutations,e.baseMutations,(e,t)=>rE(e,t))}}class rk{constructor(e,t,n,r){this.batch=e,this.commitVersion=t,this.mutationResults=n,this.docVersions=r}static from(e,t,n){N(e.mutations.length===n.length,58842,{fe:e.mutations.length,ge:n.length});let r=n9,i=e.mutations;for(let e=0;e<i.length;e++)r=r.insert(i[e].key,n[e].version);return new rk(e,t,n,r)}}class rR{constructor(e,t){this.largestBatchId=e,this.mutation=t}getKey(){return this.mutation.key}isEqual(e){return null!==e&&this.mutation===e.mutation}toString(){return`Overlay{
      largestBatchId: ${this.largestBatchId},
      mutation: ${this.mutation.toString()}
    }`}}class rV{constructor(e,t){this.count=e,this.unchangedNames=t}}function rM(e){switch(e){case C.OK:return S(64938);case C.CANCELLED:case C.UNKNOWN:case C.DEADLINE_EXCEEDED:case C.RESOURCE_EXHAUSTED:case C.INTERNAL:case C.UNAVAILABLE:case C.UNAUTHENTICATED:return!1;case C.INVALID_ARGUMENT:case C.NOT_FOUND:case C.ALREADY_EXISTS:case C.PERMISSION_DENIED:case C.FAILED_PRECONDITION:case C.ABORTED:case C.OUT_OF_RANGE:case C.UNIMPLEMENTED:case C.DATA_LOSS:return!0;default:return S(15467,{code:e})}}function rO(e){if(void 0===e)return E("GRPC error has no .code"),C.UNKNOWN;switch(e){case r.OK:return C.OK;case r.CANCELLED:return C.CANCELLED;case r.UNKNOWN:return C.UNKNOWN;case r.DEADLINE_EXCEEDED:return C.DEADLINE_EXCEEDED;case r.RESOURCE_EXHAUSTED:return C.RESOURCE_EXHAUSTED;case r.INTERNAL:return C.INTERNAL;case r.UNAVAILABLE:return C.UNAVAILABLE;case r.UNAUTHENTICATED:return C.UNAUTHENTICATED;case r.INVALID_ARGUMENT:return C.INVALID_ARGUMENT;case r.NOT_FOUND:return C.NOT_FOUND;case r.ALREADY_EXISTS:return C.ALREADY_EXISTS;case r.PERMISSION_DENIED:return C.PERMISSION_DENIED;case r.FAILED_PRECONDITION:return C.FAILED_PRECONDITION;case r.ABORTED:return C.ABORTED;case r.OUT_OF_RANGE:return C.OUT_OF_RANGE;case r.UNIMPLEMENTED:return C.UNIMPLEMENTED;case r.DATA_LOSS:return C.DATA_LOSS;default:return S(39323,{code:e})}}(i=r||(r={}))[i.OK=0]="OK",i[i.CANCELLED=1]="CANCELLED",i[i.UNKNOWN=2]="UNKNOWN",i[i.INVALID_ARGUMENT=3]="INVALID_ARGUMENT",i[i.DEADLINE_EXCEEDED=4]="DEADLINE_EXCEEDED",i[i.NOT_FOUND=5]="NOT_FOUND",i[i.ALREADY_EXISTS=6]="ALREADY_EXISTS",i[i.PERMISSION_DENIED=7]="PERMISSION_DENIED",i[i.UNAUTHENTICATED=16]="UNAUTHENTICATED",i[i.RESOURCE_EXHAUSTED=8]="RESOURCE_EXHAUSTED",i[i.FAILED_PRECONDITION=9]="FAILED_PRECONDITION",i[i.ABORTED=10]="ABORTED",i[i.OUT_OF_RANGE=11]="OUT_OF_RANGE",i[i.UNIMPLEMENTED=12]="UNIMPLEMENTED",i[i.INTERNAL=13]="INTERNAL",i[i.UNAVAILABLE=14]="UNAVAILABLE",i[i.DATA_LOSS=15]="DATA_LOSS";let rF=new c.jz([0xffffffff,0xffffffff],0);function rL(e){let t=U().encode(e),n=new c.VV;return n.update(t),new Uint8Array(n.digest())}function rP(e){let t=new DataView(e.buffer),n=t.getUint32(0,!0),r=t.getUint32(4,!0),i=t.getUint32(8,!0),s=t.getUint32(12,!0);return[new c.jz([n,r],0),new c.jz([i,s],0)]}class rU{constructor(e,t,n){if(this.bitmap=e,this.padding=t,this.hashCount=n,t<0||t>=8)throw new rq(`Invalid padding: ${t}`);if(n<0||e.length>0&&0===this.hashCount)throw new rq(`Invalid hash count: ${n}`);if(0===e.length&&0!==t)throw new rq(`Invalid padding when bitmap length is 0: ${t}`);this.pe=8*e.length-t,this.ye=c.jz.fromNumber(this.pe)}we(e,t,n){let r=e.add(t.multiply(c.jz.fromNumber(n)));return 1===r.compare(rF)&&(r=new c.jz([r.getBits(0),r.getBits(1)],0)),r.modulo(this.ye).toNumber()}be(e){return!!(this.bitmap[Math.floor(e/8)]&1<<e%8)}mightContain(e){if(0===this.pe)return!1;let[t,n]=rP(rL(e));for(let e=0;e<this.hashCount;e++){let r=this.we(t,n,e);if(!this.be(r))return!1}return!0}static create(e,t,n){let r=new rU(new Uint8Array(Math.ceil(e/8)),e%8==0?0:8-e%8,t);return n.forEach(e=>r.insert(e)),r}insert(e){if(0===this.pe)return;let[t,n]=rP(rL(e));for(let e=0;e<this.hashCount;e++){let r=this.we(t,n,e);this.Se(r)}}Se(e){let t=Math.floor(e/8);this.bitmap[t]|=1<<e%8}}class rq extends Error{constructor(){super(...arguments),this.name="BloomFilterError"}}class rB{constructor(e,t,n,r,i){this.snapshotVersion=e,this.targetChanges=t,this.targetMismatches=n,this.documentUpdates=r,this.resolvedLimboDocuments=i}static createSynthesizedRemoteEventForCurrentChange(e,t,n){let r=new Map;return r.set(e,rz.createSynthesizedTargetChangeForCurrentChange(e,t,n)),new rB(j.min(),r,new tN(B),n5,re())}}class rz{constructor(e,t,n,r,i){this.resumeToken=e,this.current=t,this.addedDocuments=n,this.modifiedDocuments=r,this.removedDocuments=i}static createSynthesizedTargetChangeForCurrentChange(e,t,n){return new rz(n,t,re(),re(),re())}}class rK{constructor(e,t,n,r){this.De=e,this.removedTargetIds=t,this.key=n,this.ve=r}}class r${constructor(e,t){this.targetId=e,this.Ce=t}}class rG{constructor(e,t,n=tO.EMPTY_BYTE_STRING,r=null){this.state=e,this.targetIds=t,this.resumeToken=n,this.cause=r}}class rj{constructor(){this.Fe=0,this.Me=rH(),this.xe=tO.EMPTY_BYTE_STRING,this.Oe=!1,this.Ne=!0}get current(){return this.Oe}get resumeToken(){return this.xe}get Be(){return 0!==this.Fe}get Le(){return this.Ne}ke(e){e.approximateByteSize()>0&&(this.Ne=!0,this.xe=e)}qe(){let e=re(),t=re(),n=re();return this.Me.forEach((r,i)=>{switch(i){case 0:e=e.add(r);break;case 2:t=t.add(r);break;case 1:n=n.add(r);break;default:S(38017,{changeType:i})}}),new rz(this.xe,this.Oe,e,t,n)}Qe(){this.Ne=!1,this.Me=rH()}$e(e,t){this.Ne=!0,this.Me=this.Me.insert(e,t)}Ue(e){this.Ne=!0,this.Me=this.Me.remove(e)}Ke(){this.Fe+=1}We(){this.Fe-=1,N(this.Fe>=0,3241,{Fe:this.Fe})}Ge(){this.Ne=!0,this.Oe=!0}}class rQ{constructor(e){this.ze=e,this.je=new Map,this.He=n5,this.Je=rW(),this.Ye=rW(),this.Ze=new tN(B)}Xe(e){for(let t of e.De)e.ve&&e.ve.isFoundDocument()?this.et(t,e.ve):this.tt(t,e.key,e.ve);for(let t of e.removedTargetIds)this.tt(t,e.key,e.ve)}nt(e){this.forEachTarget(e,t=>{let n=this.rt(t);switch(e.state){case 0:this.it(t)&&n.ke(e.resumeToken);break;case 1:n.We(),n.Be||n.Qe(),n.ke(e.resumeToken);break;case 2:n.We(),n.Be||this.removeTarget(t);break;case 3:this.it(t)&&(n.Ge(),n.ke(e.resumeToken));break;case 4:this.it(t)&&(this.st(t),n.ke(e.resumeToken));break;default:S(56790,{state:e.state})}})}forEachTarget(e,t){e.targetIds.length>0?e.targetIds.forEach(t):this.je.forEach((e,n)=>{this.it(n)&&t(n)})}ot(e){let t=e.targetId,n=e.Ce.count,r=this._t(t);if(r){let i=r.target;if(nL(i))if(0===n){let e=new X(i.path);this.tt(t,e,nd.newNoDocument(e,j.min()))}else N(1===n,20013,{expectedCount:n});else{let r=this.ut(t);if(r!==n){let n=this.ct(e),i=n?this.lt(n,e,r):1;0!==i&&(this.st(t),this.Ze=this.Ze.insert(t,2===i?"TargetPurposeExistenceFilterMismatchBloom":"TargetPurposeExistenceFilterMismatch"))}}}}ct(e){let t,n,r=e.Ce.unchangedNames;if(!r||!r.bits)return null;let{bits:{bitmap:i="",padding:s=0},hashCount:a=0}=r;try{t=tU(i).toUint8Array()}catch(e){if(e instanceof tM)return b("Decoding the base64 bloom filter in existence filter failed ("+e.message+"); ignoring the bloom filter and falling back to full re-query."),null;throw e}try{n=new rU(t,s,a)}catch(e){return b(e instanceof rq?"BloomFilter error: ":"Applying bloom filter failed: ",e),null}return 0===n.pe?null:n}lt(e,t,n){return 2*(t.Ce.count!==n-this.Tt(e,t.targetId))}Tt(e,t){let n=this.ze.getRemoteKeysForTarget(t),r=0;return n.forEach(n=>{let i=this.ze.Pt(),s=`projects/${i.projectId}/databases/${i.database}/documents/${n.path.canonicalString()}`;e.mightContain(s)||(this.tt(t,n,null),r++)}),r}It(e){let t=new Map;this.je.forEach((n,r)=>{let i=this._t(r);if(i){if(n.current&&nL(i.target)){let t=new X(i.target.path);this.Et(t).has(r)||this.dt(r,t)||this.tt(r,t,nd.newNoDocument(t,e))}n.Le&&(t.set(r,n.qe()),n.Qe())}});let n=re();this.Ye.forEach((e,t)=>{let r=!0;t.forEachWhile(e=>{let t=this._t(e);return!t||"TargetPurposeLimboResolution"===t.purpose||(r=!1,!1)}),r&&(n=n.add(e))}),this.He.forEach((t,n)=>n.setReadTime(e));let r=new rB(e,t,this.Ze,this.He,n);return this.He=n5,this.Je=rW(),this.Ye=rW(),this.Ze=new tN(B),r}et(e,t){if(!this.it(e))return;let n=2*!!this.dt(e,t.key);this.rt(e).$e(t.key,n),this.He=this.He.insert(t.key,t),this.Je=this.Je.insert(t.key,this.Et(t.key).add(e)),this.Ye=this.Ye.insert(t.key,this.At(t.key).add(e))}tt(e,t,n){if(!this.it(e))return;let r=this.rt(e);this.dt(e,t)?r.$e(t,1):r.Ue(t),this.Ye=this.Ye.insert(t,this.At(t).delete(e)),this.Ye=this.Ye.insert(t,this.At(t).add(e)),n&&(this.He=this.He.insert(t,n))}removeTarget(e){this.je.delete(e)}ut(e){let t=this.rt(e).qe();return this.ze.getRemoteKeysForTarget(e).size+t.addedDocuments.size-t.removedDocuments.size}Ke(e){this.rt(e).Ke()}rt(e){let t=this.je.get(e);return t||(t=new rj,this.je.set(e,t)),t}At(e){let t=this.Ye.get(e);return t||(t=new tA(B),this.Ye=this.Ye.insert(e,t)),t}Et(e){let t=this.Je.get(e);return t||(t=new tA(B),this.Je=this.Je.insert(e,t)),t}it(e){let t=null!==this._t(e);return t||T("WatchChangeAggregator","Detected inactive target",e),t}_t(e){let t=this.je.get(e);return t&&t.Be?null:this.ze.Rt(e)}st(e){this.je.set(e,new rj),this.ze.getRemoteKeysForTarget(e).forEach(t=>{this.tt(e,t,null)})}dt(e,t){return this.ze.getRemoteKeysForTarget(e).has(t)}}function rW(){return new tN(X.comparator)}function rH(){return new tN(X.comparator)}let rY={asc:"ASCENDING",desc:"DESCENDING"},rJ={"<":"LESS_THAN","<=":"LESS_THAN_OR_EQUAL",">":"GREATER_THAN",">=":"GREATER_THAN_OR_EQUAL","==":"EQUAL","!=":"NOT_EQUAL","array-contains":"ARRAY_CONTAINS",in:"IN","not-in":"NOT_IN","array-contains-any":"ARRAY_CONTAINS_ANY"},rX={and:"AND",or:"OR"};class rZ{constructor(e,t){this.databaseId=e,this.useProto3Json=t}}function r0(e,t){return e.useProto3Json||eN(t)?t:{value:t}}function r1(e,t){return e.useProto3Json?`${new Date(1e3*t.seconds).toISOString().replace(/\.\d*/,"").replace("Z","")}.${("000000000"+t.nanoseconds).slice(-9)}Z`:{seconds:""+t.seconds,nanos:t.nanoseconds}}function r2(e,t){return e.useProto3Json?t.toBase64():t.toUint8Array()}function r5(e){return N(!!e,49232),j.fromTimestamp(function(e){let t=tL(e);return new G(t.seconds,t.nanos)}(e))}function r4(e,t){return r3(e,t).canonicalString()}function r3(e,t){let n=new H(["projects",e.projectId,"databases",e.database]).child("documents");return void 0===t?n:n.child(t)}function r6(e){let t=H.fromString(e);return N(im(t),10190,{key:t.toString()}),t}function r8(e,t){return r4(e.databaseId,t.path)}function r9(e,t){let n=r6(t);if(n.get(1)!==e.databaseId.projectId)throw new D(C.INVALID_ARGUMENT,"Tried to deserialize key from different project: "+n.get(1)+" vs "+e.databaseId.projectId);if(n.get(3)!==e.databaseId.database)throw new D(C.INVALID_ARGUMENT,"Tried to deserialize key from different database: "+n.get(3)+" vs "+e.databaseId.database);return new X(ir(n))}function r7(e,t){return r4(e.databaseId,t)}function ie(e){let t=r6(e);return 4===t.length?H.emptyPath():ir(t)}function it(e){return new H(["projects",e.databaseId.projectId,"databases",e.databaseId.database]).canonicalString()}function ir(e){return N(e.length>4&&"documents"===e.get(4),29091,{key:e.toString()}),e.popFirst(5)}function ii(e,t,n){return{name:r8(e,t),fields:n.value.mapValue.fields}}function is(e,t,n){let r=r9(e,t.name),i=r5(t.updateTime),s=t.createTime?r5(t.createTime):j.min(),a=new nc({mapValue:{fields:t.fields}}),o=nd.newFoundDocument(r,i,s,a);return n&&o.setHasCommittedMutations(),n?o.setHasCommittedMutations():o}function ia(e,t){var n;let r;if(t instanceof rb)r={update:ii(e,t.key,t.value)};else if(t instanceof rC)r={delete:r8(e,t.key)};else if(t instanceof r_)r={update:ii(e,t.key,t.data),updateMask:function(e){let t=[];return e.fields.forEach(e=>t.push(e.canonicalString())),{fieldPaths:t}}(t.fieldMask)};else{if(!(t instanceof rD))return S(16599,{ft:t.type});r={verify:r8(e,t.key)}}return t.fieldTransforms.length>0&&(r.updateTransforms=t.fieldTransforms.map(e=>(function(e,t){let n=t.transform;if(n instanceof ro)return{fieldPath:t.field.canonicalString(),setToServerValue:"REQUEST_TIME"};if(n instanceof rl)return{fieldPath:t.field.canonicalString(),appendMissingElements:{values:n.elements}};if(n instanceof rh)return{fieldPath:t.field.canonicalString(),removeAllFromArray:{values:n.elements}};if(n instanceof rd)return{fieldPath:t.field.canonicalString(),increment:n.Re};throw S(20930,{transform:t.transform})})(0,e))),t.precondition.isNone||(r.currentDocument=void 0!==(n=t.precondition).updateTime?{updateTime:r1(e,n.updateTime.toTimestamp())}:void 0!==n.exists?{exists:n.exists}:S(27497)),r}function io(e,t){var n;let r=t.currentDocument?void 0!==(n=t.currentDocument).updateTime?ry.updateTime(r5(n.updateTime)):void 0!==n.exists?ry.exists(n.exists):ry.none():ry.none(),i=t.updateTransforms?t.updateTransforms.map(t=>{let n;return n=null,"setToServerValue"in t?(N("REQUEST_TIME"===t.setToServerValue,16630,{proto:t}),n=new ro):"appendMissingElements"in t?n=new rl(t.appendMissingElements.values||[]):"removeAllFromArray"in t?n=new rh(t.removeAllFromArray.values||[]):"increment"in t?n=new rd(e,t.increment):S(16584,{proto:t}),new rg(J.fromServerFormat(t.fieldPath),n)}):[];if(t.update){t.update.name;let n=r9(e,t.update.name),s=new nc({mapValue:{fields:t.update.fields}});return t.updateMask?new r_(n,s,new tV((t.updateMask.fieldPaths||[]).map(e=>J.fromServerFormat(e))),r,i):new rb(n,s,r,i)}return t.delete?new rC(r9(e,t.delete),r):t.verify?new rD(r9(e,t.verify),r):S(1463,{proto:t})}function il(e,t){return{documents:[r7(e,t.path)]}}function iu(e,t){var n,r;let i,s={structuredQuery:{}},a=t.path;null!==t.collectionGroup?(i=a,s.structuredQuery.from=[{collectionId:t.collectionGroup,allDescendants:!0}]):(i=a.popLast(),s.structuredQuery.from=[{collectionId:a.lastSegment()}]),s.parent=r7(e,i);let o=function(e){if(0!==e.length)return function e(t){return t instanceof nw?function(e){if("=="===e.op){if(nr(e.value))return{unaryFilter:{field:ic(e.field),op:"IS_NAN"}};if(nn(e.value))return{unaryFilter:{field:ic(e.field),op:"IS_NULL"}}}else if("!="===e.op){if(nr(e.value))return{unaryFilter:{field:ic(e.field),op:"IS_NOT_NAN"}};if(nn(e.value))return{unaryFilter:{field:ic(e.field),op:"IS_NOT_NULL"}}}return{fieldFilter:{field:ic(e.field),op:rJ[e.op],value:e.value}}}(t):t instanceof nv?function(t){let n=t.getFilters().map(t=>e(t));return 1===n.length?n[0]:{compositeFilter:{op:rX[t.op],filters:n}}}(t):S(54877,{filter:t})}(nv.create(e,"and"))}(t.filters);o&&(s.structuredQuery.where=o);let l=function(e){if(0!==e.length)return e.map(e=>({field:ic(e.field),direction:rY[e.dir]}))}(t.orderBy);l&&(s.structuredQuery.orderBy=l);let u=r0(e,t.limit);return null!==u&&(s.structuredQuery.limit=u),t.startAt&&(s.structuredQuery.startAt={before:(n=t.startAt).inclusive,values:n.position}),t.endAt&&(s.structuredQuery.endAt={before:!(r=t.endAt).inclusive,values:r.position}),{gt:s,parent:i}}function ih(e){var t;let n,r=ie(e.parent),i=e.structuredQuery,s=i.from?i.from.length:0,a=null;if(s>0){N(1===s,65062);let e=i.from[0];e.allDescendants?a=e.collectionId:r=r.child(e.collectionId)}let o=[];i.where&&(o=function(e){let t=function e(t){return void 0!==t.unaryFilter?function(e){switch(e.unaryFilter.op){case"IS_NAN":let t=id(e.unaryFilter.field);return nw.create(t,"==",{doubleValue:NaN});case"IS_NULL":let n=id(e.unaryFilter.field);return nw.create(n,"==",{nullValue:"NULL_VALUE"});case"IS_NOT_NAN":let r=id(e.unaryFilter.field);return nw.create(r,"!=",{doubleValue:NaN});case"IS_NOT_NULL":let i=id(e.unaryFilter.field);return nw.create(i,"!=",{nullValue:"NULL_VALUE"});case"OPERATOR_UNSPECIFIED":return S(61313);default:return S(60726)}}(t):void 0!==t.fieldFilter?nw.create(id(t.fieldFilter.field),function(e){switch(e){case"EQUAL":return"==";case"NOT_EQUAL":return"!=";case"GREATER_THAN":return">";case"GREATER_THAN_OR_EQUAL":return">=";case"LESS_THAN":return"<";case"LESS_THAN_OR_EQUAL":return"<=";case"ARRAY_CONTAINS":return"array-contains";case"IN":return"in";case"NOT_IN":return"not-in";case"ARRAY_CONTAINS_ANY":return"array-contains-any";case"OPERATOR_UNSPECIFIED":return S(58110);default:return S(50506)}}(t.fieldFilter.op),t.fieldFilter.value):void 0!==t.compositeFilter?nv.create(t.compositeFilter.filters.map(t=>e(t)),function(e){switch(e){case"AND":return"and";case"OR":return"or";default:return S(1026)}}(t.compositeFilter.op)):S(30097,{filter:t})}(e);return t instanceof nv&&nE(t)?t.getFilters():[t]}(i.where));let l=[];i.orderBy&&(l=i.orderBy.map(e=>new np(id(e.field),function(e){switch(e){case"ASCENDING":return"asc";case"DESCENDING":return"desc";default:return}}(e.direction))));let u=null;i.limit&&(u=eN(n="object"==typeof(t=i.limit)?t.value:t)?null:n);let h=null;i.startAt&&(h=function(e){let t=!!e.before;return new nf(e.values||[],t)}(i.startAt));let c=null;return i.endAt&&(c=function(e){let t=!e.before;return new nf(e.values||[],t)}(i.endAt)),new nB(r,a,l,o,u,"F",h,c)}function ic(e){return{fieldPath:e.canonicalString()}}function id(e){return J.fromServerFormat(e.fieldPath)}function im(e){return e.length>=4&&"projects"===e.get(0)&&"databases"===e.get(2)}class ig{constructor(e,t,n,r,i=j.min(),s=j.min(),a=tO.EMPTY_BYTE_STRING,o=null){this.target=e,this.targetId=t,this.purpose=n,this.sequenceNumber=r,this.snapshotVersion=i,this.lastLimboFreeSnapshotVersion=s,this.resumeToken=a,this.expectedCount=o}withSequenceNumber(e){return new ig(this.target,this.targetId,this.purpose,e,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,this.expectedCount)}withResumeToken(e,t){return new ig(this.target,this.targetId,this.purpose,this.sequenceNumber,t,this.lastLimboFreeSnapshotVersion,e,null)}withExpectedCount(e){return new ig(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,e)}withLastLimboFreeSnapshotVersion(e){return new ig(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,e,this.resumeToken,this.expectedCount)}}class ip{constructor(e){this.wt=e}}function iy(e,t){let n=t.key,r={prefixPath:n.getCollectionPath().popLast().toArray(),collectionGroup:n.collectionGroup,documentId:n.path.lastSegment(),readTime:iw(t.readTime),hasCommittedMutations:t.hasCommittedMutations};if(t.isFoundDocument()){var i;r.document={name:r8(i=e.wt,t.key),fields:t.data.value.mapValue.fields,updateTime:r1(i,t.version.toTimestamp()),createTime:r1(i,t.createTime.toTimestamp())}}else if(t.isNoDocument())r.noDocument={path:n.path.toArray(),readTime:iv(t.version)};else{if(!t.isUnknownDocument())return S(57904,{document:t});r.unknownDocument={path:n.path.toArray(),version:iv(t.version)}}return r}function iw(e){let t=e.toTimestamp();return[t.seconds,t.nanoseconds]}function iv(e){let t=e.toTimestamp();return{seconds:t.seconds,nanoseconds:t.nanoseconds}}function iI(e){let t=new G(e.seconds,e.nanoseconds);return j.fromTimestamp(t)}function iT(e,t){let n=(t.baseMutations||[]).map(t=>io(e.wt,t));for(let e=0;e<t.mutations.length-1;++e){let n=t.mutations[e];e+1<t.mutations.length&&void 0!==t.mutations[e+1].transform&&(n.updateTransforms=t.mutations[e+1].transform.fieldTransforms,t.mutations.splice(e+1,1),++e)}let r=t.mutations.map(t=>io(e.wt,t)),i=G.fromMillis(t.localWriteTimeMs);return new rA(t.batchId,i,n,r)}function iE(e){let t=iI(e.readTime),n=void 0!==e.lastLimboFreeSnapshotVersion?iI(e.lastLimboFreeSnapshotVersion):j.min();return new ig(void 0!==e.query.documents?function(e){let t=e.documents.length;return N(1===t,1966,{count:t}),nj(nz(ie(e.documents[0])))}(e.query):nj(ih(e.query)),e.targetId,"TargetPurposeListen",e.lastListenSequenceNumber,t,n,tO.fromBase64String(e.resumeToken))}function ib(e,t){let n,r=iv(t.snapshotVersion),i=iv(t.lastLimboFreeSnapshotVersion);n=nL(t.target)?il(e.wt,t.target):iu(e.wt,t.target).gt;let s=t.resumeToken.toBase64();return{targetId:t.targetId,canonicalId:nO(t.target),readTime:r,resumeToken:s,lastListenSequenceNumber:t.sequenceNumber,lastLimboFreeSnapshotVersion:i,query:n}}function i_(e){let t=ih({parent:e.parent,structuredQuery:e.structuredQuery});return"LAST"===e.limitType?nH(t,t.limit,"L"):t}function iS(e,t){return new rR(t.largestBatchId,io(e.wt,t.overlayMutation))}function ix(e,t){let n=t.path.lastSegment();return[e,eA(t.path.popLast()),n]}function iN(e,t,n,r){return{indexId:e,uid:t,sequenceNumber:n,readTime:iv(r.readTime),documentKey:eA(r.documentKey.path),largestBatchId:r.largestBatchId}}class iC{getBundleMetadata(e,t){return tb(e,e9).get(t).next(e=>{if(e)return{id:e.bundleId,createTime:iI(e.createTime),version:e.version}})}saveBundleMetadata(e,t){return tb(e,e9).put({bundleId:t.id,createTime:iv(r5(t.createTime)),version:t.version})}getNamedQuery(e,t){return tb(e,e7).get(t).next(e=>{if(e)return{name:e.name,query:i_(e.bundledQuery),readTime:iI(e.readTime)}})}saveNamedQuery(e,t){return tb(e,e7).put({name:t.name,readTime:iv(r5(t.readTime)),bundledQuery:t.bundledQuery})}}class iD{constructor(e,t){this.serializer=e,this.userId=t}static bt(e,t){return new iD(e,t.uid||"")}getOverlay(e,t){return tb(e,th).get(ix(this.userId,t)).next(e=>e?iS(this.serializer,e):null)}getOverlays(e,t){let n=n8();return ec.forEach(t,t=>this.getOverlay(e,t).next(e=>{null!==e&&n.set(t,e)})).next(()=>n)}saveOverlays(e,t,n){let r=[];return n.forEach((n,i)=>{let s=new rR(t,i);r.push(this.St(e,s))}),ec.waitFor(r)}removeOverlaysForBatchId(e,t,n){let r=new Set;t.forEach(e=>r.add(eA(e.getCollectionPath())));let i=[];return r.forEach(t=>{let r=IDBKeyRange.bound([this.userId,t,n],[this.userId,t,n+1],!1,!0);i.push(tb(e,th).X(td,r))}),ec.waitFor(i)}getOverlaysForCollection(e,t,n){let r=n8(),i=eA(t),s=IDBKeyRange.bound([this.userId,i,n],[this.userId,i,Number.POSITIVE_INFINITY],!0);return tb(e,th).J(td,s).next(e=>{for(let t of e){let e=iS(this.serializer,t);r.set(e.getKey(),e)}return r})}getOverlaysForCollectionGroup(e,t,n,r){let i,s=n8(),a=IDBKeyRange.bound([this.userId,t,n],[this.userId,t,Number.POSITIVE_INFINITY],!0);return tb(e,th).te({index:tm,range:a},(e,t,n)=>{let a=iS(this.serializer,t);s.size()<r||a.largestBatchId===i?(s.set(a.getKey(),a),i=a.largestBatchId):n.done()}).next(()=>s)}St(e,t){return tb(e,th).put(function(e,t,n){let[r,i,s]=ix(t,n.mutation.key);return{userId:t,collectionPath:i,documentId:s,collectionGroup:n.mutation.key.getCollectionGroup(),largestBatchId:n.largestBatchId,overlayMutation:ia(e.wt,n.mutation)}}(this.serializer,this.userId,t))}}class iA{Dt(e){return tb(e,tp)}getSessionToken(e){return this.Dt(e).get("sessionToken").next(e=>{let t=null==e?void 0:e.value;return t?tO.fromUint8Array(t):tO.EMPTY_BYTE_STRING})}setSessionToken(e,t){return this.Dt(e).put({name:"sessionToken",value:t.toUint8Array()})}}class ik{constructor(){}vt(e,t){this.Ct(e,t),t.Ft()}Ct(e,t){if("nullValue"in e)this.Mt(t,5);else if("booleanValue"in e)this.Mt(t,10),t.xt(+!!e.booleanValue);else if("integerValue"in e)this.Mt(t,15),t.xt(tP(e.integerValue));else if("doubleValue"in e){let n=tP(e.doubleValue);isNaN(n)?this.Mt(t,13):(this.Mt(t,15),eC(n)?t.xt(0):t.xt(n))}else if("timestampValue"in e){let n=e.timestampValue;this.Mt(t,20),"string"==typeof n&&(n=tL(n)),t.Ot(`${n.seconds||""}`),t.xt(n.nanos||0)}else if("stringValue"in e)this.Nt(e.stringValue,t),this.Bt(t);else if("bytesValue"in e)this.Mt(t,30),t.Lt(tU(e.bytesValue)),this.Bt(t);else if("referenceValue"in e)this.kt(e.referenceValue,t);else if("geoPointValue"in e){let n=e.geoPointValue;this.Mt(t,45),t.xt(n.latitude||0),t.xt(n.longitude||0)}else"mapValue"in e?no(e)?this.Mt(t,Number.MAX_SAFE_INTEGER):ns(e)?this.qt(e.mapValue,t):(this.Qt(e.mapValue,t),this.Bt(t)):"arrayValue"in e?(this.$t(e.arrayValue,t),this.Bt(t)):S(19022,{Ut:e})}Nt(e,t){this.Mt(t,25),this.Kt(e,t)}Kt(e,t){t.Ot(e)}Qt(e,t){let n=e.fields||{};for(let e of(this.Mt(t,55),Object.keys(n)))this.Nt(e,t),this.Ct(n[e],t)}qt(e,t){var n,r;let i=e.fields||{};this.Mt(t,53);let s=(null==(r=null==(n=i[t0].arrayValue)?void 0:n.values)?void 0:r.length)||0;this.Mt(t,15),t.xt(tP(s)),this.Nt(t0,t),this.Ct(i[t0],t)}$t(e,t){let n=e.values||[];for(let e of(this.Mt(t,50),n))this.Ct(e,t)}kt(e,t){this.Mt(t,37),X.fromName(e).path.forEach(e=>{this.Mt(t,60),this.Kt(e,t)})}Mt(e,t){e.xt(t)}Bt(e){e.xt(2)}}function iR(e){return Math.ceil((64-function(e){let t=0;for(let n=0;n<8;++n){let r=function(e){if(0===e)return 8;let t=0;return e>>4||(t+=4,e<<=4),e>>6||(t+=2,e<<=2),e>>7||(t+=1),t}(255&e[n]);if(t+=r,8!==r)break}return t}(e))/8)}ik.Wt=new ik;class iV{constructor(){this.buffer=new Uint8Array(1024),this.position=0}Gt(e){let t=e[Symbol.iterator](),n=t.next();for(;!n.done;)this.zt(n.value),n=t.next();this.jt()}Ht(e){let t=e[Symbol.iterator](),n=t.next();for(;!n.done;)this.Jt(n.value),n=t.next();this.Yt()}Zt(e){for(let t of e){let e=t.charCodeAt(0);if(e<128)this.zt(e);else if(e<2048)this.zt(960|e>>>6),this.zt(128|63&e);else if(t<"\ud800"||"\udbff"<t)this.zt(480|e>>>12),this.zt(128|63&e>>>6),this.zt(128|63&e);else{let e=t.codePointAt(0);this.zt(240|e>>>18),this.zt(128|63&e>>>12),this.zt(128|63&e>>>6),this.zt(128|63&e)}}this.jt()}Xt(e){for(let t of e){let e=t.charCodeAt(0);if(e<128)this.Jt(e);else if(e<2048)this.Jt(960|e>>>6),this.Jt(128|63&e);else if(t<"\ud800"||"\udbff"<t)this.Jt(480|e>>>12),this.Jt(128|63&e>>>6),this.Jt(128|63&e);else{let e=t.codePointAt(0);this.Jt(240|e>>>18),this.Jt(128|63&e>>>12),this.Jt(128|63&e>>>6),this.Jt(128|63&e)}}this.Yt()}en(e){let t=this.tn(e),n=iR(t);this.nn(1+n),this.buffer[this.position++]=255&n;for(let e=t.length-n;e<t.length;++e)this.buffer[this.position++]=255&t[e]}rn(e){let t=this.tn(e),n=iR(t);this.nn(1+n),this.buffer[this.position++]=~(255&n);for(let e=t.length-n;e<t.length;++e)this.buffer[this.position++]=~(255&t[e])}sn(){this._n(255),this._n(255)}an(){this.un(255),this.un(255)}reset(){this.position=0}seed(e){this.nn(e.length),this.buffer.set(e,this.position),this.position+=e.length}cn(){return this.buffer.slice(0,this.position)}tn(e){let t=function(e){let t=new DataView(new ArrayBuffer(8));return t.setFloat64(0,e,!1),new Uint8Array(t.buffer)}(e),n=!!(128&t[0]);t[0]^=n?255:128;for(let e=1;e<t.length;++e)t[e]^=255*!!n;return t}zt(e){let t=255&e;0===t?(this._n(0),this._n(255)):255===t?(this._n(255),this._n(0)):this._n(t)}Jt(e){let t=255&e;0===t?(this.un(0),this.un(255)):255===t?(this.un(255),this.un(0)):this.un(e)}jt(){this._n(0),this._n(1)}Yt(){this.un(0),this.un(1)}_n(e){this.nn(1),this.buffer[this.position++]=e}un(e){this.nn(1),this.buffer[this.position++]=~e}nn(e){let t=e+this.position;if(t<=this.buffer.length)return;let n=2*this.buffer.length;n<t&&(n=t);let r=new Uint8Array(n);r.set(this.buffer),this.buffer=r}}class iM{constructor(e){this.ln=e}Lt(e){this.ln.Gt(e)}Ot(e){this.ln.Zt(e)}xt(e){this.ln.en(e)}Ft(){this.ln.sn()}}class iO{constructor(e){this.ln=e}Lt(e){this.ln.Ht(e)}Ot(e){this.ln.Xt(e)}xt(e){this.ln.rn(e)}Ft(){this.ln.an()}}class iF{constructor(){this.ln=new iV,this.hn=new iM(this.ln),this.Pn=new iO(this.ln)}seed(e){this.ln.seed(e)}Tn(e){return 0===e?this.hn:this.Pn}cn(){return this.ln.cn()}reset(){this.ln.reset()}}class iL{constructor(e,t,n,r){this.indexId=e,this.documentKey=t,this.arrayValue=n,this.directionalValue=r}In(){let e=this.directionalValue.length,t=0===e||255===this.directionalValue[e-1]?e+1:e,n=new Uint8Array(t);return n.set(this.directionalValue,0),t!==e?n.set([0],this.directionalValue.length):++n[n.length-1],new iL(this.indexId,this.documentKey,this.arrayValue,n)}}function iP(e,t){let n=e.indexId-t.indexId;return 0!==n||0!==(n=iU(e.arrayValue,t.arrayValue))||0!==(n=iU(e.directionalValue,t.directionalValue))?n:X.comparator(e.documentKey,t.documentKey)}function iU(e,t){for(let n=0;n<e.length&&n<t.length;++n){let r=e[n]-t[n];if(0!==r)return r}return e.length-t.length}class iq{constructor(e){for(let t of(this.En=new tA((e,t)=>J.comparator(e.field,t.field)),this.collectionId=null!=e.collectionGroup?e.collectionGroup:e.path.lastSegment(),this.dn=e.orderBy,this.An=[],e.filters))t.isInequality()?this.En=this.En.add(t):this.An.push(t)}get Rn(){return this.En.size>1}Vn(e){if(N(e.collectionGroup===this.collectionId,49279),this.Rn)return!1;let t=ee(e);if(void 0!==t&&!this.mn(t))return!1;let n=et(e),r=new Set,i=0,s=0;for(;i<n.length&&this.mn(n[i]);++i)r=r.add(n[i].fieldPath.canonicalString());if(i===n.length)return!0;if(this.En.size>0){let e=this.En.getIterator().getNext();if(!r.has(e.field.canonicalString())){let t=n[i];if(!this.fn(e,t)||!this.gn(this.dn[s++],t))return!1}++i}for(;i<n.length;++i){let e=n[i];if(s>=this.dn.length||!this.gn(this.dn[s++],e))return!1}return!0}pn(){if(this.Rn)return null;let e=new tA(J.comparator),t=[];for(let n of this.An)if(!n.field.isKeyField())if("array-contains"===n.op||"array-contains-any"===n.op)t.push(new en(n.field,2));else{if(e.has(n.field))continue;e=e.add(n.field),t.push(new en(n.field,0))}for(let n of this.dn)n.field.isKeyField()||e.has(n.field)||(e=e.add(n.field),t.push(new en(n.field,+("asc"!==n.dir))));return new Z(Z.UNKNOWN_ID,this.collectionId,t,er.empty())}mn(e){for(let t of this.An)if(this.fn(t,e))return!0;return!1}fn(e,t){if(void 0===e||!e.field.isEqual(t.fieldPath))return!1;let n="array-contains"===e.op||"array-contains-any"===e.op;return 2===t.kind===n}gn(e,t){return!!e.field.isEqual(t.fieldPath)&&(0===t.kind&&"asc"===e.dir||1===t.kind&&"desc"===e.dir)}}function iB(e){return e instanceof nw}function iz(e){return e instanceof nv&&nE(e)}function iK(e){return iB(e)||iz(e)||function(e){if(e instanceof nv&&nT(e)){for(let t of e.getFilters())if(!iB(t)&&!iz(t))return!1;return!0}return!1}(e)}function i$(e,t){return N(e instanceof nw||e instanceof nv,38388),N(t instanceof nw||t instanceof nv,25473),ij(e instanceof nw?t instanceof nw?nv.create([e,t],"and"):iG(e,t):t instanceof nw?iG(t,e):function(e,t){if(N(e.filters.length>0&&t.filters.length>0,48005),nI(e)&&nI(t))return n_(e,t.getFilters());let n=nT(e)?e:t,r=nT(e)?t:e,i=n.filters.map(e=>i$(e,r));return nv.create(i,"or")}(e,t))}function iG(e,t){if(nI(t))return n_(t,e.getFilters());{let n=t.filters.map(t=>i$(e,t));return nv.create(n,"or")}}function ij(e){if(N(e instanceof nw||e instanceof nv,11850),e instanceof nw)return e;let t=e.getFilters();if(1===t.length)return ij(t[0]);if(nb(e))return e;let n=t.map(e=>ij(e)),r=[];return n.forEach(t=>{t instanceof nw?r.push(t):t instanceof nv&&(t.op===e.op?r.push(...t.filters):r.push(t))}),1===r.length?r[0]:nv.create(r,e.op)}class iQ{constructor(){this.yn=new iW}addToCollectionParentIndex(e,t){return this.yn.add(t),ec.resolve()}getCollectionParents(e,t){return ec.resolve(this.yn.getEntries(t))}addFieldIndex(e,t){return ec.resolve()}deleteFieldIndex(e,t){return ec.resolve()}deleteAllFieldIndexes(e){return ec.resolve()}createTargetIndexes(e,t){return ec.resolve()}getDocumentsMatchingTarget(e,t){return ec.resolve(null)}getIndexType(e,t){return ec.resolve(0)}getFieldIndexes(e,t){return ec.resolve([])}getNextCollectionGroupToUpdate(e){return ec.resolve(null)}getMinOffset(e,t){return ec.resolve(ea.min())}getMinOffsetFromCollectionGroup(e,t){return ec.resolve(ea.min())}updateCollectionGroup(e,t,n){return ec.resolve()}updateIndexEntries(e,t){return ec.resolve()}}class iW{constructor(){this.index={}}add(e){let t=e.lastSegment(),n=e.popLast(),r=this.index[t]||new tA(H.comparator),i=!r.has(n);return this.index[t]=r.add(n),i}has(e){let t=e.lastSegment(),n=e.popLast(),r=this.index[t];return r&&r.has(n)}getEntries(e){return(this.index[e]||new tA(H.comparator)).toArray()}}let iH="IndexedDbIndexManager",iY=new Uint8Array(0);class iJ{constructor(e,t){this.databaseId=t,this.wn=new iW,this.bn=new n2(e=>nO(e),(e,t)=>nF(e,t)),this.uid=e.uid||""}addToCollectionParentIndex(e,t){if(!this.wn.has(t)){let n=t.lastSegment(),r=t.popLast();e.addOnCommittedListener(()=>{this.wn.add(t)});let i={collectionId:n,parent:eA(r)};return tb(e,e3).put(i)}return ec.resolve()}getCollectionParents(e,t){let n=[],r=IDBKeyRange.bound([t,""],[t+"\0",""],!1,!0);return tb(e,e3).J(r).next(e=>{for(let r of e){if(r.collectionId!==t)break;n.push(ek(r.parent))}return n})}addFieldIndex(e,t){let n=tb(e,te),r={indexId:t.indexId,collectionGroup:t.collectionGroup,fields:t.fields.map(e=>[e.fieldPath.canonicalString(),e.kind])};delete r.indexId;let i=n.add(r);if(t.indexState){let n=tb(e,tn);return i.next(e=>{n.put(iN(e,this.uid,t.indexState.sequenceNumber,t.indexState.offset))})}return i.next()}deleteFieldIndex(e,t){let n=tb(e,te),r=tb(e,tn),i=tb(e,ta);return n.delete(t.indexId).next(()=>r.delete(IDBKeyRange.bound([t.indexId],[t.indexId+1],!1,!0))).next(()=>i.delete(IDBKeyRange.bound([t.indexId],[t.indexId+1],!1,!0)))}deleteAllFieldIndexes(e){let t=tb(e,te),n=tb(e,ta),r=tb(e,tn);return t.X().next(()=>n.X()).next(()=>r.X())}createTargetIndexes(e,t){return ec.forEach(this.Sn(t),t=>this.getIndexType(e,t).next(n=>{if(0===n||1===n){let n=new iq(t).pn();if(null!=n)return this.addFieldIndex(e,n)}}))}getDocumentsMatchingTarget(e,t){let n=tb(e,ta),r=!0,i=new Map;return ec.forEach(this.Sn(t),t=>this.Dn(e,t).next(e=>{r&&(r=!!e),i.set(t,e)})).next(()=>{if(r){let e=re(),r=[];return ec.forEach(i,(i,s)=>{T(iH,`Using index id=${i.indexId}|cg=${i.collectionGroup}|f=${i.fields.map(e=>`${e.fieldPath}:${e.kind}`).join(",")} to execute ${nO(t)}`);let a=function(e,t){let n=ee(t);if(void 0===n)return null;for(let t of nP(e,n.fieldPath))switch(t.op){case"array-contains-any":return t.value.arrayValue.values||[];case"array-contains":return[t.value]}return null}(s,i),o=function(e,t){let n=new Map;for(let r of et(t))for(let t of nP(e,r.fieldPath))switch(t.op){case"==":case"in":n.set(r.fieldPath.canonicalString(),t.value);break;case"not-in":case"!=":return n.set(r.fieldPath.canonicalString(),t.value),Array.from(n.values())}return null}(s,i),l=function(e,t){let n=[],r=!0;for(let i of et(t)){let t=0===i.kind?nU(e,i.fieldPath,e.startAt):nq(e,i.fieldPath,e.startAt);n.push(t.value),r&&(r=t.inclusive)}return new nf(n,r)}(s,i),u=function(e,t){let n=[],r=!0;for(let i of et(t)){let t=0===i.kind?nq(e,i.fieldPath,e.endAt):nU(e,i.fieldPath,e.endAt);n.push(t.value),r&&(r=t.inclusive)}return new nf(n,r)}(s,i),h=this.vn(i,s,l),c=this.vn(i,s,u),d=this.Cn(i,s,o),f=this.Fn(i.indexId,a,h,l.inclusive,c,u.inclusive,d);return ec.forEach(f,i=>n.Z(i,t.limit).next(t=>{t.forEach(t=>{let n=X.fromSegments(t.documentKey);e.has(n)||(e=e.add(n),r.push(n))})}))}).next(()=>r)}return ec.resolve(null)})}Sn(e){let t=this.bn.get(e);return t||(t=0===e.filters.length?[e]:(function(e){if(0===e.getFilters().length)return[];let t=function e(t){if(N(t instanceof nw||t instanceof nv,34018),t instanceof nw)return t;if(1===t.filters.length)return e(t.filters[0]);let n=t.filters.map(t=>e(t)),r=nv.create(n,t.op);return iK(r=ij(r))?r:(N(r instanceof nv,64498),N(nI(r),40251),N(r.filters.length>1,57927),r.filters.reduce((e,t)=>i$(e,t)))}(function e(t){var n,r;if(N(t instanceof nw||t instanceof nv,20012),t instanceof nw){if(t instanceof nA){let e=(null==(r=null==(n=t.value.arrayValue)?void 0:n.values)?void 0:r.map(e=>nw.create(t.field,"==",e)))||[];return nv.create(e,"or")}return t}let i=t.filters.map(t=>e(t));return nv.create(i,t.op)}(e));return N(iK(t),7391),iB(t)||iz(t)?[t]:t.getFilters()})(nv.create(e.filters,"and")).map(t=>nM(e.path,e.collectionGroup,e.orderBy,t.getFilters(),e.limit,e.startAt,e.endAt)),this.bn.set(e,t)),t}Fn(e,t,n,r,i,s,a){let o=(null!=t?t.length:1)*Math.max(n.length,i.length),l=o/(null!=t?t.length:1),u=[];for(let h=0;h<o;++h){let o=t?this.Mn(t[h/l]):iY,c=this.xn(e,o,n[h%l],r),d=this.On(e,o,i[h%l],s),f=a.map(t=>this.xn(e,o,t,!0));u.push(...this.createRange(c,d,f))}return u}xn(e,t,n,r){let i=new iL(e,X.empty(),t,n);return r?i:i.In()}On(e,t,n,r){let i=new iL(e,X.empty(),t,n);return r?i.In():i}Dn(e,t){let n=new iq(t),r=null!=t.collectionGroup?t.collectionGroup:t.path.lastSegment();return this.getFieldIndexes(e,r).next(e=>{let t=null;for(let r of e)n.Vn(r)&&(!t||r.fields.length>t.fields.length)&&(t=r);return t})}getIndexType(e,t){let n=2,r=this.Sn(t);return ec.forEach(r,t=>this.Dn(e,t).next(e=>{e?0!==n&&e.fields.length<function(e){let t=new tA(J.comparator),n=!1;for(let r of e.filters)for(let e of r.getFlattenedFilters())e.field.isKeyField()||("array-contains"===e.op||"array-contains-any"===e.op?n=!0:t=t.add(e.field));for(let n of e.orderBy)n.field.isKeyField()||(t=t.add(n.field));return t.size+ +!!n}(t)&&(n=1):n=0})).next(()=>null!==t.limit&&r.length>1&&2===n?1:n)}Nn(e,t){let n=new iF;for(let r of et(e)){let e=t.data.field(r.fieldPath);if(null==e)return null;let i=n.Tn(r.kind);ik.Wt.vt(e,i)}return n.cn()}Mn(e){let t=new iF;return ik.Wt.vt(e,t.Tn(0)),t.cn()}Bn(e,t){let n=new iF;return ik.Wt.vt(t7(this.databaseId,t),n.Tn(function(e){let t=et(e);return 0===t.length?0:t[t.length-1].kind}(e))),n.cn()}Cn(e,t,n){if(null===n)return[];let r=[];r.push(new iF);let i=0;for(let s of et(e)){let e=n[i++];for(let n of r)if(this.Ln(t,s.fieldPath)&&nt(e))r=this.kn(r,s,e);else{let t=n.Tn(s.kind);ik.Wt.vt(e,t)}}return this.qn(r)}vn(e,t,n){return this.Cn(e,t,n.position)}qn(e){let t=[];for(let n=0;n<e.length;++n)t[n]=e[n].cn();return t}kn(e,t,n){let r=[...e],i=[];for(let e of n.arrayValue.values||[])for(let n of r){let r=new iF;r.seed(n.cn()),ik.Wt.vt(e,r.Tn(t.kind)),i.push(r)}return i}Ln(e,t){return!!e.filters.find(e=>e instanceof nw&&e.field.isEqual(t)&&("in"===e.op||"not-in"===e.op))}getFieldIndexes(e,t){let n=tb(e,te),r=tb(e,tn);return(t?n.J(tt,IDBKeyRange.bound(t,t)):n.J()).next(e=>{let t=[];return ec.forEach(e,e=>r.get([e.indexId,this.uid]).next(n=>{t.push(function(e,t){let n=t?new er(t.sequenceNumber,new ea(iI(t.readTime),new X(ek(t.documentKey)),t.largestBatchId)):er.empty(),r=e.fields.map(([e,t])=>new en(J.fromServerFormat(e),t));return new Z(e.indexId,e.collectionGroup,r,n)}(e,n))})).next(()=>t)})}getNextCollectionGroupToUpdate(e){return this.getFieldIndexes(e).next(e=>0===e.length?null:(e.sort((e,t)=>{let n=e.indexState.sequenceNumber-t.indexState.sequenceNumber;return 0!==n?n:B(e.collectionGroup,t.collectionGroup)}),e[0].collectionGroup))}updateCollectionGroup(e,t,n){let r=tb(e,te),i=tb(e,tn);return this.Qn(e).next(e=>r.J(tt,IDBKeyRange.bound(t,t)).next(t=>ec.forEach(t,t=>i.put(iN(t.indexId,this.uid,e,n)))))}updateIndexEntries(e,t){let n=new Map;return ec.forEach(t,(t,r)=>{let i=n.get(t.collectionGroup);return(i?ec.resolve(i):this.getFieldIndexes(e,t.collectionGroup)).next(i=>(n.set(t.collectionGroup,i),ec.forEach(i,n=>this.$n(e,t,n).next(t=>{let i=this.Un(r,n);return t.isEqual(i)?ec.resolve():this.Kn(e,r,n,t,i)}))))})}Wn(e,t,n,r){return tb(e,ta).put({indexId:r.indexId,uid:this.uid,arrayValue:r.arrayValue,directionalValue:r.directionalValue,orderedDocumentKey:this.Bn(n,t.key),documentKey:t.key.path.toArray()})}Gn(e,t,n,r){return tb(e,ta).delete([r.indexId,this.uid,r.arrayValue,r.directionalValue,this.Bn(n,t.key),t.key.path.toArray()])}$n(e,t,n){let r=tb(e,ta),i=new tA(iP);return r.te({index:tl,range:IDBKeyRange.only([n.indexId,this.uid,this.Bn(n,t)])},(e,r)=>{i=i.add(new iL(n.indexId,t,r.arrayValue,r.directionalValue))}).next(()=>i)}Un(e,t){let n=new tA(iP),r=this.Nn(t,e);if(null==r)return n;let i=ee(t);if(null!=i){let s=e.data.field(i.fieldPath);if(nt(s))for(let i of s.arrayValue.values||[])n=n.add(new iL(t.indexId,e.key,this.Mn(i),r))}else n=n.add(new iL(t.indexId,e.key,iY,r));return n}Kn(e,t,n,r,i){T(iH,"Updating index entries for document '%s'",t.key);let s=[];return function(e,t,n,r,i){let s=e.getIterator(),a=t.getIterator(),o=tR(s),l=tR(a);for(;o||l;){let e=!1,t=!1;if(o&&l){let r=n(o,l);r<0?t=!0:r>0&&(e=!0)}else null!=o?t=!0:e=!0;e?(r(l),l=tR(a)):t?(i(o),o=tR(s)):(o=tR(s),l=tR(a))}}(r,i,iP,r=>{s.push(this.Wn(e,t,n,r))},r=>{s.push(this.Gn(e,t,n,r))}),ec.waitFor(s)}Qn(e){let t=1;return tb(e,tn).te({index:ti,reverse:!0,range:IDBKeyRange.upperBound([this.uid,Number.MAX_SAFE_INTEGER])},(e,n,r)=>{r.done(),t=n.sequenceNumber+1}).next(()=>t)}createRange(e,t,n){n=n.sort((e,t)=>iP(e,t)).filter((e,t,n)=>!t||0!==iP(e,n[t-1]));let r=[];for(let i of(r.push(e),n)){let n=iP(i,e),s=iP(i,t);if(0===n)r[0]=e.In();else if(n>0&&s<0)r.push(i),r.push(i.In());else if(s>0)break}r.push(t);let i=[];for(let e=0;e<r.length;e+=2){if(this.zn(r[e],r[e+1]))return[];let t=[r[e].indexId,this.uid,r[e].arrayValue,r[e].directionalValue,iY,[]],n=[r[e+1].indexId,this.uid,r[e+1].arrayValue,r[e+1].directionalValue,iY,[]];i.push(IDBKeyRange.bound(t,n))}return i}zn(e,t){return iP(e,t)>0}getMinOffsetFromCollectionGroup(e,t){return this.getFieldIndexes(e,t).next(iX)}getMinOffset(e,t){return ec.mapArray(this.Sn(t),t=>this.Dn(e,t).next(e=>e||S(44426))).next(iX)}}function iX(e){N(0!==e.length,28825);let t=e[0].indexState.offset,n=t.largestBatchId;for(let r=1;r<e.length;r++){let i=e[r].indexState.offset;0>eo(i,t)&&(t=i),n<i.largestBatchId&&(n=i.largestBatchId)}return new ea(t.readTime,t.documentKey,n)}let iZ={didRun:!1,sequenceNumbersCollected:0,targetsRemoved:0,documentsRemoved:0};class i0{static withCacheSize(e){return new i0(e,i0.DEFAULT_COLLECTION_PERCENTILE,i0.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT)}constructor(e,t,n){this.cacheSizeCollectionThreshold=e,this.percentileToCollect=t,this.maximumSequenceNumbersToCollect=n}}function i1(e,t,n){let r=e.store(eF),i=e.store(eB),s=[],a=IDBKeyRange.only(n.batchId),o=0,l=r.te({range:a},(e,t,n)=>(o++,n.delete()));s.push(l.next(()=>{N(1===o,47070,{batchId:n.batchId})}));let u=[];for(let e of n.mutations){var h,c;let r=(h=e.key.path,c=n.batchId,[t,eA(h),c]);s.push(i.delete(r)),u.push(e.key)}return ec.waitFor(s).next(()=>u)}function i2(e){let t;if(!e)return 0;if(e.document)t=e.document;else if(e.unknownDocument)t=e.unknownDocument;else{if(!e.noDocument)throw S(14731);t=e.noDocument}return JSON.stringify(t).length}i0.DEFAULT_COLLECTION_PERCENTILE=10,i0.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT=1e3,i0.DEFAULT=new i0(0x2800000,i0.DEFAULT_COLLECTION_PERCENTILE,i0.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT),i0.DISABLED=new i0(-1,0,0);class i5{constructor(e,t,n,r){this.userId=e,this.serializer=t,this.indexManager=n,this.referenceDelegate=r,this.jn={}}static bt(e,t,n,r){return N(""!==e.uid,64387),new i5(e.isAuthenticated()?e.uid:"",t,n,r)}checkEmpty(e){let t=!0,n=IDBKeyRange.bound([this.userId,Number.NEGATIVE_INFINITY],[this.userId,Number.POSITIVE_INFINITY]);return i3(e).te({index:eP,range:n},(e,n,r)=>{t=!1,r.done()}).next(()=>t)}addMutationBatch(e,t,n,r){let i=tb(e,eB),s=i3(e);return s.add({}).next(a=>{N("number"==typeof a,49019);let o=new rA(a,t,n,r),l=function(e,t,n){let r=n.baseMutations.map(t=>ia(e.wt,t)),i=n.mutations.map(t=>ia(e.wt,t));return{userId:t,batchId:n.batchId,localWriteTimeMs:n.localWriteTime.toMillis(),baseMutations:r,mutations:i}}(this.serializer,this.userId,o),u=[],h=new tA((e,t)=>B(e.canonicalString(),t.canonicalString()));for(let e of r){var c,d;let t=(c=this.userId,d=e.key.path,[c,eA(d),a]);h=h.add(e.key.path.popLast()),u.push(s.put(l)),u.push(i.put(t,eq))}return h.forEach(t=>{u.push(this.indexManager.addToCollectionParentIndex(e,t))}),e.addOnCommittedListener(()=>{this.jn[a]=o.keys()}),ec.waitFor(u).next(()=>o)})}lookupMutationBatch(e,t){return i3(e).get(t).next(e=>e?(N(e.userId===this.userId,48,"Unexpected user for mutation batch",{userId:e.userId,batchId:t}),iT(this.serializer,e)):null)}Hn(e,t){return this.jn[t]?ec.resolve(this.jn[t]):this.lookupMutationBatch(e,t).next(e=>{if(e){let n=e.keys();return this.jn[t]=n,n}return null})}getNextMutationBatchAfterBatchId(e,t){let n=t+1,r=IDBKeyRange.lowerBound([this.userId,n]),i=null;return i3(e).te({index:eP,range:r},(e,t,r)=>{t.userId===this.userId&&(N(t.batchId>=n,47524,{Jn:n}),i=iT(this.serializer,t)),r.done()}).next(()=>i)}getHighestUnacknowledgedBatchId(e){let t=IDBKeyRange.upperBound([this.userId,Number.POSITIVE_INFINITY]),n=-1;return i3(e).te({index:eP,range:t,reverse:!0},(e,t,r)=>{n=t.batchId,r.done()}).next(()=>n)}getAllMutationBatches(e){let t=IDBKeyRange.bound([this.userId,-1],[this.userId,Number.POSITIVE_INFINITY]);return i3(e).J(eP,t).next(e=>e.map(e=>iT(this.serializer,e)))}getAllMutationBatchesAffectingDocumentKey(e,t){let n=[this.userId,eA(t.path)],r=IDBKeyRange.lowerBound(n),i=[];return tb(e,eB).te({range:r},(n,r,s)=>{let[a,o,l]=n,u=ek(o);if(a===this.userId&&t.path.isEqual(u))return i3(e).get(l).next(e=>{if(!e)throw S(61480,{Yn:n,batchId:l});N(e.userId===this.userId,10503,"Unexpected user for mutation batch",{userId:e.userId,batchId:l}),i.push(iT(this.serializer,e))});s.done()}).next(()=>i)}getAllMutationBatchesAffectingDocumentKeys(e,t){let n=new tA(B),r=[];return t.forEach(t=>{let i=[this.userId,eA(t.path)],s=IDBKeyRange.lowerBound(i),a=tb(e,eB).te({range:s},(e,r,i)=>{let[s,a,o]=e,l=ek(a);s===this.userId&&t.path.isEqual(l)?n=n.add(o):i.done()});r.push(a)}),ec.waitFor(r).next(()=>this.Zn(e,n))}getAllMutationBatchesAffectingQuery(e,t){let n=t.path,r=n.length+1,i=[this.userId,eA(n)],s=IDBKeyRange.lowerBound(i),a=new tA(B);return tb(e,eB).te({range:s},(e,t,i)=>{let[s,o,l]=e,u=ek(o);s===this.userId&&n.isPrefixOf(u)?u.length===r&&(a=a.add(l)):i.done()}).next(()=>this.Zn(e,a))}Zn(e,t){let n=[],r=[];return t.forEach(t=>{r.push(i3(e).get(t).next(e=>{if(null===e)throw S(35274,{batchId:t});N(e.userId===this.userId,9748,"Unexpected user for mutation batch",{userId:e.userId,batchId:t}),n.push(iT(this.serializer,e))}))}),ec.waitFor(r).next(()=>n)}removeMutationBatch(e,t){return i1(e.he,this.userId,t).next(n=>(e.addOnCommittedListener(()=>{this.Xn(t.batchId)}),ec.forEach(n,t=>this.referenceDelegate.markPotentiallyOrphaned(e,t))))}Xn(e){delete this.jn[e]}performConsistencyCheck(e){return this.checkEmpty(e).next(t=>{if(!t)return ec.resolve();let n=IDBKeyRange.lowerBound([this.userId]),r=[];return tb(e,eB).te({range:n},(e,t,n)=>{if(e[0]===this.userId){let t=ek(e[1]);r.push(t)}else n.done()}).next(()=>{N(0===r.length,56720,{er:r.map(e=>e.canonicalString())})})})}containsKey(e,t){return i4(e,this.userId,t)}tr(e){return tb(e,eO).get(this.userId).next(e=>e||{userId:this.userId,lastAcknowledgedBatchId:-1,lastStreamToken:""})}}function i4(e,t,n){let r=[t,eA(n.path)],i=r[1],s=IDBKeyRange.lowerBound(r),a=!1;return tb(e,eB).te({range:s,ee:!0},(e,n,r)=>{let[s,o,l]=e;s===t&&o===i&&(a=!0),r.done()}).next(()=>a)}function i3(e){return tb(e,eF)}class i6{constructor(e){this.nr=e}next(){return this.nr+=2,this.nr}static rr(){return new i6(0)}static ir(){return new i6(-1)}}class i8{constructor(e,t){this.referenceDelegate=e,this.serializer=t}allocateTargetId(e){return this.sr(e).next(t=>{let n=new i6(t.highestTargetId);return t.highestTargetId=n.next(),this._r(e,t).next(()=>t.highestTargetId)})}getLastRemoteSnapshotVersion(e){return this.sr(e).next(e=>j.fromTimestamp(new G(e.lastRemoteSnapshotVersion.seconds,e.lastRemoteSnapshotVersion.nanoseconds)))}getHighestSequenceNumber(e){return this.sr(e).next(e=>e.highestListenSequenceNumber)}setTargetsMetadata(e,t,n){return this.sr(e).next(r=>(r.highestListenSequenceNumber=t,n&&(r.lastRemoteSnapshotVersion=n.toTimestamp()),t>r.highestListenSequenceNumber&&(r.highestListenSequenceNumber=t),this._r(e,r)))}addTargetData(e,t){return this.ar(e,t).next(()=>this.sr(e).next(n=>(n.targetCount+=1,this.ur(t,n),this._r(e,n))))}updateTargetData(e,t){return this.ar(e,t)}removeTargetData(e,t){return this.removeMatchingKeysForTargetId(e,t.targetId).next(()=>tb(e,eY).delete(t.targetId)).next(()=>this.sr(e)).next(t=>(N(t.targetCount>0,8065),t.targetCount-=1,this._r(e,t)))}removeTargets(e,t,n){let r=0,i=[];return tb(e,eY).te((s,a)=>{let o=iE(a);o.sequenceNumber<=t&&null===n.get(o.targetId)&&(r++,i.push(this.removeTargetData(e,o)))}).next(()=>ec.waitFor(i)).next(()=>r)}forEachTarget(e,t){return tb(e,eY).te((e,n)=>{t(iE(n))})}sr(e){return tb(e,e4).get(e5).next(e=>(N(null!==e,2888),e))}_r(e,t){return tb(e,e4).put(e5,t)}ar(e,t){return tb(e,eY).put(ib(this.serializer,t))}ur(e,t){let n=!1;return e.targetId>t.highestTargetId&&(t.highestTargetId=e.targetId,n=!0),e.sequenceNumber>t.highestListenSequenceNumber&&(t.highestListenSequenceNumber=e.sequenceNumber,n=!0),n}getTargetCount(e){return this.sr(e).next(e=>e.targetCount)}getTargetData(e,t){let n=nO(t),r=IDBKeyRange.bound([n,Number.NEGATIVE_INFINITY],[n,Number.POSITIVE_INFINITY]),i=null;return tb(e,eY).te({range:r,index:eJ},(e,n,r)=>{let s=iE(n);nF(t,s.target)&&(i=s,r.done())}).next(()=>i)}addMatchingKeys(e,t,n){let r=[],i=i9(e);return t.forEach(t=>{let s=eA(t.path);r.push(i.put({targetId:n,path:s})),r.push(this.referenceDelegate.addReference(e,n,t))}),ec.waitFor(r)}removeMatchingKeys(e,t,n){let r=i9(e);return ec.forEach(t,t=>{let i=eA(t.path);return ec.waitFor([r.delete([n,i]),this.referenceDelegate.removeReference(e,n,t)])})}removeMatchingKeysForTargetId(e,t){let n=i9(e),r=IDBKeyRange.bound([t],[t+1],!1,!0);return n.delete(r)}getMatchingKeysForTargetId(e,t){let n=IDBKeyRange.bound([t],[t+1],!1,!0),r=i9(e),i=re();return r.te({range:n,ee:!0},(e,t,n)=>{let r=new X(ek(e[1]));i=i.add(r)}).next(()=>i)}containsKey(e,t){let n=eA(t.path),r=IDBKeyRange.bound([n],[n+"\0"],!1,!0),i=0;return i9(e).te({index:e1,ee:!0,range:r},([e,t],n,r)=>{0!==e&&(i++,r.done())}).next(()=>i>0)}Rt(e,t){return tb(e,eY).get(t).next(e=>e?iE(e):null)}}function i9(e){return tb(e,eZ)}let i7="LruGarbageCollector";function se([e,t],[n,r]){let i=B(e,n);return 0===i?B(t,r):i}class st{constructor(e){this.cr=e,this.buffer=new tA(se),this.lr=0}hr(){return++this.lr}Pr(e){let t=[e,this.hr()];if(this.buffer.size<this.cr)this.buffer=this.buffer.add(t);else{let e=this.buffer.last();0>se(t,e)&&(this.buffer=this.buffer.delete(e).add(t))}}get maxValue(){return this.buffer.last()[0]}}class sn{constructor(e,t,n){this.garbageCollector=e,this.asyncQueue=t,this.localStore=n,this.Tr=null}start(){-1!==this.garbageCollector.params.cacheSizeCollectionThreshold&&this.Ir(6e4)}stop(){this.Tr&&(this.Tr.cancel(),this.Tr=null)}get started(){return null!==this.Tr}Ir(e){T(i7,`Garbage collection scheduled in ${e}ms`),this.Tr=this.asyncQueue.enqueueAfterDelay("lru_garbage_collection",e,async()=>{this.Tr=null;try{await this.localStore.collectGarbage(this.garbageCollector)}catch(e){ew(e)?T(i7,"Ignoring IndexedDB error during garbage collection: ",e):await eh(e)}await this.Ir(3e5)})}}class sr{constructor(e,t){this.Er=e,this.params=t}calculateTargetCount(e,t){return this.Er.dr(e).next(e=>Math.floor(t/100*e))}nthSequenceNumber(e,t){if(0===t)return ec.resolve(ex.le);let n=new st(t);return this.Er.forEachTarget(e,e=>n.Pr(e.sequenceNumber)).next(()=>this.Er.Ar(e,e=>n.Pr(e))).next(()=>n.maxValue)}removeTargets(e,t,n){return this.Er.removeTargets(e,t,n)}removeOrphanedDocuments(e,t){return this.Er.removeOrphanedDocuments(e,t)}collect(e,t){return -1===this.params.cacheSizeCollectionThreshold?(T("LruGarbageCollector","Garbage collection skipped; disabled"),ec.resolve(iZ)):this.getCacheSize(e).next(n=>n<this.params.cacheSizeCollectionThreshold?(T("LruGarbageCollector",`Garbage collection skipped; Cache size ${n} is lower than threshold ${this.params.cacheSizeCollectionThreshold}`),iZ):this.Rr(e,t))}getCacheSize(e){return this.Er.getCacheSize(e)}Rr(e,t){let n,r,i,s,a,o,l,h=Date.now();return this.calculateTargetCount(e,this.params.percentileToCollect).next(t=>(t>this.params.maximumSequenceNumbersToCollect?(T("LruGarbageCollector",`Capping sequence numbers to collect down to the maximum of ${this.params.maximumSequenceNumbersToCollect} from ${t}`),r=this.params.maximumSequenceNumbersToCollect):r=t,s=Date.now(),this.nthSequenceNumber(e,r))).next(r=>(n=r,a=Date.now(),this.removeTargets(e,n,t))).next(t=>(i=t,o=Date.now(),this.removeOrphanedDocuments(e,n))).next(e=>(l=Date.now(),I()<=u.$b.DEBUG&&T("LruGarbageCollector",`LRU Garbage Collection
	Counted targets in ${s-h}ms
	Determined least recently used ${r} in `+(a-s)+"ms\n"+`	Removed ${i} targets in `+(o-a)+"ms\n"+`	Removed ${e} documents in `+(l-o)+"ms\n"+`Total Duration: ${l-h}ms`),ec.resolve({didRun:!0,sequenceNumbersCollected:r,targetsRemoved:i,documentsRemoved:e})))}}class si{constructor(e,t){this.db=e,this.garbageCollector=new sr(this,t)}dr(e){let t=this.Vr(e);return this.db.getTargetCache().getTargetCount(e).next(e=>t.next(t=>e+t))}Vr(e){let t=0;return this.Ar(e,e=>{t++}).next(()=>t)}forEachTarget(e,t){return this.db.getTargetCache().forEachTarget(e,t)}Ar(e,t){return this.mr(e,(e,n)=>t(n))}addReference(e,t,n){return ss(e,n)}removeReference(e,t,n){return ss(e,n)}removeTargets(e,t,n){return this.db.getTargetCache().removeTargets(e,t,n)}markPotentiallyOrphaned(e,t){return ss(e,t)}gr(e,t){let n;return n=!1,tb(e,eO).ne(r=>i4(e,r,t).next(e=>(e&&(n=!0),ec.resolve(!e)))).next(()=>n)}removeOrphanedDocuments(e,t){let n=this.db.getRemoteDocumentCache().newChangeBuffer(),r=[],i=0;return this.mr(e,(s,a)=>{if(a<=t){let t=this.gr(e,s).next(t=>{if(!t)return i++,n.getEntry(e,s).next(()=>(n.removeEntry(s,j.min()),i9(e).delete([0,eA(s.path)])))});r.push(t)}}).next(()=>ec.waitFor(r)).next(()=>n.apply(e)).next(()=>i)}removeTarget(e,t){let n=t.withSequenceNumber(e.currentSequenceNumber);return this.db.getTargetCache().updateTargetData(e,n)}updateLimboDocument(e,t){return ss(e,t)}mr(e,t){let n=i9(e),r,i=ex.le;return n.te({index:e1},([e,n],{path:s,sequenceNumber:a})=>{0===e?(i!==ex.le&&t(new X(ek(r)),i),i=a,r=s):i=ex.le}).next(()=>{i!==ex.le&&t(new X(ek(r)),i)})}getCacheSize(e){return this.db.getRemoteDocumentCache().getSize(e)}}function ss(e,t){var n;return i9(e).put((n=e.currentSequenceNumber,{targetId:0,path:eA(t.path),sequenceNumber:n}))}class sa{constructor(){this.changes=new n2(e=>e.toString(),(e,t)=>e.isEqual(t)),this.changesApplied=!1}addEntry(e){this.assertNotApplied(),this.changes.set(e.key,e)}removeEntry(e,t){this.assertNotApplied(),this.changes.set(e,nd.newInvalidDocument(e).setReadTime(t))}getEntry(e,t){this.assertNotApplied();let n=this.changes.get(t);return void 0!==n?ec.resolve(n):this.getFromCache(e,t)}getEntries(e,t){return this.getAllFromCache(e,t)}apply(e){return this.assertNotApplied(),this.changesApplied=!0,this.applyChanges(e)}assertNotApplied(){}}class so{constructor(e){this.serializer=e}setIndexManager(e){this.indexManager=e}addEntry(e,t,n){return tb(e,ez).put(n)}removeEntry(e,t,n){return tb(e,ez).delete(function(e,t){let n=e.path.toArray();return[n.slice(0,n.length-2),n[n.length-2],iw(t),n[n.length-1]]}(t,n))}updateMetadata(e,t){return this.getMetadata(e).next(n=>(n.byteSize+=t,this.pr(e,n)))}getEntry(e,t){let n=nd.newInvalidDocument(t);return tb(e,ez).te({index:e$,range:IDBKeyRange.only(su(t))},(e,r)=>{n=this.yr(t,r)}).next(()=>n)}wr(e,t){let n={size:0,document:nd.newInvalidDocument(t)};return tb(e,ez).te({index:e$,range:IDBKeyRange.only(su(t))},(e,r)=>{n={document:this.yr(t,r),size:i2(r)}}).next(()=>n)}getEntries(e,t){let n=n5;return this.br(e,t,(e,t)=>{let r=this.yr(e,t);n=n.insert(e,r)}).next(()=>n)}Sr(e,t){let n=n5,r=new tN(X.comparator);return this.br(e,t,(e,t)=>{let i=this.yr(e,t);n=n.insert(e,i),r=r.insert(e,i2(t))}).next(()=>({documents:n,Dr:r}))}br(e,t,n){if(t.isEmpty())return ec.resolve();let r=new tA(sc);t.forEach(e=>r=r.add(e));let i=IDBKeyRange.bound(su(r.first()),su(r.last())),s=r.getIterator(),a=s.getNext();return tb(e,ez).te({index:e$,range:i},(e,t,r)=>{let i=X.fromSegments([...t.prefixPath,t.collectionGroup,t.documentId]);for(;a&&0>sc(a,i);)n(a,null),a=s.getNext();a&&a.isEqual(i)&&(n(a,t),a=s.hasNext()?s.getNext():null),a?r.H(su(a)):r.done()}).next(()=>{for(;a;)n(a,null),a=s.hasNext()?s.getNext():null})}getDocumentsMatchingQuery(e,t,n,r,i){let s=t.path,a=[s.popLast().toArray(),s.lastSegment(),iw(n.readTime),n.documentKey.path.isEmpty()?"":n.documentKey.path.lastSegment()],o=[s.popLast().toArray(),s.lastSegment(),[Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],""];return tb(e,ez).J(IDBKeyRange.bound(a,o,!0)).next(e=>{null==i||i.incrementDocumentReadCount(e.length);let n=n5;for(let i of e){let e=this.yr(X.fromSegments(i.prefixPath.concat(i.collectionGroup,i.documentId)),i);e.isFoundDocument()&&(nZ(t,e)||r.has(e.key))&&(n=n.insert(e.key,e))}return n})}getAllFromCollectionGroup(e,t,n,r){let i=n5,s=sh(t,n),a=sh(t,ea.max());return tb(e,ez).te({index:ej,range:IDBKeyRange.bound(s,a,!0)},(e,t,n)=>{let s=this.yr(X.fromSegments(t.prefixPath.concat(t.collectionGroup,t.documentId)),t);(i=i.insert(s.key,s)).size===r&&n.done()}).next(()=>i)}newChangeBuffer(e){return new sl(this,!!e&&e.trackRemovals)}getSize(e){return this.getMetadata(e).next(e=>e.byteSize)}getMetadata(e){return tb(e,eW).get(eH).next(e=>(N(!!e,20021),e))}pr(e,t){return tb(e,eW).put(eH,t)}yr(e,t){if(t){let e=function(e,t){let n;if(t.document)n=is(e.wt,t.document,!!t.hasCommittedMutations);else if(t.noDocument){let e=X.fromSegments(t.noDocument.path),r=iI(t.noDocument.readTime);n=nd.newNoDocument(e,r),t.hasCommittedMutations&&n.setHasCommittedMutations()}else{if(!t.unknownDocument)return S(56709);{let e=X.fromSegments(t.unknownDocument.path),r=iI(t.unknownDocument.version);n=nd.newUnknownDocument(e,r)}}return t.readTime&&n.setReadTime(function(e){let t=new G(e[0],e[1]);return j.fromTimestamp(t)}(t.readTime)),n}(this.serializer,t);if(!(e.isNoDocument()&&e.version.isEqual(j.min())))return e}return nd.newInvalidDocument(e)}}class sl extends sa{constructor(e,t){super(),this.vr=e,this.trackRemovals=t,this.Cr=new n2(e=>e.toString(),(e,t)=>e.isEqual(t))}applyChanges(e){let t=[],n=0,r=new tA((e,t)=>B(e.canonicalString(),t.canonicalString()));return this.changes.forEach((i,s)=>{let a=this.Cr.get(i);if(t.push(this.vr.removeEntry(e,i,a.readTime)),s.isValidDocument()){let o=iy(this.vr.serializer,s);r=r.add(i.path.popLast());let l=i2(o);n+=l-a.size,t.push(this.vr.addEntry(e,i,o))}else if(n-=a.size,this.trackRemovals){let n=iy(this.vr.serializer,s.convertToNoDocument(j.min()));t.push(this.vr.addEntry(e,i,n))}}),r.forEach(n=>{t.push(this.vr.indexManager.addToCollectionParentIndex(e,n))}),t.push(this.vr.updateMetadata(e,n)),ec.waitFor(t)}getFromCache(e,t){return this.vr.wr(e,t).next(e=>(this.Cr.set(t,{size:e.size,readTime:e.document.readTime}),e.document))}getAllFromCache(e,t){return this.vr.Sr(e,t).next(({documents:e,Dr:t})=>(t.forEach((t,n)=>{this.Cr.set(t,{size:n,readTime:e.get(t).readTime})}),e))}}function su(e){let t=e.path.toArray();return[t.slice(0,t.length-2),t[t.length-2],t[t.length-1]]}function sh(e,t){let n=t.documentKey.path.toArray();return[e,iw(t.readTime),n.slice(0,n.length-2),n.length>0?n[n.length-1]:""]}function sc(e,t){let n=e.path.toArray(),r=t.path.toArray(),i=0;for(let e=0;e<n.length-2&&e<r.length-2;++e)if(i=B(n[e],r[e]))return i;return(i=B(n.length,r.length))||(i=B(n[n.length-2],r[r.length-2]))||B(n[n.length-1],r[r.length-1])}class sd{constructor(e,t){this.overlayedDocument=e,this.mutatedFields=t}}class sf{constructor(e,t,n,r){this.remoteDocumentCache=e,this.mutationQueue=t,this.documentOverlayCache=n,this.indexManager=r}getDocument(e,t){let n=null;return this.documentOverlayCache.getOverlay(e,t).next(r=>(n=r,this.remoteDocumentCache.getEntry(e,t))).next(e=>(null!==n&&rT(n.mutation,e,tV.empty(),G.now()),e))}getDocuments(e,t){return this.remoteDocumentCache.getEntries(e,t).next(t=>this.getLocalViewOfDocuments(e,t,re()).next(()=>t))}getLocalViewOfDocuments(e,t,n=re()){let r=n8();return this.populateOverlays(e,r,t).next(()=>this.computeViews(e,t,r,n).next(e=>{let t=n3();return e.forEach((e,n)=>{t=t.insert(e,n.overlayedDocument)}),t}))}getOverlayedDocuments(e,t){let n=n8();return this.populateOverlays(e,n,t).next(()=>this.computeViews(e,t,n,re()))}populateOverlays(e,t,n){let r=[];return n.forEach(e=>{t.has(e)||r.push(e)}),this.documentOverlayCache.getOverlays(e,r).next(e=>{e.forEach((e,n)=>{t.set(e,n)})})}computeViews(e,t,n,r){let i=n5,s=n8(),a=n8();return t.forEach((e,t)=>{let a=n.get(t.key);r.has(t.key)&&(void 0===a||a.mutation instanceof r_)?i=i.insert(t.key,t):void 0!==a?(s.set(t.key,a.mutation.getFieldMask()),rT(a.mutation,t,a.mutation.getFieldMask(),G.now())):s.set(t.key,tV.empty())}),this.recalculateAndSaveOverlays(e,i).next(e=>(e.forEach((e,t)=>s.set(e,t)),t.forEach((e,t)=>{var n;return a.set(e,new sd(t,null!=(n=s.get(e))?n:null))}),a))}recalculateAndSaveOverlays(e,t){let n=n8(),r=new tN((e,t)=>e-t),i=re();return this.mutationQueue.getAllMutationBatchesAffectingDocumentKeys(e,t).next(e=>{for(let i of e)i.keys().forEach(e=>{let s=t.get(e);if(null===s)return;let a=n.get(e)||tV.empty();a=i.applyToLocalView(s,a),n.set(e,a);let o=(r.get(i.batchId)||re()).add(e);r=r.insert(i.batchId,o)})}).next(()=>{let s=[],a=r.getReverseIterator();for(;a.hasNext();){let r=a.getNext(),o=r.key,l=r.value,u=n8();l.forEach(e=>{if(!i.has(e)){let r=rI(t.get(e),n.get(e));null!==r&&u.set(e,r),i=i.add(e)}}),s.push(this.documentOverlayCache.saveOverlays(e,o,u))}return ec.waitFor(s)}).next(()=>n)}recalculateAndSaveOverlaysForDocumentKeys(e,t){return this.remoteDocumentCache.getEntries(e,t).next(t=>this.recalculateAndSaveOverlays(e,t))}getDocumentsMatchingQuery(e,t,n,r){return X.isDocumentKey(t.path)&&null===t.collectionGroup&&0===t.filters.length?this.getDocumentsMatchingDocumentQuery(e,t.path):n$(t)?this.getDocumentsMatchingCollectionGroupQuery(e,t,n,r):this.getDocumentsMatchingCollectionQuery(e,t,n,r)}getNextDocuments(e,t,n,r){return this.remoteDocumentCache.getAllFromCollectionGroup(e,t,n,r).next(i=>{let s=r-i.size>0?this.documentOverlayCache.getOverlaysForCollectionGroup(e,t,n.largestBatchId,r-i.size):ec.resolve(n8()),a=-1,o=i;return s.next(t=>ec.forEach(t,(t,n)=>(a<n.largestBatchId&&(a=n.largestBatchId),i.get(t)?ec.resolve():this.remoteDocumentCache.getEntry(e,t).next(e=>{o=o.insert(t,e)}))).next(()=>this.populateOverlays(e,t,i)).next(()=>this.computeViews(e,o,t,re())).next(e=>({batchId:a,changes:n6(e)})))})}getDocumentsMatchingDocumentQuery(e,t){return this.getDocument(e,new X(t)).next(e=>{let t=n3();return e.isFoundDocument()&&(t=t.insert(e.key,e)),t})}getDocumentsMatchingCollectionGroupQuery(e,t,n,r){let i=t.collectionGroup,s=n3();return this.indexManager.getCollectionParents(e,i).next(a=>ec.forEach(a,a=>{let o=new nB(a.child(i),null,t.explicitOrderBy.slice(),t.filters.slice(),t.limit,t.limitType,t.startAt,t.endAt);return this.getDocumentsMatchingCollectionQuery(e,o,n,r).next(e=>{e.forEach((e,t)=>{s=s.insert(e,t)})})}).next(()=>s))}getDocumentsMatchingCollectionQuery(e,t,n,r){let i;return this.documentOverlayCache.getOverlaysForCollection(e,t.path,n.largestBatchId).next(s=>(i=s,this.remoteDocumentCache.getDocumentsMatchingQuery(e,t,n,i,r))).next(e=>{i.forEach((t,n)=>{let r=n.getKey();null===e.get(r)&&(e=e.insert(r,nd.newInvalidDocument(r)))});let n=n3();return e.forEach((e,r)=>{let s=i.get(e);void 0!==s&&rT(s.mutation,r,tV.empty(),G.now()),nZ(t,r)&&(n=n.insert(e,r))}),n})}}class sm{constructor(e){this.serializer=e,this.Fr=new Map,this.Mr=new Map}getBundleMetadata(e,t){return ec.resolve(this.Fr.get(t))}saveBundleMetadata(e,t){return this.Fr.set(t.id,{id:t.id,version:t.version,createTime:r5(t.createTime)}),ec.resolve()}getNamedQuery(e,t){return ec.resolve(this.Mr.get(t))}saveNamedQuery(e,t){return this.Mr.set(t.name,{name:t.name,query:i_(t.bundledQuery),readTime:r5(t.readTime)}),ec.resolve()}}class sg{constructor(){this.overlays=new tN(X.comparator),this.Or=new Map}getOverlay(e,t){return ec.resolve(this.overlays.get(t))}getOverlays(e,t){let n=n8();return ec.forEach(t,t=>this.getOverlay(e,t).next(e=>{null!==e&&n.set(t,e)})).next(()=>n)}saveOverlays(e,t,n){return n.forEach((n,r)=>{this.St(e,t,r)}),ec.resolve()}removeOverlaysForBatchId(e,t,n){let r=this.Or.get(n);return void 0!==r&&(r.forEach(e=>this.overlays=this.overlays.remove(e)),this.Or.delete(n)),ec.resolve()}getOverlaysForCollection(e,t,n){let r=n8(),i=t.length+1,s=new X(t.child("")),a=this.overlays.getIteratorFrom(s);for(;a.hasNext();){let e=a.getNext().value,s=e.getKey();if(!t.isPrefixOf(s.path))break;s.path.length===i&&e.largestBatchId>n&&r.set(e.getKey(),e)}return ec.resolve(r)}getOverlaysForCollectionGroup(e,t,n,r){let i=new tN((e,t)=>e-t),s=this.overlays.getIterator();for(;s.hasNext();){let e=s.getNext().value;if(e.getKey().getCollectionGroup()===t&&e.largestBatchId>n){let t=i.get(e.largestBatchId);null===t&&(t=n8(),i=i.insert(e.largestBatchId,t)),t.set(e.getKey(),e)}}let a=n8(),o=i.getIterator();for(;o.hasNext()&&(o.getNext().value.forEach((e,t)=>a.set(e,t)),!(a.size()>=r)););return ec.resolve(a)}St(e,t,n){let r=this.overlays.get(n.key);if(null!==r){let e=this.Or.get(r.largestBatchId).delete(n.key);this.Or.set(r.largestBatchId,e)}this.overlays=this.overlays.insert(n.key,new rR(t,n));let i=this.Or.get(t);void 0===i&&(i=re(),this.Or.set(t,i)),this.Or.set(t,i.add(n.key))}}class sp{constructor(){this.sessionToken=tO.EMPTY_BYTE_STRING}getSessionToken(e){return ec.resolve(this.sessionToken)}setSessionToken(e,t){return this.sessionToken=t,ec.resolve()}}class sy{constructor(){this.Nr=new tA(sw.Br),this.Lr=new tA(sw.kr)}isEmpty(){return this.Nr.isEmpty()}addReference(e,t){let n=new sw(e,t);this.Nr=this.Nr.add(n),this.Lr=this.Lr.add(n)}qr(e,t){e.forEach(e=>this.addReference(e,t))}removeReference(e,t){this.Qr(new sw(e,t))}$r(e,t){e.forEach(e=>this.removeReference(e,t))}Ur(e){let t=new X(new H([])),n=new sw(t,e),r=new sw(t,e+1),i=[];return this.Lr.forEachInRange([n,r],e=>{this.Qr(e),i.push(e.key)}),i}Kr(){this.Nr.forEach(e=>this.Qr(e))}Qr(e){this.Nr=this.Nr.delete(e),this.Lr=this.Lr.delete(e)}Wr(e){let t=new X(new H([])),n=new sw(t,e),r=new sw(t,e+1),i=re();return this.Lr.forEachInRange([n,r],e=>{i=i.add(e.key)}),i}containsKey(e){let t=new sw(e,0),n=this.Nr.firstAfterOrEqual(t);return null!==n&&e.isEqual(n.key)}}class sw{constructor(e,t){this.key=e,this.Gr=t}static Br(e,t){return X.comparator(e.key,t.key)||B(e.Gr,t.Gr)}static kr(e,t){return B(e.Gr,t.Gr)||X.comparator(e.key,t.key)}}class sv{constructor(e,t){this.indexManager=e,this.referenceDelegate=t,this.mutationQueue=[],this.Jn=1,this.zr=new tA(sw.Br)}checkEmpty(e){return ec.resolve(0===this.mutationQueue.length)}addMutationBatch(e,t,n,r){let i=this.Jn;this.Jn++,this.mutationQueue.length>0&&this.mutationQueue[this.mutationQueue.length-1];let s=new rA(i,t,n,r);for(let t of(this.mutationQueue.push(s),r))this.zr=this.zr.add(new sw(t.key,i)),this.indexManager.addToCollectionParentIndex(e,t.key.path.popLast());return ec.resolve(s)}lookupMutationBatch(e,t){return ec.resolve(this.jr(t))}getNextMutationBatchAfterBatchId(e,t){let n=this.Hr(t+1),r=n<0?0:n;return ec.resolve(this.mutationQueue.length>r?this.mutationQueue[r]:null)}getHighestUnacknowledgedBatchId(){return ec.resolve(0===this.mutationQueue.length?-1:this.Jn-1)}getAllMutationBatches(e){return ec.resolve(this.mutationQueue.slice())}getAllMutationBatchesAffectingDocumentKey(e,t){let n=new sw(t,0),r=new sw(t,Number.POSITIVE_INFINITY),i=[];return this.zr.forEachInRange([n,r],e=>{let t=this.jr(e.Gr);i.push(t)}),ec.resolve(i)}getAllMutationBatchesAffectingDocumentKeys(e,t){let n=new tA(B);return t.forEach(e=>{let t=new sw(e,0),r=new sw(e,Number.POSITIVE_INFINITY);this.zr.forEachInRange([t,r],e=>{n=n.add(e.Gr)})}),ec.resolve(this.Jr(n))}getAllMutationBatchesAffectingQuery(e,t){let n=t.path,r=n.length+1,i=n;X.isDocumentKey(i)||(i=i.child(""));let s=new sw(new X(i),0),a=new tA(B);return this.zr.forEachWhile(e=>{let t=e.key.path;return!!n.isPrefixOf(t)&&(t.length===r&&(a=a.add(e.Gr)),!0)},s),ec.resolve(this.Jr(a))}Jr(e){let t=[];return e.forEach(e=>{let n=this.jr(e);null!==n&&t.push(n)}),t}removeMutationBatch(e,t){N(0===this.Yr(t.batchId,"removed"),55003),this.mutationQueue.shift();let n=this.zr;return ec.forEach(t.mutations,r=>{let i=new sw(r.key,t.batchId);return n=n.delete(i),this.referenceDelegate.markPotentiallyOrphaned(e,r.key)}).next(()=>{this.zr=n})}Xn(e){}containsKey(e,t){let n=new sw(t,0),r=this.zr.firstAfterOrEqual(n);return ec.resolve(t.isEqual(r&&r.key))}performConsistencyCheck(e){return this.mutationQueue.length,ec.resolve()}Yr(e,t){return this.Hr(e)}Hr(e){return 0===this.mutationQueue.length?0:e-this.mutationQueue[0].batchId}jr(e){let t=this.Hr(e);return t<0||t>=this.mutationQueue.length?null:this.mutationQueue[t]}}class sI{constructor(e){this.Zr=e,this.docs=new tN(X.comparator),this.size=0}setIndexManager(e){this.indexManager=e}addEntry(e,t){let n=t.key,r=this.docs.get(n),i=r?r.size:0,s=this.Zr(t);return this.docs=this.docs.insert(n,{document:t.mutableCopy(),size:s}),this.size+=s-i,this.indexManager.addToCollectionParentIndex(e,n.path.popLast())}removeEntry(e){let t=this.docs.get(e);t&&(this.docs=this.docs.remove(e),this.size-=t.size)}getEntry(e,t){let n=this.docs.get(t);return ec.resolve(n?n.document.mutableCopy():nd.newInvalidDocument(t))}getEntries(e,t){let n=n5;return t.forEach(e=>{let t=this.docs.get(e);n=n.insert(e,t?t.document.mutableCopy():nd.newInvalidDocument(e))}),ec.resolve(n)}getDocumentsMatchingQuery(e,t,n,r){let i=n5,s=t.path,a=new X(s.child("__id-9223372036854775808__")),o=this.docs.getIteratorFrom(a);for(;o.hasNext();){let{key:e,value:{document:a}}=o.getNext();if(!s.isPrefixOf(e.path))break;e.path.length>s.length+1||0>=eo(es(a),n)||(r.has(a.key)||nZ(t,a))&&(i=i.insert(a.key,a.mutableCopy()))}return ec.resolve(i)}getAllFromCollectionGroup(e,t,n,r){S(9500)}Xr(e,t){return ec.forEach(this.docs,e=>t(e))}newChangeBuffer(e){return new sT(this)}getSize(e){return ec.resolve(this.size)}}class sT extends sa{constructor(e){super(),this.vr=e}applyChanges(e){let t=[];return this.changes.forEach((n,r)=>{r.isValidDocument()?t.push(this.vr.addEntry(e,r)):this.vr.removeEntry(n)}),ec.waitFor(t)}getFromCache(e,t){return this.vr.getEntry(e,t)}getAllFromCache(e,t){return this.vr.getEntries(e,t)}}class sE{constructor(e){this.persistence=e,this.ei=new n2(e=>nO(e),nF),this.lastRemoteSnapshotVersion=j.min(),this.highestTargetId=0,this.ti=0,this.ni=new sy,this.targetCount=0,this.ri=i6.rr()}forEachTarget(e,t){return this.ei.forEach((e,n)=>t(n)),ec.resolve()}getLastRemoteSnapshotVersion(e){return ec.resolve(this.lastRemoteSnapshotVersion)}getHighestSequenceNumber(e){return ec.resolve(this.ti)}allocateTargetId(e){return this.highestTargetId=this.ri.next(),ec.resolve(this.highestTargetId)}setTargetsMetadata(e,t,n){return n&&(this.lastRemoteSnapshotVersion=n),t>this.ti&&(this.ti=t),ec.resolve()}ar(e){this.ei.set(e.target,e);let t=e.targetId;t>this.highestTargetId&&(this.ri=new i6(t),this.highestTargetId=t),e.sequenceNumber>this.ti&&(this.ti=e.sequenceNumber)}addTargetData(e,t){return this.ar(t),this.targetCount+=1,ec.resolve()}updateTargetData(e,t){return this.ar(t),ec.resolve()}removeTargetData(e,t){return this.ei.delete(t.target),this.ni.Ur(t.targetId),this.targetCount-=1,ec.resolve()}removeTargets(e,t,n){let r=0,i=[];return this.ei.forEach((s,a)=>{a.sequenceNumber<=t&&null===n.get(a.targetId)&&(this.ei.delete(s),i.push(this.removeMatchingKeysForTargetId(e,a.targetId)),r++)}),ec.waitFor(i).next(()=>r)}getTargetCount(e){return ec.resolve(this.targetCount)}getTargetData(e,t){let n=this.ei.get(t)||null;return ec.resolve(n)}addMatchingKeys(e,t,n){return this.ni.qr(t,n),ec.resolve()}removeMatchingKeys(e,t,n){this.ni.$r(t,n);let r=this.persistence.referenceDelegate,i=[];return r&&t.forEach(t=>{i.push(r.markPotentiallyOrphaned(e,t))}),ec.waitFor(i)}removeMatchingKeysForTargetId(e,t){return this.ni.Ur(t),ec.resolve()}getMatchingKeysForTargetId(e,t){let n=this.ni.Wr(t);return ec.resolve(n)}containsKey(e,t){return ec.resolve(this.ni.containsKey(t))}}class sb{constructor(e,t){this.ii={},this.overlays={},this.si=new ex(0),this.oi=!1,this.oi=!0,this._i=new sp,this.referenceDelegate=e(this),this.ai=new sE(this),this.indexManager=new iQ,this.remoteDocumentCache=new sI(e=>this.referenceDelegate.ui(e)),this.serializer=new ip(t),this.ci=new sm(this.serializer)}start(){return Promise.resolve()}shutdown(){return this.oi=!1,Promise.resolve()}get started(){return this.oi}setDatabaseDeletedListener(){}setNetworkEnabled(){}getIndexManager(e){return this.indexManager}getDocumentOverlayCache(e){let t=this.overlays[e.toKey()];return t||(t=new sg,this.overlays[e.toKey()]=t),t}getMutationQueue(e,t){let n=this.ii[e.toKey()];return n||(n=new sv(t,this.referenceDelegate),this.ii[e.toKey()]=n),n}getGlobalsCache(){return this._i}getTargetCache(){return this.ai}getRemoteDocumentCache(){return this.remoteDocumentCache}getBundleCache(){return this.ci}runTransaction(e,t,n){T("MemoryPersistence","Starting transaction:",e);let r=new s_(this.si.next());return this.referenceDelegate.li(),n(r).next(e=>this.referenceDelegate.hi(r).next(()=>e)).toPromise().then(e=>(r.raiseOnCommittedEvent(),e))}Pi(e,t){return ec.or(Object.values(this.ii).map(n=>()=>n.containsKey(e,t)))}}class s_ extends eu{constructor(e){super(),this.currentSequenceNumber=e}}class sS{constructor(e){this.persistence=e,this.Ti=new sy,this.Ii=null}static Ei(e){return new sS(e)}get di(){if(this.Ii)return this.Ii;throw S(60996)}addReference(e,t,n){return this.Ti.addReference(n,t),this.di.delete(n.toString()),ec.resolve()}removeReference(e,t,n){return this.Ti.removeReference(n,t),this.di.add(n.toString()),ec.resolve()}markPotentiallyOrphaned(e,t){return this.di.add(t.toString()),ec.resolve()}removeTarget(e,t){this.Ti.Ur(t.targetId).forEach(e=>this.di.add(e.toString()));let n=this.persistence.getTargetCache();return n.getMatchingKeysForTargetId(e,t.targetId).next(e=>{e.forEach(e=>this.di.add(e.toString()))}).next(()=>n.removeTargetData(e,t))}li(){this.Ii=new Set}hi(e){let t=this.persistence.getRemoteDocumentCache().newChangeBuffer();return ec.forEach(this.di,n=>{let r=X.fromPath(n);return this.Ai(e,r).next(e=>{e||t.removeEntry(r,j.min())})}).next(()=>(this.Ii=null,t.apply(e)))}updateLimboDocument(e,t){return this.Ai(e,t).next(e=>{e?this.di.delete(t.toString()):this.di.add(t.toString())})}ui(e){return 0}Ai(e,t){return ec.or([()=>ec.resolve(this.Ti.containsKey(t)),()=>this.persistence.getTargetCache().containsKey(e,t),()=>this.persistence.Pi(e,t)])}}class sx{constructor(e,t){this.persistence=e,this.Ri=new n2(e=>eA(e.path),(e,t)=>e.isEqual(t)),this.garbageCollector=new sr(this,t)}static Ei(e,t){return new sx(e,t)}li(){}hi(e){return ec.resolve()}forEachTarget(e,t){return this.persistence.getTargetCache().forEachTarget(e,t)}dr(e){let t=this.Vr(e);return this.persistence.getTargetCache().getTargetCount(e).next(e=>t.next(t=>e+t))}Vr(e){let t=0;return this.Ar(e,e=>{t++}).next(()=>t)}Ar(e,t){return ec.forEach(this.Ri,(n,r)=>this.gr(e,n,r).next(e=>e?ec.resolve():t(r)))}removeTargets(e,t,n){return this.persistence.getTargetCache().removeTargets(e,t,n)}removeOrphanedDocuments(e,t){let n=0,r=this.persistence.getRemoteDocumentCache(),i=r.newChangeBuffer();return r.Xr(e,r=>this.gr(e,r,t).next(e=>{e||(n++,i.removeEntry(r,j.min()))})).next(()=>i.apply(e)).next(()=>n)}markPotentiallyOrphaned(e,t){return this.Ri.set(t,e.currentSequenceNumber),ec.resolve()}removeTarget(e,t){let n=t.withSequenceNumber(e.currentSequenceNumber);return this.persistence.getTargetCache().updateTargetData(e,n)}addReference(e,t,n){return this.Ri.set(n,e.currentSequenceNumber),ec.resolve()}removeReference(e,t,n){return this.Ri.set(n,e.currentSequenceNumber),ec.resolve()}updateLimboDocument(e,t){return this.Ri.set(t,e.currentSequenceNumber),ec.resolve()}ui(e){let t=e.key.toString().length;return e.isFoundDocument()&&(t+=function e(t){switch(t2(t)){case 0:case 1:return 4;case 2:return 8;case 3:case 8:return 16;case 4:let n=tG(t);return n?16+e(n):16;case 5:return 2*t.stringValue.length;case 6:return tU(t.bytesValue).approximateByteSize();case 7:return t.referenceValue.length;case 9:return(t.arrayValue.values||[]).reduce((t,n)=>t+e(n),0);case 10:case 11:var r;let i;return r=t.mapValue,i=0,tS(r.fields,(t,n)=>{i+=t.length+e(n)}),i;default:throw S(13486,{value:t})}}(e.data.value)),t}gr(e,t,n){return ec.or([()=>this.persistence.Pi(e,t),()=>this.persistence.getTargetCache().containsKey(e,t),()=>{let e=this.Ri.get(t);return ec.resolve(void 0!==e&&e>n)}])}getCacheSize(e){return this.persistence.getRemoteDocumentCache().getSize(e)}}class sN{constructor(e){this.serializer=e}q(e,t,n,r){let i=new ef("createOrUpgrade",t);n<1&&r>=1&&(e.createObjectStore(eV),e.createObjectStore(eO,{keyPath:"userId"}),e.createObjectStore(eF,{keyPath:eL,autoIncrement:!0}).createIndex(eP,eU,{unique:!0}),e.createObjectStore(eB),sC(e),e.createObjectStore(eR));let s=ec.resolve();return n<3&&r>=3&&(0!==n&&(e.deleteObjectStore(eZ),e.deleteObjectStore(eY),e.deleteObjectStore(e4),sC(e)),s=s.next(()=>(function(e){let t=e.store(e4),n={highestTargetId:0,highestListenSequenceNumber:0,lastRemoteSnapshotVersion:j.min().toTimestamp(),targetCount:0};return t.put(e5,n)})(i))),n<4&&r>=4&&(0!==n&&(s=s.next(()=>i.store(eF).J().next(t=>{e.deleteObjectStore(eF),e.createObjectStore(eF,{keyPath:eL,autoIncrement:!0}).createIndex(eP,eU,{unique:!0});let n=i.store(eF),r=t.map(e=>n.put(e));return ec.waitFor(r)}))),s=s.next(()=>{e.createObjectStore(e8,{keyPath:"clientId"})})),n<5&&r>=5&&(s=s.next(()=>this.Vi(i))),n<6&&r>=6&&(s=s.next(()=>(e.createObjectStore(eW),this.mi(i)))),n<7&&r>=7&&(s=s.next(()=>this.fi(i))),n<8&&r>=8&&(s=s.next(()=>this.gi(e,i))),n<9&&r>=9&&(s=s.next(()=>{e.objectStoreNames.contains("remoteDocumentChanges")&&e.deleteObjectStore("remoteDocumentChanges")})),n<10&&r>=10&&(s=s.next(()=>this.pi(i))),n<11&&r>=11&&(s=s.next(()=>{e.createObjectStore(e9,{keyPath:"bundleId"}),e.createObjectStore(e7,{keyPath:"name"})})),n<12&&r>=12&&(s=s.next(()=>{let t=e.createObjectStore(th,{keyPath:tc});t.createIndex(td,tf,{unique:!1}),t.createIndex(tm,tg,{unique:!1})})),n<13&&r>=13&&(s=s.next(()=>(function(e){let t=e.createObjectStore(ez,{keyPath:eK});t.createIndex(e$,eG),t.createIndex(ej,eQ)})(e)).next(()=>this.yi(e,i)).next(()=>e.deleteObjectStore(eR))),n<14&&r>=14&&(s=s.next(()=>this.wi(e,i))),n<15&&r>=15&&(s=s.next(()=>{e.createObjectStore(te,{keyPath:"indexId",autoIncrement:!0}).createIndex(tt,"collectionGroup",{unique:!1}),e.createObjectStore(tn,{keyPath:tr}).createIndex(ti,ts,{unique:!1}),e.createObjectStore(ta,{keyPath:to}).createIndex(tl,tu,{unique:!1})})),n<16&&r>=16&&(s=s.next(()=>{t.objectStore(tn).clear()}).next(()=>{t.objectStore(ta).clear()})),n<17&&r>=17&&(s=s.next(()=>{e.createObjectStore(tp,{keyPath:"name"})})),s}mi(e){let t=0;return e.store(eR).te((e,n)=>{t+=i2(n)}).next(()=>{let n={byteSize:t};return e.store(eW).put(eH,n)})}Vi(e){let t=e.store(eO),n=e.store(eF);return t.J().next(t=>ec.forEach(t,t=>{let r=IDBKeyRange.bound([t.userId,-1],[t.userId,t.lastAcknowledgedBatchId]);return n.J(eP,r).next(n=>ec.forEach(n,n=>{N(n.userId===t.userId,18650,"Cannot process batch from unexpected user",{batchId:n.batchId});let r=iT(this.serializer,n);return i1(e,t.userId,r).next(()=>{})}))}))}fi(e){let t=e.store(eZ),n=e.store(eR);return e.store(e4).get(e5).next(e=>{let r=[];return n.te((n,i)=>{let s=new H(n),a=[0,eA(s)];r.push(t.get(a).next(n=>n?ec.resolve():t.put({targetId:0,path:eA(s),sequenceNumber:e.highestListenSequenceNumber})))}).next(()=>ec.waitFor(r))})}gi(e,t){e.createObjectStore(e3,{keyPath:e6});let n=t.store(e3),r=new iW,i=e=>{if(r.add(e)){let t=e.lastSegment(),r=e.popLast();return n.put({collectionId:t,parent:eA(r)})}};return t.store(eR).te({ee:!0},(e,t)=>i(new H(e).popLast())).next(()=>t.store(eB).te({ee:!0},([e,t,n],r)=>i(ek(t).popLast())))}pi(e){let t=e.store(eY);return t.te((e,n)=>{let r=iE(n),i=ib(this.serializer,r);return t.put(i)})}yi(e,t){let n=t.store(eR),r=[];return n.te((e,n)=>{let i=t.store(ez),s=(n.document?new X(H.fromString(n.document.name).popFirst(5)):n.noDocument?X.fromSegments(n.noDocument.path):n.unknownDocument?X.fromSegments(n.unknownDocument.path):S(36783)).path.toArray(),a={prefixPath:s.slice(0,s.length-2),collectionGroup:s[s.length-2],documentId:s[s.length-1],readTime:n.readTime||[0,0],unknownDocument:n.unknownDocument,noDocument:n.noDocument,document:n.document,hasCommittedMutations:!!n.hasCommittedMutations};r.push(i.put(a))}).next(()=>ec.waitFor(r))}wi(e,t){let n=t.store(eF),r=new so(this.serializer),i=new sb(sS.Ei,this.serializer.wt);return n.J().next(e=>{let n=new Map;return e.forEach(e=>{var t;let r=null!=(t=n.get(e.userId))?t:re();iT(this.serializer,e).keys().forEach(e=>r=r.add(e)),n.set(e.userId,r)}),ec.forEach(n,(e,n)=>{let s=new y(n),a=iD.bt(this.serializer,s),o=i.getIndexManager(s);return new sf(r,i5.bt(s,this.serializer,o,i.referenceDelegate),a,o).recalculateAndSaveOverlaysForDocumentKeys(new tE(t,ex.le),e).next()})})}}function sC(e){e.createObjectStore(eZ,{keyPath:e0}).createIndex(e1,e2,{unique:!0}),e.createObjectStore(eY,{keyPath:"targetId"}).createIndex(eJ,eX,{unique:!0}),e.createObjectStore(e4)}let sD="IndexedDbPersistence",sA="Failed to obtain exclusive access to the persistence layer. To allow shared access, multi-tab synchronization has to be enabled in all tabs. If you are using `experimentalForceOwningTab:true`, make sure that only one tab has persistence enabled at any given time.";class sk{constructor(e,t,n,r,i,s,a,o,l,u,h=17){if(this.allowTabSynchronization=e,this.persistenceKey=t,this.clientId=n,this.bi=i,this.window=s,this.document=a,this.Si=l,this.Di=u,this.Ci=h,this.si=null,this.oi=!1,this.isPrimary=!1,this.networkEnabled=!0,this.Fi=null,this.inForeground=!1,this.Mi=null,this.xi=null,this.Oi=Number.NEGATIVE_INFINITY,this.Ni=e=>Promise.resolve(),!sk.C())throw new D(C.UNIMPLEMENTED,"This platform is either missing IndexedDB or is known to have an incomplete implementation. Offline persistence has been disabled.");this.referenceDelegate=new si(this,r),this.Bi=t+"main",this.serializer=new ip(o),this.Li=new em(this.Bi,this.Ci,new sN(this.serializer)),this._i=new iA,this.ai=new i8(this.referenceDelegate,this.serializer),this.remoteDocumentCache=new so(this.serializer),this.ci=new iC,this.window&&this.window.localStorage?this.ki=this.window.localStorage:(this.ki=null,!1===u&&E(sD,"LocalStorage is unavailable. As a result, persistence may not work reliably. In particular enablePersistence() could fail immediately after refreshing the page."))}start(){return this.qi().then(()=>{if(!this.isPrimary&&!this.allowTabSynchronization)throw new D(C.FAILED_PRECONDITION,sA);return this.Qi(),this.$i(),this.Ui(),this.runTransaction("getHighestListenSequenceNumber","readonly",e=>this.ai.getHighestSequenceNumber(e))}).then(e=>{this.si=new ex(e,this.Si)}).then(()=>{this.oi=!0}).catch(e=>(this.Li&&this.Li.close(),Promise.reject(e)))}Ki(e){return this.Ni=async t=>{if(this.started)return e(t)},e(this.isPrimary)}setDatabaseDeletedListener(e){this.Li.U(async t=>{null===t.newVersion&&await e()})}setNetworkEnabled(e){this.networkEnabled!==e&&(this.networkEnabled=e,this.bi.enqueueAndForget(async()=>{this.started&&await this.qi()}))}qi(){return this.runTransaction("updateClientMetadataAndTryBecomePrimary","readwrite",e=>tb(e,e8).put({clientId:this.clientId,updateTimeMs:Date.now(),networkEnabled:this.networkEnabled,inForeground:this.inForeground}).next(()=>{if(this.isPrimary)return this.Wi(e).next(e=>{e||(this.isPrimary=!1,this.bi.enqueueRetryable(()=>this.Ni(!1)))})}).next(()=>this.Gi(e)).next(t=>this.isPrimary&&!t?this.zi(e).next(()=>!1):!!t&&this.ji(e).next(()=>!0))).catch(e=>{if(ew(e))return T(sD,"Failed to extend owner lease: ",e),this.isPrimary;if(!this.allowTabSynchronization)throw e;return T(sD,"Releasing owner lease after error during lease refresh",e),!1}).then(e=>{this.isPrimary!==e&&this.bi.enqueueRetryable(()=>this.Ni(e)),this.isPrimary=e})}Wi(e){return tb(e,eV).get(eM).next(e=>ec.resolve(this.Hi(e)))}Ji(e){return tb(e,e8).delete(this.clientId)}async Yi(){if(this.isPrimary&&!this.Zi(this.Oi,18e5)){this.Oi=Date.now();let e=await this.runTransaction("maybeGarbageCollectMultiClientState","readwrite-primary",e=>{let t=tb(e,e8);return t.J().next(e=>{let n=this.Xi(e,18e5),r=e.filter(e=>-1===n.indexOf(e));return ec.forEach(r,e=>t.delete(e.clientId)).next(()=>r)})}).catch(()=>[]);if(this.ki)for(let t of e)this.ki.removeItem(this.es(t.clientId))}}Ui(){this.xi=this.bi.enqueueAfterDelay("client_metadata_refresh",4e3,()=>this.qi().then(()=>this.Yi()).then(()=>this.Ui()))}Hi(e){return!!e&&e.ownerId===this.clientId}Gi(e){return this.Di?ec.resolve(!0):tb(e,eV).get(eM).next(t=>{if(null!==t&&this.Zi(t.leaseTimestampMs,5e3)&&!this.ts(t.ownerId)){if(this.Hi(t)&&this.networkEnabled)return!0;if(!this.Hi(t)){if(!t.allowTabSynchronization)throw new D(C.FAILED_PRECONDITION,sA);return!1}}return!(!this.networkEnabled||!this.inForeground)||tb(e,e8).J().next(e=>void 0===this.Xi(e,5e3).find(e=>{if(this.clientId!==e.clientId){let t=!this.networkEnabled&&e.networkEnabled,n=!this.inForeground&&e.inForeground,r=this.networkEnabled===e.networkEnabled;if(t||n&&r)return!0}return!1}))}).next(e=>(this.isPrimary!==e&&T(sD,`Client ${e?"is":"is not"} eligible for a primary lease.`),e))}async shutdown(){this.oi=!1,this.ns(),this.xi&&(this.xi.cancel(),this.xi=null),this.rs(),this.ss(),await this.Li.runTransaction("shutdown","readwrite",[eV,e8],e=>{let t=new tE(e,ex.le);return this.zi(t).next(()=>this.Ji(t))}),this.Li.close(),this._s()}Xi(e,t){return e.filter(e=>this.Zi(e.updateTimeMs,t)&&!this.ts(e.clientId))}us(){return this.runTransaction("getActiveClients","readonly",e=>tb(e,e8).J().next(e=>this.Xi(e,18e5).map(e=>e.clientId)))}get started(){return this.oi}getGlobalsCache(){return this._i}getMutationQueue(e,t){return i5.bt(e,this.serializer,t,this.referenceDelegate)}getTargetCache(){return this.ai}getRemoteDocumentCache(){return this.remoteDocumentCache}getIndexManager(e){return new iJ(e,this.serializer.wt.databaseId)}getDocumentOverlayCache(e){return iD.bt(this.serializer,e)}getBundleCache(){return this.ci}runTransaction(e,t,n){var r;let i;T(sD,"Starting transaction:",e);let s=17===(r=this.Ci)?tT:16===r||15===r?tI:14===r||13===r?tv:12===r?tw:11===r?ty:void S(60245);return this.Li.runTransaction(e,"readonly"===t?"readonly":"readwrite",s,r=>(i=new tE(r,this.si?this.si.next():ex.le),"readwrite-primary"===t?this.Wi(i).next(e=>!!e||this.Gi(i)).next(t=>{if(!t)throw E(`Failed to obtain primary lease for action '${e}'.`),this.isPrimary=!1,this.bi.enqueueRetryable(()=>this.Ni(!1)),new D(C.FAILED_PRECONDITION,el);return n(i)}).next(e=>this.ji(i).next(()=>e)):this.cs(i).next(()=>n(i)))).then(e=>(i.raiseOnCommittedEvent(),e))}cs(e){return tb(e,eV).get(eM).next(e=>{if(null!==e&&this.Zi(e.leaseTimestampMs,5e3)&&!this.ts(e.ownerId)&&!this.Hi(e)&&!(this.Di||this.allowTabSynchronization&&e.allowTabSynchronization))throw new D(C.FAILED_PRECONDITION,sA)})}ji(e){let t={ownerId:this.clientId,allowTabSynchronization:this.allowTabSynchronization,leaseTimestampMs:Date.now()};return tb(e,eV).put(eM,t)}static C(){return em.C()}zi(e){let t=tb(e,eV);return t.get(eM).next(e=>this.Hi(e)?(T(sD,"Releasing primary lease."),t.delete(eM)):ec.resolve())}Zi(e,t){let n=Date.now();return!(e<n-t)&&(!(e>n)||(E(`Detected an update time that is in the future: ${e} > ${n}`),!1))}Qi(){null!==this.document&&"function"==typeof this.document.addEventListener&&(this.Mi=()=>{this.bi.enqueueAndForget(()=>(this.inForeground="visible"===this.document.visibilityState,this.qi()))},this.document.addEventListener("visibilitychange",this.Mi),this.inForeground="visible"===this.document.visibilityState)}rs(){this.Mi&&(this.document.removeEventListener("visibilitychange",this.Mi),this.Mi=null)}$i(){var e;"function"==typeof(null==(e=this.window)?void 0:e.addEventListener)&&(this.Fi=()=>{this.ns();let e=/(?:Version|Mobile)\/1[456]/;(0,h.nr)()&&(navigator.appVersion.match(e)||navigator.userAgent.match(e))&&this.bi.enterRestrictedMode(!0),this.bi.enqueueAndForget(()=>this.shutdown())},this.window.addEventListener("pagehide",this.Fi))}ss(){this.Fi&&(this.window.removeEventListener("pagehide",this.Fi),this.Fi=null)}ts(e){var t;try{let n=null!==(null==(t=this.ki)?void 0:t.getItem(this.es(e)));return T(sD,`Client '${e}' ${n?"is":"is not"} zombied in LocalStorage`),n}catch(e){return E(sD,"Failed to get zombied client id.",e),!1}}ns(){if(this.ki)try{this.ki.setItem(this.es(this.clientId),String(Date.now()))}catch(e){E("Failed to set zombie client id.",e)}}_s(){if(this.ki)try{this.ki.removeItem(this.es(this.clientId))}catch(e){}}es(e){return`firestore_zombie_${this.persistenceKey}_${e}`}}function sR(e,t){let n=e.projectId;return e.isDefaultDatabase||(n+="."+e.database),"firestore/"+t+"/"+n+"/"}class sV{constructor(e,t,n,r){this.targetId=e,this.fromCache=t,this.ls=n,this.hs=r}static Ps(e,t){let n=re(),r=re();for(let e of t.docChanges)switch(e.type){case 0:n=n.add(e.doc.key);break;case 1:r=r.add(e.doc.key)}return new sV(e,t.fromCache,n,r)}}class sM{constructor(){this._documentReadCount=0}get documentReadCount(){return this._documentReadCount}incrementDocumentReadCount(e){this._documentReadCount+=e}}class sO{constructor(){this.Ts=!1,this.Is=!1,this.Es=100,this.ds=(0,h.nr)()?8:eg((0,h.ZQ)())>0?6:4}initialize(e,t){this.As=e,this.indexManager=t,this.Ts=!0}getDocumentsMatchingQuery(e,t,n,r){let i={result:null};return this.Rs(e,t).next(e=>{i.result=e}).next(()=>{if(!i.result)return this.Vs(e,t,r,n).next(e=>{i.result=e})}).next(()=>{if(i.result)return;let n=new sM;return this.fs(e,t,n).next(r=>{if(i.result=r,this.Is)return this.gs(e,t,n,r.size)})}).next(()=>i.result)}gs(e,t,n,r){return n.documentReadCount<this.Es?(I()<=u.$b.DEBUG&&T("QueryEngine","SDK will not create cache indexes for query:",nX(t),"since it only creates cache indexes for collection contains","more than or equal to",this.Es,"documents"),ec.resolve()):(I()<=u.$b.DEBUG&&T("QueryEngine","Query:",nX(t),"scans",n.documentReadCount,"local documents and returns",r,"documents as results."),n.documentReadCount>this.ds*r?(I()<=u.$b.DEBUG&&T("QueryEngine","The SDK decides to create cache indexes for query:",nX(t),"as using cache indexes may help improve performance."),this.indexManager.createTargetIndexes(e,nj(t))):ec.resolve())}Rs(e,t){if(nK(t))return ec.resolve(null);let n=nj(t);return this.indexManager.getIndexType(e,n).next(r=>0===r?null:(null!==t.limit&&1===r&&(n=nj(t=nH(t,null,"F"))),this.indexManager.getDocumentsMatchingTarget(e,n).next(r=>{let i=re(...r);return this.As.getDocuments(e,i).next(r=>this.indexManager.getMinOffset(e,n).next(n=>{let s=this.ps(t,r);return this.ys(t,s,i,n.readTime)?this.Rs(e,nH(t,null,"F")):this.ws(e,s,t,n)}))})))}Vs(e,t,n,r){return nK(t)||r.isEqual(j.min())?ec.resolve(null):this.As.getDocuments(e,n).next(i=>{let s=this.ps(t,i);return this.ys(t,s,n,r)?ec.resolve(null):(I()<=u.$b.DEBUG&&T("QueryEngine","Re-using previous result from %s to execute query: %s",r.toString(),nX(t)),this.ws(e,s,t,ei(r,-1)).next(e=>e))})}ps(e,t){let n=new tA(n1(e));return t.forEach((t,r)=>{nZ(e,r)&&(n=n.add(r))}),n}ys(e,t,n,r){if(null===e.limit)return!1;if(n.size!==t.size)return!0;let i="F"===e.limitType?t.last():t.first();return!!i&&(i.hasPendingWrites||i.version.compareTo(r)>0)}fs(e,t,n){return I()<=u.$b.DEBUG&&T("QueryEngine","Using full collection scan to execute query:",nX(t)),this.As.getDocumentsMatchingQuery(e,t,ea.min(),n)}ws(e,t,n,r){return this.As.getDocumentsMatchingQuery(e,n,r).next(e=>(t.forEach(t=>{e=e.insert(t.key,t)}),e))}}let sF="LocalStore";class sL{constructor(e,t,n,r){this.persistence=e,this.bs=t,this.serializer=r,this.Ss=new tN(B),this.Ds=new n2(e=>nO(e),nF),this.vs=new Map,this.Cs=e.getRemoteDocumentCache(),this.ai=e.getTargetCache(),this.ci=e.getBundleCache(),this.Fs(n)}Fs(e){this.documentOverlayCache=this.persistence.getDocumentOverlayCache(e),this.indexManager=this.persistence.getIndexManager(e),this.mutationQueue=this.persistence.getMutationQueue(e,this.indexManager),this.localDocuments=new sf(this.Cs,this.mutationQueue,this.documentOverlayCache,this.indexManager),this.Cs.setIndexManager(this.indexManager),this.bs.initialize(this.localDocuments,this.indexManager)}collectGarbage(e){return this.persistence.runTransaction("Collect garbage","readwrite-primary",t=>e.collect(t,this.Ss))}}async function sP(e,t){return await e.persistence.runTransaction("Handle user change","readonly",n=>{let r;return e.mutationQueue.getAllMutationBatches(n).next(i=>(r=i,e.Fs(t),e.mutationQueue.getAllMutationBatches(n))).next(t=>{let i=[],s=[],a=re();for(let e of r)for(let t of(i.push(e.batchId),e.mutations))a=a.add(t.key);for(let e of t)for(let t of(s.push(e.batchId),e.mutations))a=a.add(t.key);return e.localDocuments.getDocuments(n,a).next(e=>({Ms:e,removedBatchIds:i,addedBatchIds:s}))})})}function sU(e){return e.persistence.runTransaction("Get last remote snapshot version","readonly",t=>e.ai.getLastRemoteSnapshotVersion(t))}function sq(e,t,n){let r=re(),i=re();return n.forEach(e=>r=r.add(e)),t.getEntries(e,r).next(e=>{let r=n5;return n.forEach((n,s)=>{let a=e.get(n);s.isFoundDocument()!==a.isFoundDocument()&&(i=i.add(n)),s.isNoDocument()&&s.version.isEqual(j.min())?(t.removeEntry(n,s.readTime),r=r.insert(n,s)):!a.isValidDocument()||s.version.compareTo(a.version)>0||0===s.version.compareTo(a.version)&&a.hasPendingWrites?(t.addEntry(s),r=r.insert(n,s)):T(sF,"Ignoring outdated watch update for ",n,". Current version:",a.version," Watch version:",s.version)}),{xs:r,Os:i}})}function sB(e,t){return e.persistence.runTransaction("Allocate target","readwrite",n=>{let r;return e.ai.getTargetData(n,t).next(i=>i?(r=i,ec.resolve(r)):e.ai.allocateTargetId(n).next(i=>(r=new ig(t,i,"TargetPurposeListen",n.currentSequenceNumber),e.ai.addTargetData(n,r).next(()=>r))))}).then(n=>{let r=e.Ss.get(n.targetId);return(null===r||n.snapshotVersion.compareTo(r.snapshotVersion)>0)&&(e.Ss=e.Ss.insert(n.targetId,n),e.Ds.set(t,n.targetId)),n})}async function sz(e,t,n){let r=e.Ss.get(t);try{n||await e.persistence.runTransaction("Release target",n?"readwrite":"readwrite-primary",t=>e.persistence.referenceDelegate.removeTarget(t,r))}catch(e){if(!ew(e))throw e;T(sF,`Failed to update sequence numbers for target ${t}: ${e}`)}e.Ss=e.Ss.remove(t),e.Ds.delete(r.target)}function sK(e,t,n){let r=j.min(),i=re();return e.persistence.runTransaction("Execute query","readwrite",s=>(function(e,t,n){let r=e.Ds.get(n);return void 0!==r?ec.resolve(e.Ss.get(r)):e.ai.getTargetData(t,n)})(e,s,nj(t)).next(t=>{if(t)return r=t.lastLimboFreeSnapshotVersion,e.ai.getMatchingKeysForTargetId(s,t.targetId).next(e=>{i=e})}).next(()=>e.bs.getDocumentsMatchingQuery(s,t,n?r:j.min(),n?i:re())).next(n=>(sj(e,n0(t),n),{documents:n,Ns:i})))}function s$(e,t){let n=e.ai,r=e.Ss.get(t);return r?Promise.resolve(r.target):e.persistence.runTransaction("Get target data","readonly",e=>n.Rt(e,t).next(e=>e?e.target:null))}function sG(e,t){let n=e.vs.get(t)||j.min();return e.persistence.runTransaction("Get new document changes","readonly",r=>e.Cs.getAllFromCollectionGroup(r,t,ei(n,-1),Number.MAX_SAFE_INTEGER)).then(n=>(sj(e,t,n),n))}function sj(e,t,n){let r=e.vs.get(t)||j.min();n.forEach((e,t)=>{t.readTime.compareTo(r)>0&&(r=t.readTime)}),e.vs.set(t,r)}async function sQ(e,t,n,r){let i=re(),s=n5;for(let e of n){let n=t.Bs(e.metadata.name);e.document&&(i=i.add(n));let r=t.Ls(e);r.setReadTime(t.ks(e.metadata.readTime)),s=s.insert(n,r)}let a=e.Cs.newChangeBuffer({trackRemovals:!0}),o=await sB(e,nj(nz(H.fromString(`__bundle__/docs/${r}`))));return e.persistence.runTransaction("Apply bundle documents","readwrite",t=>sq(t,a,s).next(e=>(a.apply(t),e)).next(n=>e.ai.removeMatchingKeysForTargetId(t,o.targetId).next(()=>e.ai.addMatchingKeys(t,i,o.targetId)).next(()=>e.localDocuments.getLocalViewOfDocuments(t,n.xs,n.Os)).next(()=>n.xs)))}async function sW(e,t,n=re()){let r=await sB(e,nj(i_(t.bundledQuery)));return e.persistence.runTransaction("Save named query","readwrite",i=>{let s=r5(t.readTime);if(r.snapshotVersion.compareTo(s)>=0)return e.ci.saveNamedQuery(i,t);let a=r.withResumeToken(tO.EMPTY_BYTE_STRING,s);return e.Ss=e.Ss.insert(a.targetId,a),e.ai.updateTargetData(i,a).next(()=>e.ai.removeMatchingKeysForTargetId(i,r.targetId)).next(()=>e.ai.addMatchingKeys(i,n,r.targetId)).next(()=>e.ci.saveNamedQuery(i,t))})}let sH="firestore_clients";function sY(e,t){return`${sH}_${e}_${t}`}let sJ="firestore_mutations";function sX(e,t,n){let r=`${sJ}_${e}_${n}`;return t.isAuthenticated()&&(r+=`_${t.uid}`),r}let sZ="firestore_targets";function s0(e,t){return`${sZ}_${e}_${t}`}let s1="SharedClientState";class s2{constructor(e,t,n,r){this.user=e,this.batchId=t,this.state=n,this.error=r}static qs(e,t,n){let r=JSON.parse(n),i,s="object"==typeof r&&-1!==["pending","acknowledged","rejected"].indexOf(r.state)&&(void 0===r.error||"object"==typeof r.error);return s&&r.error&&(s="string"==typeof r.error.message&&"string"==typeof r.error.code)&&(i=new D(r.error.code,r.error.message)),s?new s2(e,t,r.state,i):(E(s1,`Failed to parse mutation state for ID '${t}': ${n}`),null)}Qs(){let e={state:this.state,updateTimeMs:Date.now()};return this.error&&(e.error={code:this.error.code,message:this.error.message}),JSON.stringify(e)}}class s5{constructor(e,t,n){this.targetId=e,this.state=t,this.error=n}static qs(e,t){let n=JSON.parse(t),r,i="object"==typeof n&&-1!==["not-current","current","rejected"].indexOf(n.state)&&(void 0===n.error||"object"==typeof n.error);return i&&n.error&&(i="string"==typeof n.error.message&&"string"==typeof n.error.code)&&(r=new D(n.error.code,n.error.message)),i?new s5(e,n.state,r):(E(s1,`Failed to parse target state for ID '${e}': ${t}`),null)}Qs(){let e={state:this.state,updateTimeMs:Date.now()};return this.error&&(e.error={code:this.error.code,message:this.error.message}),JSON.stringify(e)}}class s4{constructor(e,t){this.clientId=e,this.activeTargetIds=t}static qs(e,t){let n=JSON.parse(t),r="object"==typeof n&&n.activeTargetIds instanceof Array,i=rt;for(let e=0;r&&e<n.activeTargetIds.length;++e)r=eD(n.activeTargetIds[e]),i=i.add(n.activeTargetIds[e]);return r?new s4(e,i):(E(s1,`Failed to parse client data for instance '${e}': ${t}`),null)}}class s3{constructor(e,t){this.clientId=e,this.onlineState=t}static qs(e){let t=JSON.parse(e);return"object"==typeof t&&-1!==["Unknown","Online","Offline"].indexOf(t.onlineState)&&"string"==typeof t.clientId?new s3(t.clientId,t.onlineState):(E(s1,`Failed to parse online state: ${e}`),null)}}class s6{constructor(){this.activeTargetIds=rt}$s(e){this.activeTargetIds=this.activeTargetIds.add(e)}Us(e){this.activeTargetIds=this.activeTargetIds.delete(e)}Qs(){return JSON.stringify({activeTargetIds:this.activeTargetIds.toArray(),updateTimeMs:Date.now()})}}class s8{constructor(e,t,n,r,i){var s,a,o;this.window=e,this.bi=t,this.persistenceKey=n,this.Ks=r,this.syncEngine=null,this.onlineStateHandler=null,this.sequenceNumberHandler=null,this.Ws=this.Gs.bind(this),this.zs=new tN(B),this.started=!1,this.js=[];let l=n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");this.storage=this.window.localStorage,this.currentUser=i,this.Hs=sY(this.persistenceKey,this.Ks),this.Js=(s=this.persistenceKey,`firestore_sequence_number_${s}`),this.zs=this.zs.insert(this.Ks,new s6),this.Ys=RegExp(`^${sH}_${l}_([^_]*)$`),this.Zs=RegExp(`^${sJ}_${l}_(\\d+)(?:_(.*))?$`),this.Xs=RegExp(`^${sZ}_${l}_(\\d+)$`),this.eo=(a=this.persistenceKey,`firestore_online_state_${a}`),this.no=(o=this.persistenceKey,`firestore_bundle_loaded_v2_${o}`),this.window.addEventListener("storage",this.Ws)}static C(e){return!(!e||!e.localStorage)}async start(){for(let e of(await this.syncEngine.us())){if(e===this.Ks)continue;let t=this.getItem(sY(this.persistenceKey,e));if(t){let n=s4.qs(e,t);n&&(this.zs=this.zs.insert(n.clientId,n))}}this.ro();let e=this.storage.getItem(this.eo);if(e){let t=this.io(e);t&&this.so(t)}for(let e of this.js)this.Gs(e);this.js=[],this.window.addEventListener("pagehide",()=>this.shutdown()),this.started=!0}writeSequenceNumber(e){this.setItem(this.Js,JSON.stringify(e))}getAllActiveQueryTargets(){return this.oo(this.zs)}isActiveQueryTarget(e){let t=!1;return this.zs.forEach((n,r)=>{r.activeTargetIds.has(e)&&(t=!0)}),t}addPendingMutation(e){this._o(e,"pending")}updateMutationState(e,t,n){this._o(e,t,n),this.ao(e)}addLocalQueryTarget(e,t=!0){let n="not-current";if(this.isActiveQueryTarget(e)){let t=this.storage.getItem(s0(this.persistenceKey,e));if(t){let r=s5.qs(e,t);r&&(n=r.state)}}return t&&this.uo.$s(e),this.ro(),n}removeLocalQueryTarget(e){this.uo.Us(e),this.ro()}isLocalQueryTarget(e){return this.uo.activeTargetIds.has(e)}clearQueryState(e){this.removeItem(s0(this.persistenceKey,e))}updateQueryState(e,t,n){this.co(e,t,n)}handleUserChange(e,t,n){t.forEach(e=>{this.ao(e)}),this.currentUser=e,n.forEach(e=>{this.addPendingMutation(e)})}setOnlineState(e){this.lo(e)}notifyBundleLoaded(e){this.ho(e)}shutdown(){this.started&&(this.window.removeEventListener("storage",this.Ws),this.removeItem(this.Hs),this.started=!1)}getItem(e){let t=this.storage.getItem(e);return T(s1,"READ",e,t),t}setItem(e,t){T(s1,"SET",e,t),this.storage.setItem(e,t)}removeItem(e){T(s1,"REMOVE",e),this.storage.removeItem(e)}Gs(e){if(e.storageArea===this.storage){if(T(s1,"EVENT",e.key,e.newValue),e.key===this.Hs)return void E("Received WebStorage notification for local change. Another client might have garbage-collected our state");this.bi.enqueueRetryable(async()=>{if(this.started){if(null!==e.key){if(this.Ys.test(e.key)){if(null==e.newValue){let t=this.Po(e.key);return this.To(t,null)}{let t=this.Io(e.key,e.newValue);if(t)return this.To(t.clientId,t)}}else if(this.Zs.test(e.key)){if(null!==e.newValue){let t=this.Eo(e.key,e.newValue);if(t)return this.Ao(t)}}else if(this.Xs.test(e.key)){if(null!==e.newValue){let t=this.Ro(e.key,e.newValue);if(t)return this.Vo(t)}}else if(e.key===this.eo){if(null!==e.newValue){let t=this.io(e.newValue);if(t)return this.so(t)}}else if(e.key===this.Js){let t=function(e){let t=ex.le;if(null!=e)try{let n=JSON.parse(e);N("number"==typeof n,30636,{mo:e}),t=n}catch(e){E(s1,"Failed to read sequence number from WebStorage",e)}return t}(e.newValue);t!==ex.le&&this.sequenceNumberHandler(t)}else if(e.key===this.no){let t=this.fo(e.newValue);await Promise.all(t.map(e=>this.syncEngine.po(e)))}}}else this.js.push(e)})}}get uo(){return this.zs.get(this.Ks)}ro(){this.setItem(this.Hs,this.uo.Qs())}_o(e,t,n){let r=new s2(this.currentUser,e,t,n),i=sX(this.persistenceKey,this.currentUser,e);this.setItem(i,r.Qs())}ao(e){let t=sX(this.persistenceKey,this.currentUser,e);this.removeItem(t)}lo(e){let t={clientId:this.Ks,onlineState:e};this.storage.setItem(this.eo,JSON.stringify(t))}co(e,t,n){let r=s0(this.persistenceKey,e),i=new s5(e,t,n);this.setItem(r,i.Qs())}ho(e){let t=JSON.stringify(Array.from(e));this.setItem(this.no,t)}Po(e){let t=this.Ys.exec(e);return t?t[1]:null}Io(e,t){let n=this.Po(e);return s4.qs(n,t)}Eo(e,t){let n=this.Zs.exec(e),r=Number(n[1]),i=void 0!==n[2]?n[2]:null;return s2.qs(new y(i),r,t)}Ro(e,t){let n=Number(this.Xs.exec(e)[1]);return s5.qs(n,t)}io(e){return s3.qs(e)}fo(e){return JSON.parse(e)}async Ao(e){if(e.user.uid===this.currentUser.uid)return this.syncEngine.yo(e.batchId,e.state,e.error);T(s1,`Ignoring mutation for non-active user ${e.user.uid}`)}Vo(e){return this.syncEngine.wo(e.targetId,e.state,e.error)}To(e,t){let n=t?this.zs.insert(e,t):this.zs.remove(e),r=this.oo(this.zs),i=this.oo(n),s=[],a=[];return i.forEach(e=>{r.has(e)||s.push(e)}),r.forEach(e=>{i.has(e)||a.push(e)}),this.syncEngine.bo(s,a).then(()=>{this.zs=n})}so(e){this.zs.get(e.clientId)&&this.onlineStateHandler(e.onlineState)}oo(e){let t=rt;return e.forEach((e,n)=>{t=t.unionWith(n.activeTargetIds)}),t}}class s9{constructor(){this.So=new s6,this.Do={},this.onlineStateHandler=null,this.sequenceNumberHandler=null}addPendingMutation(e){}updateMutationState(e,t,n){}addLocalQueryTarget(e,t=!0){return t&&this.So.$s(e),this.Do[e]||"not-current"}updateQueryState(e,t,n){this.Do[e]=t}removeLocalQueryTarget(e){this.So.Us(e)}isLocalQueryTarget(e){return this.So.activeTargetIds.has(e)}clearQueryState(e){delete this.Do[e]}getAllActiveQueryTargets(){return this.So.activeTargetIds}isActiveQueryTarget(e){return this.So.activeTargetIds.has(e)}start(){return this.So=new s6,Promise.resolve()}handleUserChange(e,t,n){}setOnlineState(e){}shutdown(){}writeSequenceNumber(e){}notifyBundleLoaded(e){}}class s7{vo(e){}shutdown(){}}let ae="ConnectivityMonitor";class at{constructor(){this.Co=()=>this.Fo(),this.Mo=()=>this.xo(),this.Oo=[],this.No()}vo(e){this.Oo.push(e)}shutdown(){window.removeEventListener("online",this.Co),window.removeEventListener("offline",this.Mo)}No(){window.addEventListener("online",this.Co),window.addEventListener("offline",this.Mo)}Fo(){for(let e of(T(ae,"Network connectivity changed: AVAILABLE"),this.Oo))e(0)}xo(){for(let e of(T(ae,"Network connectivity changed: UNAVAILABLE"),this.Oo))e(1)}static C(){return"undefined"!=typeof window&&void 0!==window.addEventListener&&void 0!==window.removeEventListener}}let an=null;function ar(){return null===an?an=0x10000000+Math.round(0x80000000*Math.random()):an++,"0x"+an.toString(16)}let ai="RestConnection",as={BatchGetDocuments:"batchGet",Commit:"commit",RunQuery:"runQuery",RunAggregationQuery:"runAggregationQuery"};class aa{get Bo(){return!1}constructor(e){this.databaseInfo=e,this.databaseId=e.databaseId;let t=e.ssl?"https":"http",n=encodeURIComponent(this.databaseId.projectId),r=encodeURIComponent(this.databaseId.database);this.Lo=t+"://"+e.host,this.ko=`projects/${n}/databases/${r}`,this.qo=this.databaseId.database===tW?`project_id=${n}`:`project_id=${n}&database_id=${r}`}Qo(e,t,n,r,i){let s=ar(),a=this.$o(e,t.toUriEncodedString());T(ai,`Sending RPC '${e}' ${s}:`,a,n);let o={"google-cloud-resource-prefix":this.ko,"x-goog-request-params":this.qo};return this.Uo(o,r,i),this.Ko(e,a,o,n).then(t=>(T(ai,`Received RPC '${e}' ${s}: `,t),t),t=>{throw b(ai,`RPC '${e}' ${s} failed with error: `,t,"url: ",a,"request:",n),t})}Wo(e,t,n,r,i,s){return this.Qo(e,t,n,r,i)}Uo(e,t,n){e["X-Goog-Api-Client"]="gl-js/ fire/"+w,e["Content-Type"]="text/plain",this.databaseInfo.appId&&(e["X-Firebase-GMPID"]=this.databaseInfo.appId),t&&t.headers.forEach((t,n)=>e[n]=t),n&&n.headers.forEach((t,n)=>e[n]=t)}$o(e,t){let n=as[e];return`${this.Lo}/v1/${t}:${n}`}terminate(){}}class ao{constructor(e){this.Go=e.Go,this.zo=e.zo}jo(e){this.Ho=e}Jo(e){this.Yo=e}Zo(e){this.Xo=e}onMessage(e){this.e_=e}close(){this.zo()}send(e){this.Go(e)}t_(){this.Ho()}n_(){this.Yo()}r_(e){this.Xo(e)}i_(e){this.e_(e)}}let al="WebChannelConnection";class au extends aa{constructor(e){super(e),this.forceLongPolling=e.forceLongPolling,this.autoDetectLongPolling=e.autoDetectLongPolling,this.useFetchStreams=e.useFetchStreams,this.longPollingOptions=e.longPollingOptions}Ko(e,t,n,r){let i=ar();return new Promise((s,a)=>{let o=new d.ZS;o.setWithCredentials(!0),o.listenOnce(d.Bx.COMPLETE,()=>{try{switch(o.getLastErrorCode()){case d.O4.NO_ERROR:let t=o.getResponseJson();T(al,`XHR for RPC '${e}' ${i} received:`,JSON.stringify(t)),s(t);break;case d.O4.TIMEOUT:T(al,`RPC '${e}' ${i} timed out`),a(new D(C.DEADLINE_EXCEEDED,"Request time out"));break;case d.O4.HTTP_ERROR:let n=o.getStatus();if(T(al,`RPC '${e}' ${i} failed with status:`,n,"response text:",o.getResponseText()),n>0){let e=o.getResponseJson();Array.isArray(e)&&(e=e[0]);let t=null==e?void 0:e.error;if(t&&t.status&&t.message){let e=function(e){let t=e.toLowerCase().replace(/_/g,"-");return Object.values(C).indexOf(t)>=0?t:C.UNKNOWN}(t.status);a(new D(e,t.message))}else a(new D(C.UNKNOWN,"Server responded with status "+o.getStatus()))}else a(new D(C.UNAVAILABLE,"Connection failed."));break;default:S(9055,{s_:e,streamId:i,o_:o.getLastErrorCode(),__:o.getLastError()})}}finally{T(al,`RPC '${e}' ${i} completed.`)}});let l=JSON.stringify(r);T(al,`RPC '${e}' ${i} sending request:`,r),o.send(t,"POST",l,n,15)})}a_(e,t,n){let i=ar(),s=[this.Lo,"/","google.firestore.v1.Firestore","/",e,"/channel"],a=(0,d.fF)(),o=(0,d.Ao)(),l={httpSessionIdParam:"gsessionid",initMessageHeaders:{},messageUrlParams:{database:`projects/${this.databaseId.projectId}/databases/${this.databaseId.database}`},sendRawJson:!0,supportsCrossDomainXhr:!0,internalChannelParams:{forwardChannelRequestTimeoutMs:6e5},forceLongPolling:this.forceLongPolling,detectBufferingProxy:this.autoDetectLongPolling},u=this.longPollingOptions.timeoutSeconds;void 0!==u&&(l.longPollingTimeout=Math.round(1e3*u)),this.useFetchStreams&&(l.useFetchStreams=!0),this.Uo(l.initMessageHeaders,t,n),l.encodeInitMessageHeaders=!0;let h=s.join("");T(al,`Creating RPC '${e}' stream ${i}: ${h}`,l);let c=a.createWebChannel(h,l),f=!1,m=!1,g=new ao({Go:t=>{m?T(al,`Not sending because RPC '${e}' stream ${i} is closed:`,t):(f||(T(al,`Opening RPC '${e}' stream ${i} transport.`),c.open(),f=!0),T(al,`RPC '${e}' stream ${i} sending:`,t),c.send(t))},zo:()=>c.close()}),p=(e,t,n)=>{e.listen(t,e=>{try{n(e)}catch(e){setTimeout(()=>{throw e},0)}})};return p(c,d.iO.EventType.OPEN,()=>{m||(T(al,`RPC '${e}' stream ${i} transport opened.`),g.t_())}),p(c,d.iO.EventType.CLOSE,()=>{m||(m=!0,T(al,`RPC '${e}' stream ${i} transport closed`),g.r_())}),p(c,d.iO.EventType.ERROR,t=>{m||(m=!0,b(al,`RPC '${e}' stream ${i} transport errored. Name:`,t.name,"Message:",t.message),g.r_(new D(C.UNAVAILABLE,"The operation could not be completed")))}),p(c,d.iO.EventType.MESSAGE,t=>{var n;if(!m){let s=t.data[0];N(!!s,16349);let a=(null==s?void 0:s.error)||(null==(n=s[0])?void 0:n.error);if(a){T(al,`RPC '${e}' stream ${i} received error:`,a);let t=a.status,n=function(e){let t=r[e];if(void 0!==t)return rO(t)}(t),s=a.message;void 0===n&&(n=C.INTERNAL,s="Unknown error status: "+t+" with message "+a.message),m=!0,g.r_(new D(n,s)),c.close()}else T(al,`RPC '${e}' stream ${i} received:`,s),g.i_(s)}}),p(o,d.Jh.STAT_EVENT,t=>{t.stat===d.ro.PROXY?T(al,`RPC '${e}' stream ${i} detected buffering proxy`):t.stat===d.ro.NOPROXY&&T(al,`RPC '${e}' stream ${i} detected no buffering proxy`)}),setTimeout(()=>{g.n_()},0),g}}function ah(){return"undefined"!=typeof window?window:null}function ac(){return"undefined"!=typeof document?document:null}function ad(e){return new rZ(e,!0)}class af{constructor(e,t,n=1e3,r=1.5,i=6e4){this.bi=e,this.timerId=t,this.u_=n,this.c_=r,this.l_=i,this.h_=0,this.P_=null,this.T_=Date.now(),this.reset()}reset(){this.h_=0}I_(){this.h_=this.l_}E_(e){this.cancel();let t=Math.floor(this.h_+this.d_()),n=Math.max(0,Date.now()-this.T_),r=Math.max(0,t-n);r>0&&T("ExponentialBackoff",`Backing off for ${r} ms (base delay: ${this.h_} ms, delay with jitter: ${t} ms, last attempt: ${n} ms ago)`),this.P_=this.bi.enqueueAfterDelay(this.timerId,r,()=>(this.T_=Date.now(),e())),this.h_*=this.c_,this.h_<this.u_&&(this.h_=this.u_),this.h_>this.l_&&(this.h_=this.l_)}A_(){null!==this.P_&&(this.P_.skipDelay(),this.P_=null)}cancel(){null!==this.P_&&(this.P_.cancel(),this.P_=null)}d_(){return(Math.random()-.5)*this.h_}}let am="PersistentStream";class ag{constructor(e,t,n,r,i,s,a,o){this.bi=e,this.R_=n,this.V_=r,this.connection=i,this.authCredentialsProvider=s,this.appCheckCredentialsProvider=a,this.listener=o,this.state=0,this.m_=0,this.f_=null,this.g_=null,this.stream=null,this.p_=0,this.y_=new af(e,t)}w_(){return 1===this.state||5===this.state||this.b_()}b_(){return 2===this.state||3===this.state}start(){this.p_=0,4!==this.state?this.auth():this.S_()}async stop(){this.w_()&&await this.close(0)}D_(){this.state=0,this.y_.reset()}v_(){this.b_()&&null===this.f_&&(this.f_=this.bi.enqueueAfterDelay(this.R_,6e4,()=>this.C_()))}F_(e){this.M_(),this.stream.send(e)}async C_(){if(this.b_())return this.close(0)}M_(){this.f_&&(this.f_.cancel(),this.f_=null)}x_(){this.g_&&(this.g_.cancel(),this.g_=null)}async close(e,t){this.M_(),this.x_(),this.y_.cancel(),this.m_++,4!==e?this.y_.reset():t&&t.code===C.RESOURCE_EXHAUSTED?(E(t.toString()),E("Using maximum backoff delay to prevent overloading the backend."),this.y_.I_()):t&&t.code===C.UNAUTHENTICATED&&3!==this.state&&(this.authCredentialsProvider.invalidateToken(),this.appCheckCredentialsProvider.invalidateToken()),null!==this.stream&&(this.O_(),this.stream.close(),this.stream=null),this.state=e,await this.listener.Zo(t)}O_(){}auth(){this.state=1;let e=this.N_(this.m_),t=this.m_;Promise.all([this.authCredentialsProvider.getToken(),this.appCheckCredentialsProvider.getToken()]).then(([e,n])=>{this.m_===t&&this.B_(e,n)},t=>{e(()=>{let e=new D(C.UNKNOWN,"Fetching auth token failed: "+t.message);return this.L_(e)})})}B_(e,t){let n=this.N_(this.m_);this.stream=this.k_(e,t),this.stream.jo(()=>{n(()=>this.listener.jo())}),this.stream.Jo(()=>{n(()=>(this.state=2,this.g_=this.bi.enqueueAfterDelay(this.V_,1e4,()=>(this.b_()&&(this.state=3),Promise.resolve())),this.listener.Jo()))}),this.stream.Zo(e=>{n(()=>this.L_(e))}),this.stream.onMessage(e=>{n(()=>1==++this.p_?this.q_(e):this.onNext(e))})}S_(){this.state=5,this.y_.E_(async()=>{this.state=0,this.start()})}L_(e){return T(am,`close with error: ${e}`),this.stream=null,this.close(4,e)}N_(e){return t=>{this.bi.enqueueAndForget(()=>this.m_===e?t():(T(am,"stream callback skipped by getCloseGuardedDispatcher."),Promise.resolve()))}}}class ap extends ag{constructor(e,t,n,r,i,s){super(e,"listen_stream_connection_backoff","listen_stream_idle","health_check_timeout",t,n,r,s),this.serializer=i}k_(e,t){return this.connection.a_("Listen",e,t)}q_(e){return this.onNext(e)}onNext(e){this.y_.reset();let t=function(e,t){let n;if("targetChange"in t){var r,i;t.targetChange;let s="NO_CHANGE"===(r=t.targetChange.targetChangeType||"NO_CHANGE")?0:"ADD"===r?1:"REMOVE"===r?2:"CURRENT"===r?3:"RESET"===r?4:S(39313,{state:r}),a=t.targetChange.targetIds||[],o=(i=t.targetChange.resumeToken,e.useProto3Json?(N(void 0===i||"string"==typeof i,58123),tO.fromBase64String(i||"")):(N(void 0===i||i instanceof m||i instanceof Uint8Array,16193),tO.fromUint8Array(i||new Uint8Array))),l=t.targetChange.cause;n=new rG(s,a,o,l&&new D(void 0===l.code?C.UNKNOWN:rO(l.code),l.message||"")||null)}else if("documentChange"in t){t.documentChange;let r=t.documentChange;r.document,r.document.name,r.document.updateTime;let i=r9(e,r.document.name),s=r5(r.document.updateTime),a=r.document.createTime?r5(r.document.createTime):j.min(),o=new nc({mapValue:{fields:r.document.fields}}),l=nd.newFoundDocument(i,s,a,o);n=new rK(r.targetIds||[],r.removedTargetIds||[],l.key,l)}else if("documentDelete"in t){t.documentDelete;let r=t.documentDelete;r.document;let i=r9(e,r.document),s=r.readTime?r5(r.readTime):j.min(),a=nd.newNoDocument(i,s);n=new rK([],r.removedTargetIds||[],a.key,a)}else if("documentRemove"in t){t.documentRemove;let r=t.documentRemove;r.document;let i=r9(e,r.document);n=new rK([],r.removedTargetIds||[],i,null)}else{if(!("filter"in t))return S(11601,{Vt:t});{t.filter;let e=t.filter;e.targetId;let{count:r=0,unchangedNames:i}=e,s=new rV(r,i);n=new r$(e.targetId,s)}}return n}(this.serializer,e),n=function(e){if(!("targetChange"in e))return j.min();let t=e.targetChange;return t.targetIds&&t.targetIds.length?j.min():t.readTime?r5(t.readTime):j.min()}(e);return this.listener.Q_(t,n)}U_(e){let t={};t.database=it(this.serializer),t.addTarget=function(e,t){let n,r=t.target;if((n=nL(r)?{documents:il(e,r)}:{query:iu(e,r).gt}).targetId=t.targetId,t.resumeToken.approximateByteSize()>0){n.resumeToken=r2(e,t.resumeToken);let r=r0(e,t.expectedCount);null!==r&&(n.expectedCount=r)}else if(t.snapshotVersion.compareTo(j.min())>0){n.readTime=r1(e,t.snapshotVersion.toTimestamp());let r=r0(e,t.expectedCount);null!==r&&(n.expectedCount=r)}return n}(this.serializer,e);let n=function(e,t){let n=function(e){switch(e){case"TargetPurposeListen":return null;case"TargetPurposeExistenceFilterMismatch":return"existence-filter-mismatch";case"TargetPurposeExistenceFilterMismatchBloom":return"existence-filter-mismatch-bloom";case"TargetPurposeLimboResolution":return"limbo-document";default:return S(28987,{purpose:e})}}(t.purpose);return null==n?null:{"goog-listen-tags":n}}(this.serializer,e);n&&(t.labels=n),this.F_(t)}K_(e){let t={};t.database=it(this.serializer),t.removeTarget=e,this.F_(t)}}class ay extends ag{constructor(e,t,n,r,i,s){super(e,"write_stream_connection_backoff","write_stream_idle","health_check_timeout",t,n,r,s),this.serializer=i}get W_(){return this.p_>0}start(){this.lastStreamToken=void 0,super.start()}O_(){this.W_&&this.G_([])}k_(e,t){return this.connection.a_("Write",e,t)}q_(e){return N(!!e.streamToken,31322),this.lastStreamToken=e.streamToken,N(!e.writeResults||0===e.writeResults.length,55816),this.listener.z_()}onNext(e){var t,n;N(!!e.streamToken,12678),this.lastStreamToken=e.streamToken,this.y_.reset();let r=(t=e.writeResults,n=e.commitTime,t&&t.length>0?(N(void 0!==n,14353),t.map(e=>{let t;return(t=e.updateTime?r5(e.updateTime):r5(n)).isEqual(j.min())&&(t=r5(n)),new rp(t,e.transformResults||[])})):[]),i=r5(e.commitTime);return this.listener.j_(i,r)}H_(){let e={};e.database=it(this.serializer),this.F_(e)}G_(e){let t={streamToken:this.lastStreamToken,writes:e.map(e=>ia(this.serializer,e))};this.F_(t)}}class aw{}class av extends aw{constructor(e,t,n,r){super(),this.authCredentials=e,this.appCheckCredentials=t,this.connection=n,this.serializer=r,this.J_=!1}Y_(){if(this.J_)throw new D(C.FAILED_PRECONDITION,"The client has already been terminated.")}Qo(e,t,n,r){return this.Y_(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then(([i,s])=>this.connection.Qo(e,r3(t,n),r,i,s)).catch(e=>{throw"FirebaseError"===e.name?(e.code===C.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new D(C.UNKNOWN,e.toString())})}Wo(e,t,n,r,i){return this.Y_(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then(([s,a])=>this.connection.Wo(e,r3(t,n),r,s,a,i)).catch(e=>{throw"FirebaseError"===e.name?(e.code===C.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),e):new D(C.UNKNOWN,e.toString())})}terminate(){this.J_=!0,this.connection.terminate()}}class aI{constructor(e,t){this.asyncQueue=e,this.onlineStateHandler=t,this.state="Unknown",this.Z_=0,this.X_=null,this.ea=!0}ta(){0===this.Z_&&(this.na("Unknown"),this.X_=this.asyncQueue.enqueueAfterDelay("online_state_timeout",1e4,()=>(this.X_=null,this.ra("Backend didn't respond within 10 seconds."),this.na("Offline"),Promise.resolve())))}ia(e){"Online"===this.state?this.na("Unknown"):(this.Z_++,this.Z_>=1&&(this.sa(),this.ra(`Connection failed 1 times. Most recent error: ${e.toString()}`),this.na("Offline")))}set(e){this.sa(),this.Z_=0,"Online"===e&&(this.ea=!1),this.na(e)}na(e){e!==this.state&&(this.state=e,this.onlineStateHandler(e))}ra(e){let t=`Could not reach Cloud Firestore backend. ${e}
This typically indicates that your device does not have a healthy Internet connection at the moment. The client will operate in offline mode until it is able to successfully connect to the backend.`;this.ea?(E(t),this.ea=!1):T("OnlineStateTracker",t)}sa(){null!==this.X_&&(this.X_.cancel(),this.X_=null)}}let aT="RemoteStore";class aE{constructor(e,t,n,r,i){this.localStore=e,this.datastore=t,this.asyncQueue=n,this.remoteSyncer={},this.oa=[],this._a=new Map,this.aa=new Set,this.ua=[],this.ca=i,this.ca.vo(e=>{n.enqueueAndForget(async()=>{ak(this)&&(T(aT,"Restarting streams for network reachability change."),await async function(e){e.aa.add(4),await a_(e),e.la.set("Unknown"),e.aa.delete(4),await ab(e)}(this))})}),this.la=new aI(n,r)}}async function ab(e){if(ak(e))for(let t of e.ua)await t(!0)}async function a_(e){for(let t of e.ua)await t(!1)}function aS(e,t){e._a.has(t.targetId)||(e._a.set(t.targetId,t),aA(e)?aD(e):aQ(e).b_()&&aN(e,t))}function ax(e,t){let n=aQ(e);e._a.delete(t),n.b_()&&aC(e,t),0===e._a.size&&(n.b_()?n.v_():ak(e)&&e.la.set("Unknown"))}function aN(e,t){if(e.ha.Ke(t.targetId),t.resumeToken.approximateByteSize()>0||t.snapshotVersion.compareTo(j.min())>0){let n=e.remoteSyncer.getRemoteKeysForTarget(t.targetId).size;t=t.withExpectedCount(n)}aQ(e).U_(t)}function aC(e,t){e.ha.Ke(t),aQ(e).K_(t)}function aD(e){e.ha=new rQ({getRemoteKeysForTarget:t=>e.remoteSyncer.getRemoteKeysForTarget(t),Rt:t=>e._a.get(t)||null,Pt:()=>e.datastore.serializer.databaseId}),aQ(e).start(),e.la.ta()}function aA(e){return ak(e)&&!aQ(e).w_()&&e._a.size>0}function ak(e){return 0===e.aa.size}async function aR(e){e.la.set("Online")}async function aV(e){e._a.forEach((t,n)=>{aN(e,t)})}async function aM(e,t){e.ha=void 0,aA(e)?(e.la.ia(t),aD(e)):e.la.set("Unknown")}async function aO(e,t,n){if(e.la.set("Online"),t instanceof rG&&2===t.state&&t.cause)try{await async function(e,t){let n=t.cause;for(let r of t.targetIds)e._a.has(r)&&(await e.remoteSyncer.rejectListen(r,n),e._a.delete(r),e.ha.removeTarget(r))}(e,t)}catch(n){T(aT,"Failed to remove targets %s: %s ",t.targetIds.join(","),n),await aF(e,n)}else if(t instanceof rK?e.ha.Xe(t):t instanceof r$?e.ha.ot(t):e.ha.nt(t),!n.isEqual(j.min()))try{let t=await sU(e.localStore);n.compareTo(t)>=0&&await function(e,t){let n=e.ha.It(t);return n.targetChanges.forEach((n,r)=>{if(n.resumeToken.approximateByteSize()>0){let i=e._a.get(r);i&&e._a.set(r,i.withResumeToken(n.resumeToken,t))}}),n.targetMismatches.forEach((t,n)=>{let r=e._a.get(t);if(!r)return;e._a.set(t,r.withResumeToken(tO.EMPTY_BYTE_STRING,r.snapshotVersion)),aC(e,t);let i=new ig(r.target,t,n,r.sequenceNumber);aN(e,i)}),e.remoteSyncer.applyRemoteEvent(n)}(e,n)}catch(t){T(aT,"Failed to raise snapshot:",t),await aF(e,t)}}async function aF(e,t,n){if(!ew(t))throw t;e.aa.add(1),await a_(e),e.la.set("Offline"),n||(n=()=>sU(e.localStore)),e.asyncQueue.enqueueRetryable(async()=>{T(aT,"Retrying IndexedDB access"),await n(),e.aa.delete(1),await ab(e)})}function aL(e,t){return t().catch(n=>aF(e,n,t))}async function aP(e){var t;let n=aW(e),r=e.oa.length>0?e.oa[e.oa.length-1].batchId:-1;for(;ak(t=e)&&t.oa.length<10;)try{let t=await function(e,t){return e.persistence.runTransaction("Get next mutation batch","readonly",n=>(void 0===t&&(t=-1),e.mutationQueue.getNextMutationBatchAfterBatchId(n,t)))}(e.localStore,r);if(null===t){0===e.oa.length&&n.v_();break}r=t.batchId,function(e,t){e.oa.push(t);let n=aW(e);n.b_()&&n.W_&&n.G_(t.mutations)}(e,t)}catch(t){await aF(e,t)}aU(e)&&aq(e)}function aU(e){return ak(e)&&!aW(e).w_()&&e.oa.length>0}function aq(e){aW(e).start()}async function aB(e){aW(e).H_()}async function az(e){let t=aW(e);for(let n of e.oa)t.G_(n.mutations)}async function aK(e,t,n){let r=e.oa.shift(),i=rk.from(r,t,n);await aL(e,()=>e.remoteSyncer.applySuccessfulWrite(i)),await aP(e)}async function a$(e,t){t&&aW(e).W_&&await async function(e,t){var n;if(rM(n=t.code)&&n!==C.ABORTED){let n=e.oa.shift();aW(e).D_(),await aL(e,()=>e.remoteSyncer.rejectFailedWrite(n.batchId,t)),await aP(e)}}(e,t),aU(e)&&aq(e)}async function aG(e,t){e.asyncQueue.verifyOperationInProgress(),T(aT,"RemoteStore received new credentials");let n=ak(e);e.aa.add(3),await a_(e),n&&e.la.set("Unknown"),await e.remoteSyncer.handleCredentialChange(t),e.aa.delete(3),await ab(e)}async function aj(e,t){t?(e.aa.delete(2),await ab(e)):t||(e.aa.add(2),await a_(e),e.la.set("Unknown"))}function aQ(e){var t,n,r;return e.Pa||(t=e.datastore,n=e.asyncQueue,r={jo:aR.bind(null,e),Jo:aV.bind(null,e),Zo:aM.bind(null,e),Q_:aO.bind(null,e)},t.Y_(),e.Pa=new ap(n,t.connection,t.authCredentials,t.appCheckCredentials,t.serializer,r),e.ua.push(async t=>{t?(e.Pa.D_(),aA(e)?aD(e):e.la.set("Unknown")):(await e.Pa.stop(),e.ha=void 0)})),e.Pa}function aW(e){var t,n,r;return e.Ta||(t=e.datastore,n=e.asyncQueue,r={jo:()=>Promise.resolve(),Jo:aB.bind(null,e),Zo:a$.bind(null,e),z_:az.bind(null,e),j_:aK.bind(null,e)},t.Y_(),e.Ta=new ay(n,t.connection,t.authCredentials,t.appCheckCredentials,t.serializer,r),e.ua.push(async t=>{t?(e.Ta.D_(),await aP(e)):(await e.Ta.stop(),e.oa.length>0&&(T(aT,`Stopping write stream with ${e.oa.length} pending writes`),e.oa=[]))})),e.Ta}class aH{constructor(e,t,n,r,i){this.asyncQueue=e,this.timerId=t,this.targetTimeMs=n,this.op=r,this.removalCallback=i,this.deferred=new A,this.then=this.deferred.promise.then.bind(this.deferred.promise),this.deferred.promise.catch(e=>{})}get promise(){return this.deferred.promise}static createAndSchedule(e,t,n,r,i){let s=new aH(e,t,Date.now()+n,r,i);return s.start(n),s}start(e){this.timerHandle=setTimeout(()=>this.handleDelayElapsed(),e)}skipDelay(){return this.handleDelayElapsed()}cancel(e){null!==this.timerHandle&&(this.clearTimeout(),this.deferred.reject(new D(C.CANCELLED,"Operation cancelled"+(e?": "+e:""))))}handleDelayElapsed(){this.asyncQueue.enqueueAndForget(()=>null!==this.timerHandle?(this.clearTimeout(),this.op().then(e=>this.deferred.resolve(e))):Promise.resolve())}clearTimeout(){null!==this.timerHandle&&(this.removalCallback(this),clearTimeout(this.timerHandle),this.timerHandle=null)}}function aY(e,t){if(E("AsyncQueue",`${t}: ${e}`),ew(e))return new D(C.UNAVAILABLE,`${t}: ${e}`);throw e}class aJ{static emptySet(e){return new aJ(e.comparator)}constructor(e){this.comparator=e?(t,n)=>e(t,n)||X.comparator(t.key,n.key):(e,t)=>X.comparator(e.key,t.key),this.keyedMap=n3(),this.sortedSet=new tN(this.comparator)}has(e){return null!=this.keyedMap.get(e)}get(e){return this.keyedMap.get(e)}first(){return this.sortedSet.minKey()}last(){return this.sortedSet.maxKey()}isEmpty(){return this.sortedSet.isEmpty()}indexOf(e){let t=this.keyedMap.get(e);return t?this.sortedSet.indexOf(t):-1}get size(){return this.sortedSet.size}forEach(e){this.sortedSet.inorderTraversal((t,n)=>(e(t),!1))}add(e){let t=this.delete(e.key);return t.copy(t.keyedMap.insert(e.key,e),t.sortedSet.insert(e,null))}delete(e){let t=this.get(e);return t?this.copy(this.keyedMap.remove(e),this.sortedSet.remove(t)):this}isEqual(e){if(!(e instanceof aJ)||this.size!==e.size)return!1;let t=this.sortedSet.getIterator(),n=e.sortedSet.getIterator();for(;t.hasNext();){let e=t.getNext().key,r=n.getNext().key;if(!e.isEqual(r))return!1}return!0}toString(){let e=[];return this.forEach(t=>{e.push(t.toString())}),0===e.length?"DocumentSet ()":"DocumentSet (\n  "+e.join("  \n")+"\n)"}copy(e,t){let n=new aJ;return n.comparator=this.comparator,n.keyedMap=e,n.sortedSet=t,n}}class aX{constructor(){this.Ia=new tN(X.comparator)}track(e){let t=e.doc.key,n=this.Ia.get(t);n?0!==e.type&&3===n.type?this.Ia=this.Ia.insert(t,e):3===e.type&&1!==n.type?this.Ia=this.Ia.insert(t,{type:n.type,doc:e.doc}):2===e.type&&2===n.type?this.Ia=this.Ia.insert(t,{type:2,doc:e.doc}):2===e.type&&0===n.type?this.Ia=this.Ia.insert(t,{type:0,doc:e.doc}):1===e.type&&0===n.type?this.Ia=this.Ia.remove(t):1===e.type&&2===n.type?this.Ia=this.Ia.insert(t,{type:1,doc:n.doc}):0===e.type&&1===n.type?this.Ia=this.Ia.insert(t,{type:2,doc:e.doc}):S(63341,{Vt:e,Ea:n}):this.Ia=this.Ia.insert(t,e)}da(){let e=[];return this.Ia.inorderTraversal((t,n)=>{e.push(n)}),e}}class aZ{constructor(e,t,n,r,i,s,a,o,l){this.query=e,this.docs=t,this.oldDocs=n,this.docChanges=r,this.mutatedKeys=i,this.fromCache=s,this.syncStateChanged=a,this.excludesMetadataChanges=o,this.hasCachedResults=l}static fromInitialDocuments(e,t,n,r,i){let s=[];return t.forEach(e=>{s.push({type:0,doc:e})}),new aZ(e,t,aJ.emptySet(t),s,n,r,!0,!1,i)}get hasPendingWrites(){return!this.mutatedKeys.isEmpty()}isEqual(e){if(!(this.fromCache===e.fromCache&&this.hasCachedResults===e.hasCachedResults&&this.syncStateChanged===e.syncStateChanged&&this.mutatedKeys.isEqual(e.mutatedKeys)&&nY(this.query,e.query)&&this.docs.isEqual(e.docs)&&this.oldDocs.isEqual(e.oldDocs)))return!1;let t=this.docChanges,n=e.docChanges;if(t.length!==n.length)return!1;for(let e=0;e<t.length;e++)if(t[e].type!==n[e].type||!t[e].doc.isEqual(n[e].doc))return!1;return!0}}class a0{constructor(){this.Aa=void 0,this.Ra=[]}Va(){return this.Ra.some(e=>e.ma())}}class a1{constructor(){this.queries=a2(),this.onlineState="Unknown",this.fa=new Set}terminate(){!function(e,t){let n=e.queries;e.queries=a2(),n.forEach((e,n)=>{for(let e of n.Ra)e.onError(t)})}(this,new D(C.ABORTED,"Firestore shutting down"))}}function a2(){return new n2(e=>nJ(e),nY)}async function a5(e,t){let n=3,r=t.query,i=e.queries.get(r);i?!i.Va()&&t.ma()&&(n=2):(i=new a0,n=+!t.ma());try{switch(n){case 0:i.Aa=await e.onListen(r,!0);break;case 1:i.Aa=await e.onListen(r,!1);break;case 2:await e.onFirstRemoteStoreListen(r)}}catch(n){let e=aY(n,`Initialization of query '${nX(t.query)}' failed`);return void t.onError(e)}e.queries.set(r,i),i.Ra.push(t),t.ga(e.onlineState),i.Aa&&t.pa(i.Aa)&&a8(e)}async function a4(e,t){let n=t.query,r=3,i=e.queries.get(n);if(i){let e=i.Ra.indexOf(t);e>=0&&(i.Ra.splice(e,1),0===i.Ra.length?r=+!t.ma():!i.Va()&&t.ma()&&(r=2))}switch(r){case 0:return e.queries.delete(n),e.onUnlisten(n,!0);case 1:return e.queries.delete(n),e.onUnlisten(n,!1);case 2:return e.onLastRemoteStoreUnlisten(n);default:return}}function a3(e,t){let n=!1;for(let r of t){let t=r.query,i=e.queries.get(t);if(i){for(let e of i.Ra)e.pa(r)&&(n=!0);i.Aa=r}}n&&a8(e)}function a6(e,t,n){let r=e.queries.get(t);if(r)for(let e of r.Ra)e.onError(n);e.queries.delete(t)}function a8(e){e.fa.forEach(e=>{e.next()})}(a=s||(s={})).ya="default",a.Cache="cache";class a9{constructor(e,t,n){this.query=e,this.wa=t,this.ba=!1,this.Sa=null,this.onlineState="Unknown",this.options=n||{}}pa(e){if(!this.options.includeMetadataChanges){let t=[];for(let n of e.docChanges)3!==n.type&&t.push(n);e=new aZ(e.query,e.docs,e.oldDocs,t,e.mutatedKeys,e.fromCache,e.syncStateChanged,!0,e.hasCachedResults)}let t=!1;return this.ba?this.Da(e)&&(this.wa.next(e),t=!0):this.va(e,this.onlineState)&&(this.Ca(e),t=!0),this.Sa=e,t}onError(e){this.wa.error(e)}ga(e){this.onlineState=e;let t=!1;return this.Sa&&!this.ba&&this.va(this.Sa,e)&&(this.Ca(this.Sa),t=!0),t}va(e,t){return!(e.fromCache&&this.ma())||(!this.options.Fa||"Offline"===t)&&(!e.docs.isEmpty()||e.hasCachedResults||"Offline"===t)}Da(e){if(e.docChanges.length>0)return!0;let t=this.Sa&&this.Sa.hasPendingWrites!==e.hasPendingWrites;return!(!e.syncStateChanged&&!t)&&!0===this.options.includeMetadataChanges}Ca(e){e=aZ.fromInitialDocuments(e.query,e.docs,e.mutatedKeys,e.fromCache,e.hasCachedResults),this.ba=!0,this.wa.next(e)}ma(){return this.options.source!==s.Cache}}class a7{constructor(e,t){this.Ma=e,this.byteLength=t}xa(){return"metadata"in this.Ma}}class oe{constructor(e){this.serializer=e}Bs(e){return r9(this.serializer,e)}Ls(e){return e.metadata.exists?is(this.serializer,e.document,!1):nd.newNoDocument(this.Bs(e.metadata.name),this.ks(e.metadata.readTime))}ks(e){return r5(e)}}class ot{constructor(e){this.key=e}}class on{constructor(e){this.key=e}}class or{constructor(e,t){this.query=e,this.qa=t,this.Qa=null,this.hasCachedResults=!1,this.current=!1,this.$a=re(),this.mutatedKeys=re(),this.Ua=n1(e),this.Ka=new aJ(this.Ua)}get Wa(){return this.qa}Ga(e,t){let n=t?t.za:new aX,r=t?t.Ka:this.Ka,i=t?t.mutatedKeys:this.mutatedKeys,s=r,a=!1,o="F"===this.query.limitType&&r.size===this.query.limit?r.last():null,l="L"===this.query.limitType&&r.size===this.query.limit?r.first():null;if(e.inorderTraversal((e,t)=>{let u=r.get(e),h=nZ(this.query,t)?t:null,c=!!u&&this.mutatedKeys.has(u.key),d=!!h&&(h.hasLocalMutations||this.mutatedKeys.has(h.key)&&h.hasCommittedMutations),f=!1;u&&h?u.data.isEqual(h.data)?c!==d&&(n.track({type:3,doc:h}),f=!0):this.ja(u,h)||(n.track({type:2,doc:h}),f=!0,(o&&this.Ua(h,o)>0||l&&0>this.Ua(h,l))&&(a=!0)):!u&&h?(n.track({type:0,doc:h}),f=!0):u&&!h&&(n.track({type:1,doc:u}),f=!0,(o||l)&&(a=!0)),f&&(h?(s=s.add(h),i=d?i.add(e):i.delete(e)):(s=s.delete(e),i=i.delete(e)))}),null!==this.query.limit)for(;s.size>this.query.limit;){let e="F"===this.query.limitType?s.last():s.first();s=s.delete(e.key),i=i.delete(e.key),n.track({type:1,doc:e})}return{Ka:s,za:n,ys:a,mutatedKeys:i}}ja(e,t){return e.hasLocalMutations&&t.hasCommittedMutations&&!t.hasLocalMutations}applyChanges(e,t,n,r){let i=this.Ka;this.Ka=e.Ka,this.mutatedKeys=e.mutatedKeys;let s=e.za.da();s.sort((e,t)=>(function(e,t){let n=e=>{switch(e){case 0:return 1;case 2:case 3:return 2;case 1:return 0;default:return S(20277,{Vt:e})}};return n(e)-n(t)})(e.type,t.type)||this.Ua(e.doc,t.doc)),this.Ha(n),r=null!=r&&r;let a=t&&!r?this.Ja():[],o=0===this.$a.size&&this.current&&!r?1:0,l=o!==this.Qa;return(this.Qa=o,0!==s.length||l)?{snapshot:new aZ(this.query,e.Ka,i,s,e.mutatedKeys,0===o,l,!1,!!n&&n.resumeToken.approximateByteSize()>0),Ya:a}:{Ya:a}}ga(e){return this.current&&"Offline"===e?(this.current=!1,this.applyChanges({Ka:this.Ka,za:new aX,mutatedKeys:this.mutatedKeys,ys:!1},!1)):{Ya:[]}}Za(e){return!this.qa.has(e)&&!!this.Ka.has(e)&&!this.Ka.get(e).hasLocalMutations}Ha(e){e&&(e.addedDocuments.forEach(e=>this.qa=this.qa.add(e)),e.modifiedDocuments.forEach(e=>{}),e.removedDocuments.forEach(e=>this.qa=this.qa.delete(e)),this.current=e.current)}Ja(){if(!this.current)return[];let e=this.$a;this.$a=re(),this.Ka.forEach(e=>{this.Za(e.key)&&(this.$a=this.$a.add(e.key))});let t=[];return e.forEach(e=>{this.$a.has(e)||t.push(new on(e))}),this.$a.forEach(n=>{e.has(n)||t.push(new ot(n))}),t}Xa(e){this.qa=e.Ns,this.$a=re();let t=this.Ga(e.documents);return this.applyChanges(t,!0)}eu(){return aZ.fromInitialDocuments(this.query,this.Ka,this.mutatedKeys,0===this.Qa,this.hasCachedResults)}}let oi="SyncEngine";class os{constructor(e,t,n){this.query=e,this.targetId=t,this.view=n}}class oa{constructor(e){this.key=e,this.tu=!1}}class oo{constructor(e,t,n,r,i,s){this.localStore=e,this.remoteStore=t,this.eventManager=n,this.sharedClientState=r,this.currentUser=i,this.maxConcurrentLimboResolutions=s,this.nu={},this.ru=new n2(e=>nJ(e),nY),this.iu=new Map,this.su=new Set,this.ou=new tN(X.comparator),this._u=new Map,this.au=new sy,this.uu={},this.cu=new Map,this.lu=i6.ir(),this.onlineState="Unknown",this.hu=void 0}get isPrimaryClient(){return!0===this.hu}}async function ol(e,t,n=!0){let r,i=oP(e),s=i.ru.get(t);return s?(i.sharedClientState.addLocalQueryTarget(s.targetId),r=s.view.eu()):r=await oh(i,t,n,!0),r}async function ou(e,t){let n=oP(e);await oh(n,t,!0,!1)}async function oh(e,t,n,r){let i,s=await sB(e.localStore,nj(t)),a=s.targetId,o=e.sharedClientState.addLocalQueryTarget(a,n);return r&&(i=await oc(e,t,a,"current"===o,s.resumeToken)),e.isPrimaryClient&&n&&aS(e.remoteStore,s),i}async function oc(e,t,n,r,i){e.Pu=(t,n,r)=>(async function(e,t,n,r){let i=t.view.Ga(n);i.ys&&(i=await sK(e.localStore,t.query,!1).then(({documents:e})=>t.view.Ga(e,i)));let s=r&&r.targetChanges.get(t.targetId),a=r&&null!=r.targetMismatches.get(t.targetId),o=t.view.applyChanges(i,e.isPrimaryClient,s,a);return o_(e,t.targetId,o.Ya),o.snapshot})(e,t,n,r);let s=await sK(e.localStore,t,!0),a=new or(t,s.Ns),o=a.Ga(s.documents),l=rz.createSynthesizedTargetChangeForCurrentChange(n,r&&"Offline"!==e.onlineState,i),u=a.applyChanges(o,e.isPrimaryClient,l);o_(e,n,u.Ya);let h=new os(t,n,a);return e.ru.set(t,h),e.iu.has(n)?e.iu.get(n).push(t):e.iu.set(n,[t]),u.snapshot}async function od(e,t,n){let r=e.ru.get(t),i=e.iu.get(r.targetId);if(i.length>1)return e.iu.set(r.targetId,i.filter(e=>!nY(e,t))),void e.ru.delete(t);e.isPrimaryClient?(e.sharedClientState.removeLocalQueryTarget(r.targetId),e.sharedClientState.isActiveQueryTarget(r.targetId)||await sz(e.localStore,r.targetId,!1).then(()=>{e.sharedClientState.clearQueryState(r.targetId),n&&ax(e.remoteStore,r.targetId),oE(e,r.targetId)}).catch(eh)):(oE(e,r.targetId),await sz(e.localStore,r.targetId,!0))}async function of(e,t){let n=e.ru.get(t),r=e.iu.get(n.targetId);e.isPrimaryClient&&1===r.length&&(e.sharedClientState.removeLocalQueryTarget(n.targetId),ax(e.remoteStore,n.targetId))}async function om(e,t,n){let r=oU(e);try{var i;let e,s=await function(e,t){let n,r,i=G.now(),s=t.reduce((e,t)=>e.add(t.key),re());return e.persistence.runTransaction("Locally write mutations","readwrite",a=>{let o=n5,l=re();return e.Cs.getEntries(a,s).next(e=>{(o=e).forEach((e,t)=>{t.isValidDocument()||(l=l.add(e))})}).next(()=>e.localDocuments.getOverlayedDocuments(a,o)).next(r=>{n=r;let s=[];for(let e of t){let t=function(e,t){let n=null;for(let r of e.fieldTransforms){let e=t.data.field(r.field),i=ra(r.transform,e||null);null!=i&&(null===n&&(n=nc.empty()),n.set(r.field,i))}return n||null}(e,n.get(e.key).overlayedDocument);null!=t&&s.push(new r_(e.key,t,function e(t){let n=[];return tS(t.fields,(t,r)=>{let i=new J([t]);if(ni(r)){let t=e(r.mapValue).fields;if(0===t.length)n.push(i);else for(let e of t)n.push(i.child(e))}else n.push(i)}),new tV(n)}(t.value.mapValue),ry.exists(!0)))}return e.mutationQueue.addMutationBatch(a,i,s,t)}).next(t=>{r=t;let i=t.applyToLocalDocumentSet(n,l);return e.documentOverlayCache.saveOverlays(a,t.batchId,i)})}).then(()=>({batchId:r.batchId,changes:n6(n)}))}(r.localStore,t);r.sharedClientState.addPendingMutation(s.batchId),i=s.batchId,(e=r.uu[r.currentUser.toKey()])||(e=new tN(B)),e=e.insert(i,n),r.uu[r.currentUser.toKey()]=e,await ox(r,s.changes),await aP(r.remoteStore)}catch(t){let e=aY(t,"Failed to persist write");n.reject(e)}}async function og(e,t){try{let n=await function(e,t){let n=t.snapshotVersion,r=e.Ss;return e.persistence.runTransaction("Apply remote event","readwrite-primary",i=>{let s=e.Cs.newChangeBuffer({trackRemovals:!0});r=e.Ss;let a=[];t.targetChanges.forEach((s,o)=>{var l;let u=r.get(o);if(!u)return;a.push(e.ai.removeMatchingKeys(i,s.removedDocuments,o).next(()=>e.ai.addMatchingKeys(i,s.addedDocuments,o)));let h=u.withSequenceNumber(i.currentSequenceNumber);null!==t.targetMismatches.get(o)?h=h.withResumeToken(tO.EMPTY_BYTE_STRING,j.min()).withLastLimboFreeSnapshotVersion(j.min()):s.resumeToken.approximateByteSize()>0&&(h=h.withResumeToken(s.resumeToken,n)),r=r.insert(o,h),l=h,(0===u.resumeToken.approximateByteSize()||l.snapshotVersion.toMicroseconds()-u.snapshotVersion.toMicroseconds()>=3e8||s.addedDocuments.size+s.modifiedDocuments.size+s.removedDocuments.size>0)&&a.push(e.ai.updateTargetData(i,h))});let o=n5,l=re();if(t.documentUpdates.forEach(n=>{t.resolvedLimboDocuments.has(n)&&a.push(e.persistence.referenceDelegate.updateLimboDocument(i,n))}),a.push(sq(i,s,t.documentUpdates).next(e=>{o=e.xs,l=e.Os})),!n.isEqual(j.min())){let t=e.ai.getLastRemoteSnapshotVersion(i).next(t=>e.ai.setTargetsMetadata(i,i.currentSequenceNumber,n));a.push(t)}return ec.waitFor(a).next(()=>s.apply(i)).next(()=>e.localDocuments.getLocalViewOfDocuments(i,o,l)).next(()=>o)}).then(t=>(e.Ss=r,t))}(e.localStore,t);t.targetChanges.forEach((t,n)=>{let r=e._u.get(n);r&&(N(t.addedDocuments.size+t.modifiedDocuments.size+t.removedDocuments.size<=1,22616),t.addedDocuments.size>0?r.tu=!0:t.modifiedDocuments.size>0?N(r.tu,14607):t.removedDocuments.size>0&&(N(r.tu,42227),r.tu=!1))}),await ox(e,n,t)}catch(e){await eh(e)}}function op(e,t,n){var r;if(e.isPrimaryClient&&0===n||!e.isPrimaryClient&&1===n){let n,i=[];e.ru.forEach((e,n)=>{let r=n.view.ga(t);r.snapshot&&i.push(r.snapshot)}),(r=e.eventManager).onlineState=t,n=!1,r.queries.forEach((e,r)=>{for(let e of r.Ra)e.ga(t)&&(n=!0)}),n&&a8(r),i.length&&e.nu.Q_(i),e.onlineState=t,e.isPrimaryClient&&e.sharedClientState.setOnlineState(t)}}async function oy(e,t,n){e.sharedClientState.updateQueryState(t,"rejected",n);let r=e._u.get(t),i=r&&r.key;if(i){let n=new tN(X.comparator);n=n.insert(i,nd.newNoDocument(i,j.min()));let r=re().add(i),s=new rB(j.min(),new Map,new tN(B),n,r);await og(e,s),e.ou=e.ou.remove(i),e._u.delete(t),oS(e)}else await sz(e.localStore,t,!1).then(()=>oE(e,t,n)).catch(eh)}async function ow(e,t){var n;let r=t.batch.batchId;try{let i=await (n=e.localStore,n.persistence.runTransaction("Acknowledge batch","readwrite-primary",e=>{let r=t.batch.keys(),i=n.Cs.newChangeBuffer({trackRemovals:!0});return(function(e,t,n,r){let i=n.batch,s=i.keys(),a=ec.resolve();return s.forEach(e=>{a=a.next(()=>r.getEntry(t,e)).next(t=>{let s=n.docVersions.get(e);N(null!==s,48541),0>t.version.compareTo(s)&&(i.applyToRemoteDocument(t,n),t.isValidDocument()&&(t.setReadTime(n.commitVersion),r.addEntry(t)))})}),a.next(()=>e.mutationQueue.removeMutationBatch(t,i))})(n,e,t,i).next(()=>i.apply(e)).next(()=>n.mutationQueue.performConsistencyCheck(e)).next(()=>n.documentOverlayCache.removeOverlaysForBatchId(e,r,t.batch.batchId)).next(()=>n.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(e,function(e){let t=re();for(let n=0;n<e.mutationResults.length;++n)e.mutationResults[n].transformResults.length>0&&(t=t.add(e.batch.mutations[n].key));return t}(t))).next(()=>n.localDocuments.getDocuments(e,r))}));oT(e,r,null),oI(e,r),e.sharedClientState.updateMutationState(r,"acknowledged"),await ox(e,i)}catch(e){await eh(e)}}async function ov(e,t,n){var r;try{let i=await (r=e.localStore,r.persistence.runTransaction("Reject batch","readwrite-primary",e=>{let n;return r.mutationQueue.lookupMutationBatch(e,t).next(t=>(N(null!==t,37113),n=t.keys(),r.mutationQueue.removeMutationBatch(e,t))).next(()=>r.mutationQueue.performConsistencyCheck(e)).next(()=>r.documentOverlayCache.removeOverlaysForBatchId(e,n,t)).next(()=>r.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(e,n)).next(()=>r.localDocuments.getDocuments(e,n))}));oT(e,t,n),oI(e,t),e.sharedClientState.updateMutationState(t,"rejected",n),await ox(e,i)}catch(e){await eh(e)}}function oI(e,t){(e.cu.get(t)||[]).forEach(e=>{e.resolve()}),e.cu.delete(t)}function oT(e,t,n){let r=e.uu[e.currentUser.toKey()];if(r){let i=r.get(t);i&&(n?i.reject(n):i.resolve(),r=r.remove(t)),e.uu[e.currentUser.toKey()]=r}}function oE(e,t,n=null){for(let r of(e.sharedClientState.removeLocalQueryTarget(t),e.iu.get(t)))e.ru.delete(r),n&&e.nu.Tu(r,n);e.iu.delete(t),e.isPrimaryClient&&e.au.Ur(t).forEach(t=>{e.au.containsKey(t)||ob(e,t)})}function ob(e,t){e.su.delete(t.path.canonicalString());let n=e.ou.get(t);null!==n&&(ax(e.remoteStore,n),e.ou=e.ou.remove(t),e._u.delete(n),oS(e))}function o_(e,t,n){for(let r of n)r instanceof ot?(e.au.addReference(r.key,t),function(e,t){let n=t.key,r=n.path.canonicalString();e.ou.get(n)||e.su.has(r)||(T(oi,"New document in limbo: "+n),e.su.add(r),oS(e))}(e,r)):r instanceof on?(T(oi,"Document no longer in limbo: "+r.key),e.au.removeReference(r.key,t),e.au.containsKey(r.key)||ob(e,r.key)):S(19791,{Iu:r})}function oS(e){for(;e.su.size>0&&e.ou.size<e.maxConcurrentLimboResolutions;){let t=e.su.values().next().value;e.su.delete(t);let n=new X(H.fromString(t)),r=e.lu.next();e._u.set(r,new oa(n)),e.ou=e.ou.insert(n,r),aS(e.remoteStore,new ig(nj(nz(n.path)),r,"TargetPurposeLimboResolution",ex.le))}}async function ox(e,t,n){let r=[],i=[],s=[];e.ru.isEmpty()||(e.ru.forEach((a,o)=>{s.push(e.Pu(o,t,n).then(t=>{var s;if((t||n)&&e.isPrimaryClient){let r=t?!t.fromCache:null==(s=null==n?void 0:n.targetChanges.get(o.targetId))?void 0:s.current;e.sharedClientState.updateQueryState(o.targetId,r?"current":"not-current")}if(t){r.push(t);let e=sV.Ps(o.targetId,t);i.push(e)}}))}),await Promise.all(s),e.nu.Q_(r),await async function(e,t){try{await e.persistence.runTransaction("notifyLocalViewChanges","readwrite",n=>ec.forEach(t,t=>ec.forEach(t.ls,r=>e.persistence.referenceDelegate.addReference(n,t.targetId,r)).next(()=>ec.forEach(t.hs,r=>e.persistence.referenceDelegate.removeReference(n,t.targetId,r)))))}catch(e){if(!ew(e))throw e;T(sF,"Failed to update sequence numbers: "+e)}for(let n of t){let t=n.targetId;if(!n.fromCache){let n=e.Ss.get(t),r=n.snapshotVersion,i=n.withLastLimboFreeSnapshotVersion(r);e.Ss=e.Ss.insert(t,i)}}}(e.localStore,i))}async function oN(e,t){if(!e.currentUser.isEqual(t)){T(oi,"User change. New user:",t.toKey());let n=await sP(e.localStore,t);e.currentUser=t,e.cu.forEach(e=>{e.forEach(e=>{e.reject(new D(C.CANCELLED,"'waitForPendingWrites' promise is rejected due to a user change."))})}),e.cu.clear(),e.sharedClientState.handleUserChange(t,n.removedBatchIds,n.addedBatchIds),await ox(e,n.Ms)}}function oC(e,t){let n=e._u.get(t);if(n&&n.tu)return re().add(n.key);{let n=re(),r=e.iu.get(t);if(!r)return n;for(let t of r){let r=e.ru.get(t);n=n.unionWith(r.view.Wa)}return n}}async function oD(e,t){let n=await sK(e.localStore,t.query,!0),r=t.view.Xa(n);return e.isPrimaryClient&&o_(e,t.targetId,r.Ya),r}async function oA(e,t){return sG(e.localStore,t).then(t=>ox(e,t))}async function ok(e,t,n,r){var i;let s=await function(e,t){let n=e.mutationQueue;return e.persistence.runTransaction("Lookup mutation documents","readonly",r=>n.Hn(r,t).next(t=>t?e.localDocuments.getDocuments(r,t):ec.resolve(null)))}(e.localStore,t);null!==s?("pending"===n?await aP(e.remoteStore):"acknowledged"===n||"rejected"===n?(oT(e,t,r||null),oI(e,t),i=e.localStore,i.mutationQueue.Xn(t)):S(6720,"Unknown batchState",{Eu:n}),await ox(e,s)):T(oi,"Cannot apply mutation batch with id: "+t)}async function oR(e,t){if(oP(e),oU(e),!0===t&&!0!==e.hu){let t=e.sharedClientState.getAllActiveQueryTargets(),n=await oV(e,t.toArray());for(let t of(e.hu=!0,await aj(e.remoteStore,!0),n))aS(e.remoteStore,t)}else if(!1===t&&!1!==e.hu){let t=[],n=Promise.resolve();e.iu.forEach((r,i)=>{e.sharedClientState.isLocalQueryTarget(i)?t.push(i):n=n.then(()=>(oE(e,i),sz(e.localStore,i,!0))),ax(e.remoteStore,i)}),await n,await oV(e,t),e._u.forEach((t,n)=>{ax(e.remoteStore,n)}),e.au.Kr(),e._u=new Map,e.ou=new tN(X.comparator),e.hu=!1,await aj(e.remoteStore,!1)}}async function oV(e,t,n){let r=[],i=[];for(let n of t){let t,s=e.iu.get(n);if(s&&0!==s.length)for(let n of(t=await sB(e.localStore,nj(s[0])),s)){let t=e.ru.get(n),r=await oD(e,t);r.snapshot&&i.push(r.snapshot)}else{let r=await s$(e.localStore,n);t=await sB(e.localStore,r),await oc(e,oM(r),n,!1,t.resumeToken)}r.push(t)}return e.nu.Q_(i),r}function oM(e){var t,n,r,i,s;return t=e.path,n=e.collectionGroup,r=e.orderBy,i=e.filters,s=e.limit,new nB(t,n,r,i,s,"F",e.startAt,e.endAt)}function oO(e){return e.localStore.persistence.us()}async function oF(e,t,n,r){if(e.hu)return void T(oi,"Ignoring unexpected query state notification.");let i=e.iu.get(t);if(i&&i.length>0)switch(n){case"current":case"not-current":{let r=await sG(e.localStore,n0(i[0])),s=rB.createSynthesizedRemoteEventForCurrentChange(t,"current"===n,tO.EMPTY_BYTE_STRING);await ox(e,r,s);break}case"rejected":await sz(e.localStore,t,!0),oE(e,t,r);break;default:S(64155,n)}}async function oL(e,t,n){let r=oP(e);if(r.hu){for(let e of t){if(r.iu.has(e)&&r.sharedClientState.isActiveQueryTarget(e)){T(oi,"Adding an already active target "+e);continue}let t=await s$(r.localStore,e),n=await sB(r.localStore,t);await oc(r,oM(t),n.targetId,!1,n.resumeToken),aS(r.remoteStore,n)}for(let e of n)r.iu.has(e)&&await sz(r.localStore,e,!1).then(()=>{ax(r.remoteStore,e),oE(r,e)}).catch(eh)}}function oP(e){return e.remoteStore.remoteSyncer.applyRemoteEvent=og.bind(null,e),e.remoteStore.remoteSyncer.getRemoteKeysForTarget=oC.bind(null,e),e.remoteStore.remoteSyncer.rejectListen=oy.bind(null,e),e.nu.Q_=a3.bind(null,e.eventManager),e.nu.Tu=a6.bind(null,e.eventManager),e}function oU(e){return e.remoteStore.remoteSyncer.applySuccessfulWrite=ow.bind(null,e),e.remoteStore.remoteSyncer.rejectFailedWrite=ov.bind(null,e),e}class oq{constructor(){this.kind="memory",this.synchronizeTabs=!1}async initialize(e){this.serializer=ad(e.databaseInfo.databaseId),this.sharedClientState=this.Au(e),this.persistence=this.Ru(e),await this.persistence.start(),this.localStore=this.Vu(e),this.gcScheduler=this.mu(e,this.localStore),this.indexBackfillerScheduler=this.fu(e,this.localStore)}mu(e,t){return null}fu(e,t){return null}Vu(e){var t,n;return t=this.persistence,n=new sO,new sL(t,n,e.initialUser,this.serializer)}Ru(e){return new sb(sS.Ei,this.serializer)}Au(e){return new s9}async terminate(){var e,t;null==(e=this.gcScheduler)||e.stop(),null==(t=this.indexBackfillerScheduler)||t.stop(),this.sharedClientState.shutdown(),await this.persistence.shutdown()}}oq.provider={build:()=>new oq};class oB extends oq{constructor(e){super(),this.cacheSizeBytes=e}mu(e,t){return N(this.persistence.referenceDelegate instanceof sx,46915),new sn(this.persistence.referenceDelegate.garbageCollector,e.asyncQueue,t)}Ru(e){let t=void 0!==this.cacheSizeBytes?i0.withCacheSize(this.cacheSizeBytes):i0.DEFAULT;return new sb(e=>sx.Ei(e,t),this.serializer)}}class oz extends oq{constructor(e,t,n){super(),this.gu=e,this.cacheSizeBytes=t,this.forceOwnership=n,this.kind="persistent",this.synchronizeTabs=!1}async initialize(e){await super.initialize(e),await this.gu.initialize(this,e),await oU(this.gu.syncEngine),await aP(this.gu.remoteStore),await this.persistence.Ki(()=>(this.gcScheduler&&!this.gcScheduler.started&&this.gcScheduler.start(),this.indexBackfillerScheduler&&!this.indexBackfillerScheduler.started&&this.indexBackfillerScheduler.start(),Promise.resolve()))}Vu(e){var t,n;return t=this.persistence,n=new sO,new sL(t,n,e.initialUser,this.serializer)}mu(e,t){return new sn(this.persistence.referenceDelegate.garbageCollector,e.asyncQueue,t)}fu(e,t){let n=new eS(t,this.persistence);return new e_(e.asyncQueue,n)}Ru(e){let t=sR(e.databaseInfo.databaseId,e.databaseInfo.persistenceKey),n=void 0!==this.cacheSizeBytes?i0.withCacheSize(this.cacheSizeBytes):i0.DEFAULT;return new sk(this.synchronizeTabs,t,e.clientId,n,e.asyncQueue,ah(),ac(),this.serializer,this.sharedClientState,!!this.forceOwnership)}Au(e){return new s9}}class oK extends oz{constructor(e,t){super(e,t,!1),this.gu=e,this.cacheSizeBytes=t,this.synchronizeTabs=!0}async initialize(e){await super.initialize(e);let t=this.gu.syncEngine;this.sharedClientState instanceof s8&&(this.sharedClientState.syncEngine={yo:ok.bind(null,t),wo:oF.bind(null,t),bo:oL.bind(null,t),us:oO.bind(null,t),po:oA.bind(null,t)},await this.sharedClientState.start()),await this.persistence.Ki(async e=>{await oR(this.gu.syncEngine,e),this.gcScheduler&&(e&&!this.gcScheduler.started?this.gcScheduler.start():e||this.gcScheduler.stop()),this.indexBackfillerScheduler&&(e&&!this.indexBackfillerScheduler.started?this.indexBackfillerScheduler.start():e||this.indexBackfillerScheduler.stop())})}Au(e){let t=ah();if(!s8.C(t))throw new D(C.UNIMPLEMENTED,"IndexedDB persistence is only available on platforms that support LocalStorage.");let n=sR(e.databaseInfo.databaseId,e.databaseInfo.persistenceKey);return new s8(t,e.asyncQueue,n,e.clientId,e.initialUser)}}class o${async initialize(e,t){this.localStore||(this.localStore=e.localStore,this.sharedClientState=e.sharedClientState,this.datastore=this.createDatastore(t),this.remoteStore=this.createRemoteStore(t),this.eventManager=this.createEventManager(t),this.syncEngine=this.createSyncEngine(t,!e.synchronizeTabs),this.sharedClientState.onlineStateHandler=e=>op(this.syncEngine,e,1),this.remoteStore.remoteSyncer.handleCredentialChange=oN.bind(null,this.syncEngine),await aj(this.remoteStore,this.syncEngine.isPrimaryClient))}createEventManager(e){return new a1}createDatastore(e){var t;let n=ad(e.databaseInfo.databaseId),r=new au(e.databaseInfo);return t=e.authCredentials,new av(t,e.appCheckCredentials,r,n)}createRemoteStore(e){var t,n;return t=this.localStore,n=this.datastore,new aE(t,n,e.asyncQueue,e=>op(this.syncEngine,e,0),at.C()?new at:new s7)}createSyncEngine(e,t){return function(e,t,n,r,i,s,a){let o=new oo(e,t,n,r,i,s);return a&&(o.hu=!0),o}(this.localStore,this.remoteStore,this.eventManager,this.sharedClientState,e.initialUser,e.maxConcurrentLimboResolutions,t)}async terminate(){var e,t;await async function(e){T(aT,"RemoteStore shutting down."),e.aa.add(5),await a_(e),e.ca.shutdown(),e.la.set("Unknown")}(this.remoteStore),null==(e=this.datastore)||e.terminate(),null==(t=this.eventManager)||t.terminate()}}o$.provider={build:()=>new o$};class oG{constructor(e){this.observer=e,this.muted=!1}next(e){this.muted||this.observer.next&&this.pu(this.observer.next,e)}error(e){this.muted||(this.observer.error?this.pu(this.observer.error,e):E("Uncaught Error in snapshot listener:",e.toString()))}yu(){this.muted=!0}pu(e,t){setTimeout(()=>{this.muted||e(t)},0)}}class oj{constructor(e){this.datastore=e,this.readVersions=new Map,this.mutations=[],this.committed=!1,this.lastTransactionError=null,this.writtenDocs=new Set}async lookup(e){if(this.ensureCommitNotCalled(),this.mutations.length>0)throw this.lastTransactionError=new D(C.INVALID_ARGUMENT,"Firestore transactions require all reads to be executed before all writes."),this.lastTransactionError;let t=await async function(e,t){let n={documents:t.map(t=>r8(e.serializer,t))},r=await e.Wo("BatchGetDocuments",e.serializer.databaseId,H.emptyPath(),n,t.length),i=new Map;r.forEach(t=>{var n;let r=(n=e.serializer,"found"in t?function(e,t){N(!!t.found,43571),t.found.name,t.found.updateTime;let n=r9(e,t.found.name),r=r5(t.found.updateTime),i=t.found.createTime?r5(t.found.createTime):j.min(),s=new nc({mapValue:{fields:t.found.fields}});return nd.newFoundDocument(n,r,i,s)}(n,t):"missing"in t?function(e,t){N(!!t.missing,3894),N(!!t.readTime,22933);let n=r9(e,t.missing),r=r5(t.readTime);return nd.newNoDocument(n,r)}(n,t):S(7234,{result:t}));i.set(r.key.toString(),r)});let s=[];return t.forEach(e=>{let t=i.get(e.toString());N(!!t,55234,{key:e}),s.push(t)}),s}(this.datastore,e);return t.forEach(e=>this.recordVersion(e)),t}set(e,t){this.write(t.toMutation(e,this.precondition(e))),this.writtenDocs.add(e.toString())}update(e,t){try{this.write(t.toMutation(e,this.preconditionForUpdate(e)))}catch(e){this.lastTransactionError=e}this.writtenDocs.add(e.toString())}delete(e){this.write(new rC(e,this.precondition(e))),this.writtenDocs.add(e.toString())}async commit(){if(this.ensureCommitNotCalled(),this.lastTransactionError)throw this.lastTransactionError;let e=this.readVersions;this.mutations.forEach(t=>{e.delete(t.key.toString())}),e.forEach((e,t)=>{let n=X.fromPath(t);this.mutations.push(new rD(n,this.precondition(n)))}),await async function(e,t){let n={writes:t.map(t=>ia(e.serializer,t))};await e.Qo("Commit",e.serializer.databaseId,H.emptyPath(),n)}(this.datastore,this.mutations),this.committed=!0}recordVersion(e){let t;if(e.isFoundDocument())t=e.version;else{if(!e.isNoDocument())throw S(50498,{xu:e.constructor.name});t=j.min()}let n=this.readVersions.get(e.key.toString());if(n){if(!t.isEqual(n))throw new D(C.ABORTED,"Document version changed between two reads.")}else this.readVersions.set(e.key.toString(),t)}precondition(e){let t=this.readVersions.get(e.toString());return!this.writtenDocs.has(e.toString())&&t?t.isEqual(j.min())?ry.exists(!1):ry.updateTime(t):ry.none()}preconditionForUpdate(e){let t=this.readVersions.get(e.toString());if(!this.writtenDocs.has(e.toString())&&t){if(t.isEqual(j.min()))throw new D(C.INVALID_ARGUMENT,"Can't update a document that doesn't exist.");return ry.updateTime(t)}return ry.exists(!0)}write(e){this.ensureCommitNotCalled(),this.mutations.push(e)}ensureCommitNotCalled(){}}class oQ{constructor(e,t,n,r,i){this.asyncQueue=e,this.datastore=t,this.options=n,this.updateFunction=r,this.deferred=i,this.Ou=n.maxAttempts,this.y_=new af(this.asyncQueue,"transaction_retry")}Nu(){this.Ou-=1,this.Bu()}Bu(){this.y_.E_(async()=>{let e=new oj(this.datastore),t=this.Lu(e);t&&t.then(t=>{this.asyncQueue.enqueueAndForget(()=>e.commit().then(()=>{this.deferred.resolve(t)}).catch(e=>{this.ku(e)}))}).catch(e=>{this.ku(e)})})}Lu(e){try{let t=this.updateFunction(e);return!eN(t)&&t.catch&&t.then?t:(this.deferred.reject(Error("Transaction callback must return a Promise")),null)}catch(e){return this.deferred.reject(e),null}}ku(e){this.Ou>0&&this.qu(e)?(this.Ou-=1,this.asyncQueue.enqueueAndForget(()=>(this.Bu(),Promise.resolve()))):this.deferred.reject(e)}qu(e){if("FirebaseError"===e.name){let t=e.code;return"aborted"===t||"failed-precondition"===t||"already-exists"===t||!rM(t)}return!1}}let oW="FirestoreClient";class oH{constructor(e,t,n,r,i){this.authCredentials=e,this.appCheckCredentials=t,this.asyncQueue=n,this.databaseInfo=r,this.user=y.UNAUTHENTICATED,this.clientId=q.newId(),this.authCredentialListener=()=>Promise.resolve(),this.appCheckCredentialListener=()=>Promise.resolve(),this._uninitializedComponentsProvider=i,this.authCredentials.start(n,async e=>{T(oW,"Received user=",e.uid),await this.authCredentialListener(e),this.user=e}),this.appCheckCredentials.start(n,e=>(T(oW,"Received new app check token=",e),this.appCheckCredentialListener(e,this.user)))}get configuration(){return{asyncQueue:this.asyncQueue,databaseInfo:this.databaseInfo,clientId:this.clientId,authCredentials:this.authCredentials,appCheckCredentials:this.appCheckCredentials,initialUser:this.user,maxConcurrentLimboResolutions:100}}setCredentialChangeListener(e){this.authCredentialListener=e}setAppCheckTokenChangeListener(e){this.appCheckCredentialListener=e}terminate(){this.asyncQueue.enterRestrictedMode();let e=new A;return this.asyncQueue.enqueueAndForgetEvenWhileRestricted(async()=>{try{this._onlineComponents&&await this._onlineComponents.terminate(),this._offlineComponents&&await this._offlineComponents.terminate(),this.authCredentials.shutdown(),this.appCheckCredentials.shutdown(),e.resolve()}catch(n){let t=aY(n,"Failed to shutdown persistence");e.reject(t)}}),e.promise}}async function oY(e,t){e.asyncQueue.verifyOperationInProgress(),T(oW,"Initializing OfflineComponentProvider");let n=e.configuration;await t.initialize(n);let r=n.initialUser;e.setCredentialChangeListener(async e=>{r.isEqual(e)||(await sP(t.localStore,e),r=e)}),t.persistence.setDatabaseDeletedListener(()=>e.terminate()),e._offlineComponents=t}async function oJ(e,t){e.asyncQueue.verifyOperationInProgress();let n=await oX(e);T(oW,"Initializing OnlineComponentProvider"),await t.initialize(n,e.configuration),e.setCredentialChangeListener(e=>aG(t.remoteStore,e)),e.setAppCheckTokenChangeListener((e,n)=>aG(t.remoteStore,n)),e._onlineComponents=t}async function oX(e){if(!e._offlineComponents)if(e._uninitializedComponentsProvider){T(oW,"Using user provided OfflineComponentProvider");try{await oY(e,e._uninitializedComponentsProvider._offline)}catch(t){if(!("FirebaseError"===t.name?t.code===C.FAILED_PRECONDITION||t.code===C.UNIMPLEMENTED:!("undefined"!=typeof DOMException&&t instanceof DOMException)||22===t.code||20===t.code||11===t.code))throw t;b("Error using user provided cache. Falling back to memory cache: "+t),await oY(e,new oq)}}else T(oW,"Using default OfflineComponentProvider"),await oY(e,new oB(void 0));return e._offlineComponents}async function oZ(e){return e._onlineComponents||(e._uninitializedComponentsProvider?(T(oW,"Using user provided OnlineComponentProvider"),await oJ(e,e._uninitializedComponentsProvider._online)):(T(oW,"Using default OnlineComponentProvider"),await oJ(e,new o$))),e._onlineComponents}async function o0(e){let t=await oZ(e),n=t.eventManager;return n.onListen=ol.bind(null,t.syncEngine),n.onUnlisten=od.bind(null,t.syncEngine),n.onFirstRemoteStoreListen=ou.bind(null,t.syncEngine),n.onLastRemoteStoreUnlisten=of.bind(null,t.syncEngine),n}function o1(e){let t={};return void 0!==e.timeoutSeconds&&(t.timeoutSeconds=e.timeoutSeconds),t}let o2=new Map;function o5(e,t,n){if(!n)throw new D(C.INVALID_ARGUMENT,`Function ${e}() cannot be called with an empty ${t}.`)}function o4(e){if(!X.isDocumentKey(e))throw new D(C.INVALID_ARGUMENT,`Invalid document reference. Document references must have an even number of segments, but ${e} has ${e.length}.`)}function o3(e){if(X.isDocumentKey(e))throw new D(C.INVALID_ARGUMENT,`Invalid collection reference. Collection references must have an odd number of segments, but ${e} has ${e.length}.`)}function o6(e){if(void 0===e)return"undefined";if(null===e)return"null";if("string"==typeof e)return e.length>20&&(e=`${e.substring(0,20)}...`),JSON.stringify(e);if("number"==typeof e||"boolean"==typeof e)return""+e;if("object"==typeof e){if(e instanceof Array)return"an array";{var t;let n=(t=e).constructor?t.constructor.name:null;return n?`a custom ${n} object`:"an object"}}return"function"==typeof e?"a function":S(12329,{type:typeof e})}function o8(e,t){if("_delegate"in e&&(e=e._delegate),!(e instanceof t)){if(t.name===e.constructor.name)throw new D(C.INVALID_ARGUMENT,"Type does not match the expected instance. Did you pass a reference from a different Firestore SDK?");{let n=o6(e);throw new D(C.INVALID_ARGUMENT,`Expected type '${t.name}', but it was: ${n}`)}}return e}let o9="firestore.googleapis.com";class o7{constructor(e){var t,n;if(void 0===e.host){if(void 0!==e.ssl)throw new D(C.INVALID_ARGUMENT,"Can't provide ssl option if host option is not set");this.host=o9,this.ssl=!0}else this.host=e.host,this.ssl=null==(t=e.ssl)||t;if(this.credentials=e.credentials,this.ignoreUndefinedProperties=!!e.ignoreUndefinedProperties,this.localCache=e.localCache,void 0===e.cacheSizeBytes)this.cacheSizeBytes=0x2800000;else{if(-1!==e.cacheSizeBytes&&e.cacheSizeBytes<1048576)throw new D(C.INVALID_ARGUMENT,"cacheSizeBytes must be at least 1048576");this.cacheSizeBytes=e.cacheSizeBytes}(function(e,t,n,r){if(!0===t&&!0===r)throw new D(C.INVALID_ARGUMENT,`${e} and ${n} cannot be used together.`)})("experimentalForceLongPolling",e.experimentalForceLongPolling,"experimentalAutoDetectLongPolling",e.experimentalAutoDetectLongPolling),this.experimentalForceLongPolling=!!e.experimentalForceLongPolling,this.experimentalForceLongPolling?this.experimentalAutoDetectLongPolling=!1:void 0===e.experimentalAutoDetectLongPolling?this.experimentalAutoDetectLongPolling=!0:this.experimentalAutoDetectLongPolling=!!e.experimentalAutoDetectLongPolling,this.experimentalLongPollingOptions=o1(null!=(n=e.experimentalLongPollingOptions)?n:{}),function(e){if(void 0!==e.timeoutSeconds){if(isNaN(e.timeoutSeconds))throw new D(C.INVALID_ARGUMENT,`invalid long polling timeout: ${e.timeoutSeconds} (must not be NaN)`);if(e.timeoutSeconds<5)throw new D(C.INVALID_ARGUMENT,`invalid long polling timeout: ${e.timeoutSeconds} (minimum allowed value is 5)`);if(e.timeoutSeconds>30)throw new D(C.INVALID_ARGUMENT,`invalid long polling timeout: ${e.timeoutSeconds} (maximum allowed value is 30)`)}}(this.experimentalLongPollingOptions),this.useFetchStreams=!!e.useFetchStreams}isEqual(e){var t,n;return this.host===e.host&&this.ssl===e.ssl&&this.credentials===e.credentials&&this.cacheSizeBytes===e.cacheSizeBytes&&this.experimentalForceLongPolling===e.experimentalForceLongPolling&&this.experimentalAutoDetectLongPolling===e.experimentalAutoDetectLongPolling&&(t=this.experimentalLongPollingOptions,n=e.experimentalLongPollingOptions,t.timeoutSeconds===n.timeoutSeconds)&&this.ignoreUndefinedProperties===e.ignoreUndefinedProperties&&this.useFetchStreams===e.useFetchStreams}}class le{constructor(e,t,n,r){this._authCredentials=e,this._appCheckCredentials=t,this._databaseId=n,this._app=r,this.type="firestore-lite",this._persistenceKey="(lite)",this._settings=new o7({}),this._settingsFrozen=!1,this._emulatorOptions={},this._terminateTask="notTerminated"}get app(){if(!this._app)throw new D(C.FAILED_PRECONDITION,"Firestore was not initialized using the Firebase SDK. 'app' is not available");return this._app}get _initialized(){return this._settingsFrozen}get _terminated(){return"notTerminated"!==this._terminateTask}_setSettings(e){if(this._settingsFrozen)throw new D(C.FAILED_PRECONDITION,"Firestore has already been started and its settings can no longer be changed. You can only modify settings before calling any other methods on a Firestore object.");this._settings=new o7(e),this._emulatorOptions=e.emulatorOptions||{},void 0!==e.credentials&&(this._authCredentials=function(e){if(!e)return new R;switch(e.type){case"firstParty":return new F(e.sessionIndex||"0",e.iamToken||null,e.authTokenFactory||null);case"provider":return e.client;default:throw new D(C.INVALID_ARGUMENT,"makeAuthCredentialsProvider failed due to invalid credential type")}}(e.credentials))}_getSettings(){return this._settings}_getEmulatorOptions(){return this._emulatorOptions}_freezeSettings(){return this._settingsFrozen=!0,this._settings}_delete(){return"notTerminated"===this._terminateTask&&(this._terminateTask=this._terminate()),this._terminateTask}async _restart(){"notTerminated"===this._terminateTask?await this._terminate():this._terminateTask="notTerminated"}toJSON(){return{app:this._app,databaseId:this._databaseId,settings:this._settings}}_terminate(){return function(e){let t=o2.get(e);t&&(T("ComponentProvider","Removing Datastore"),o2.delete(e),t.terminate())}(this),Promise.resolve()}}class lt{constructor(e,t,n){this.converter=t,this._query=n,this.type="query",this.firestore=e}withConverter(e){return new lt(this.firestore,e,this._query)}}class ln{constructor(e,t,n){this.converter=t,this._key=n,this.type="document",this.firestore=e}get _path(){return this._key.path}get id(){return this._key.path.lastSegment()}get path(){return this._key.path.canonicalString()}get parent(){return new lr(this.firestore,this.converter,this._key.path.popLast())}withConverter(e){return new ln(this.firestore,e,this._key)}}class lr extends lt{constructor(e,t,n){super(e,t,nz(n)),this._path=n,this.type="collection"}get id(){return this._query.path.lastSegment()}get path(){return this._query.path.canonicalString()}get parent(){let e=this._path.popLast();return e.isEmpty()?null:new ln(this.firestore,null,new X(e))}withConverter(e){return new lr(this.firestore,e,this._path)}}function li(e,t,...n){if(e=(0,h.Ku)(e),o5("collection","path",t),e instanceof le){let r=H.fromString(t,...n);return o3(r),new lr(e,null,r)}{if(!(e instanceof ln||e instanceof lr))throw new D(C.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore");let r=e._path.child(H.fromString(t,...n));return o3(r),new lr(e.firestore,null,r)}}function ls(e,t,...n){if(e=(0,h.Ku)(e),1==arguments.length&&(t=q.newId()),o5("doc","path",t),e instanceof le){let r=H.fromString(t,...n);return o4(r),new ln(e,null,new X(r))}{if(!(e instanceof ln||e instanceof lr))throw new D(C.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore");let r=e._path.child(H.fromString(t,...n));return o4(r),new ln(e.firestore,e instanceof lr?e.converter:null,new X(r))}}let la="AsyncQueue";class lo{constructor(e=Promise.resolve()){this.Qu=[],this.$u=!1,this.Uu=[],this.Ku=null,this.Wu=!1,this.Gu=!1,this.zu=[],this.y_=new af(this,"async_queue_retry"),this.ju=()=>{let e=ac();e&&T(la,"Visibility state changed to "+e.visibilityState),this.y_.A_()},this.Hu=e;let t=ac();t&&"function"==typeof t.addEventListener&&t.addEventListener("visibilitychange",this.ju)}get isShuttingDown(){return this.$u}enqueueAndForget(e){this.enqueue(e)}enqueueAndForgetEvenWhileRestricted(e){this.Ju(),this.Yu(e)}enterRestrictedMode(e){if(!this.$u){this.$u=!0,this.Gu=e||!1;let t=ac();t&&"function"==typeof t.removeEventListener&&t.removeEventListener("visibilitychange",this.ju)}}enqueue(e){if(this.Ju(),this.$u)return new Promise(()=>{});let t=new A;return this.Yu(()=>this.$u&&this.Gu?Promise.resolve():(e().then(t.resolve,t.reject),t.promise)).then(()=>t.promise)}enqueueRetryable(e){this.enqueueAndForget(()=>(this.Qu.push(e),this.Zu()))}async Zu(){if(0!==this.Qu.length){try{await this.Qu[0](),this.Qu.shift(),this.y_.reset()}catch(e){if(!ew(e))throw e;T(la,"Operation failed with retryable error: "+e)}this.Qu.length>0&&this.y_.E_(()=>this.Zu())}}Yu(e){let t=this.Hu.then(()=>(this.Wu=!0,e().catch(e=>{throw this.Ku=e,this.Wu=!1,E("INTERNAL UNHANDLED ERROR: ",ll(e)),e}).then(e=>(this.Wu=!1,e))));return this.Hu=t,t}enqueueAfterDelay(e,t,n){this.Ju(),this.zu.indexOf(e)>-1&&(t=0);let r=aH.createAndSchedule(this,e,t,n,e=>this.Xu(e));return this.Uu.push(r),r}Ju(){this.Ku&&S(47125,{ec:ll(this.Ku)})}verifyOperationInProgress(){}async tc(){let e;do e=this.Hu,await e;while(e!==this.Hu)}nc(e){for(let t of this.Uu)if(t.timerId===e)return!0;return!1}rc(e){return this.tc().then(()=>{for(let t of(this.Uu.sort((e,t)=>e.targetTimeMs-t.targetTimeMs),this.Uu))if(t.skipDelay(),"all"!==e&&t.timerId===e)break;return this.tc()})}sc(e){this.zu.push(e)}Xu(e){let t=this.Uu.indexOf(e);this.Uu.splice(t,1)}}function ll(e){let t=e.message||"";return e.stack&&(t=e.stack.includes(e.message)?e.stack:e.message+"\n"+e.stack),t}class lu extends le{constructor(e,t,n,r){super(e,t,n,r),this.type="firestore",this._queue=new lo,this._persistenceKey=(null==r?void 0:r.name)||"[DEFAULT]"}async _terminate(){if(this._firestoreClient){let e=this._firestoreClient.terminate();this._queue=new lo(e),this._firestoreClient=void 0,await e}}}function lh(e,t){let n="object"==typeof e?e:(0,o.Sx)(),r=(0,o.j6)(n,"firestore").getImmediate({identifier:"string"==typeof e?e:t||tW});if(!r._initialized){let e=(0,h.yU)("firestore");e&&function(e,t,n,r={}){var i;let s=(e=o8(e,le))._getSettings(),a=Object.assign(Object.assign({},s),{emulatorOptions:e._getEmulatorOptions()}),o=`${t}:${n}`;s.host!==o9&&s.host!==o&&b("Host has been set in both settings() and connectFirestoreEmulator(), emulator host will be used.");let l=Object.assign(Object.assign({},s),{host:o,ssl:!1,emulatorOptions:r});if(!(0,h.bD)(l,a)&&(e._setSettings(l),r.mockUserToken)){let t,n;if("string"==typeof r.mockUserToken)t=r.mockUserToken,n=y.MOCK_USER;else{t=(0,h.Fy)(r.mockUserToken,null==(i=e._app)?void 0:i.options.projectId);let s=r.mockUserToken.sub||r.mockUserToken.user_id;if(!s)throw new D(C.INVALID_ARGUMENT,"mockUserToken must contain 'sub' or 'user_id' field!");n=new y(s)}e._authCredentials=new V(new k(t,n))}}(r,...e)}return r}function lc(e){if(e._terminated)throw new D(C.FAILED_PRECONDITION,"The client has already been terminated.");return e._firestoreClient||ld(e),e._firestoreClient}function ld(e){var t,n,r,i,s;let a=e._freezeSettings(),o=(i=e._databaseId,s=(null==(t=e._app)?void 0:t.options.appId)||"",new tQ(i,s,e._persistenceKey,a.host,a.ssl,a.experimentalForceLongPolling,a.experimentalAutoDetectLongPolling,o1(a.experimentalLongPollingOptions),a.useFetchStreams));e._componentsProvider||(null==(n=a.localCache)?void 0:n._offlineComponentProvider)&&(null==(r=a.localCache)?void 0:r._onlineComponentProvider)&&(e._componentsProvider={_offline:a.localCache._offlineComponentProvider,_online:a.localCache._onlineComponentProvider}),e._firestoreClient=new oH(e._authCredentials,e._appCheckCredentials,e._queue,o,e._componentsProvider&&function(e){let t=null==e?void 0:e._online.build();return{_offline:null==e?void 0:e._offline.build(t),_online:t}}(e._componentsProvider))}class lf{constructor(e){this._byteString=e}static fromBase64String(e){try{return new lf(tO.fromBase64String(e))}catch(e){throw new D(C.INVALID_ARGUMENT,"Failed to construct data from Base64 string: "+e)}}static fromUint8Array(e){return new lf(tO.fromUint8Array(e))}toBase64(){return this._byteString.toBase64()}toUint8Array(){return this._byteString.toUint8Array()}toString(){return"Bytes(base64: "+this.toBase64()+")"}isEqual(e){return this._byteString.isEqual(e._byteString)}}class lm{constructor(...e){for(let t=0;t<e.length;++t)if(0===e[t].length)throw new D(C.INVALID_ARGUMENT,"Invalid field name at argument $(i + 1). Field names must not be empty.");this._internalPath=new J(e)}isEqual(e){return this._internalPath.isEqual(e._internalPath)}}class lg{constructor(e){this._methodName=e}}class lp{constructor(e,t){if(!isFinite(e)||e<-90||e>90)throw new D(C.INVALID_ARGUMENT,"Latitude must be a number between -90 and 90, but was: "+e);if(!isFinite(t)||t<-180||t>180)throw new D(C.INVALID_ARGUMENT,"Longitude must be a number between -180 and 180, but was: "+t);this._lat=e,this._long=t}get latitude(){return this._lat}get longitude(){return this._long}isEqual(e){return this._lat===e._lat&&this._long===e._long}toJSON(){return{latitude:this._lat,longitude:this._long}}_compareTo(e){return B(this._lat,e._lat)||B(this._long,e._long)}}class ly{constructor(e){this._values=(e||[]).map(e=>e)}toArray(){return this._values.map(e=>e)}isEqual(e){return function(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(e[n]!==t[n])return!1;return!0}(this._values,e._values)}}let lw=/^__.*__$/;class lv{constructor(e,t,n){this.data=e,this.fieldMask=t,this.fieldTransforms=n}toMutation(e,t){return null!==this.fieldMask?new r_(e,this.data,this.fieldMask,t,this.fieldTransforms):new rb(e,this.data,t,this.fieldTransforms)}}class lI{constructor(e,t,n){this.data=e,this.fieldMask=t,this.fieldTransforms=n}toMutation(e,t){return new r_(e,this.data,this.fieldMask,t,this.fieldTransforms)}}function lT(e){switch(e){case 0:case 2:case 1:return!0;case 3:case 4:return!1;default:throw S(40011,{oc:e})}}class lE{constructor(e,t,n,r,i,s){this.settings=e,this.databaseId=t,this.serializer=n,this.ignoreUndefinedProperties=r,void 0===i&&this._c(),this.fieldTransforms=i||[],this.fieldMask=s||[]}get path(){return this.settings.path}get oc(){return this.settings.oc}ac(e){return new lE(Object.assign(Object.assign({},this.settings),e),this.databaseId,this.serializer,this.ignoreUndefinedProperties,this.fieldTransforms,this.fieldMask)}uc(e){var t;let n=null==(t=this.path)?void 0:t.child(e),r=this.ac({path:n,cc:!1});return r.lc(e),r}hc(e){var t;let n=null==(t=this.path)?void 0:t.child(e),r=this.ac({path:n,cc:!1});return r._c(),r}Pc(e){return this.ac({path:void 0,cc:!0})}Tc(e){return lz(e,this.settings.methodName,this.settings.Ic||!1,this.path,this.settings.Ec)}contains(e){return void 0!==this.fieldMask.find(t=>e.isPrefixOf(t))||void 0!==this.fieldTransforms.find(t=>e.isPrefixOf(t.field))}_c(){if(this.path)for(let e=0;e<this.path.length;e++)this.lc(this.path.get(e))}lc(e){if(0===e.length)throw this.Tc("Document fields must not be empty");if(lT(this.oc)&&lw.test(e))throw this.Tc('Document fields cannot begin and end with "__"')}}class lb{constructor(e,t,n){this.databaseId=e,this.ignoreUndefinedProperties=t,this.serializer=n||ad(e)}dc(e,t,n,r=!1){return new lE({oc:e,methodName:t,Ec:n,path:J.emptyPath(),cc:!1,Ic:r},this.databaseId,this.serializer,this.ignoreUndefinedProperties)}}function l_(e){let t=e._freezeSettings(),n=ad(e._databaseId);return new lb(e._databaseId,!!t.ignoreUndefinedProperties,n)}function lS(e,t,n,r,i,s={}){let a,o,l=e.dc(s.merge||s.mergeFields?2:0,t,n,i);lP("Data must be an object, but it was:",l,r);let u=lF(r,l);if(s.merge)a=new tV(l.fieldMask),o=l.fieldTransforms;else if(s.mergeFields){let e=[];for(let r of s.mergeFields){let i=lU(t,r,n);if(!l.contains(i))throw new D(C.INVALID_ARGUMENT,`Field '${i}' is specified in your field mask but missing from your input data.`);lK(e,i)||e.push(i)}a=new tV(e),o=l.fieldTransforms.filter(e=>a.covers(e.field))}else a=null,o=l.fieldTransforms;return new lv(new nc(u),a,o)}class lx extends lg{_toFieldTransform(e){if(2!==e.oc)throw 1===e.oc?e.Tc(`${this._methodName}() can only appear at the top level of your update data`):e.Tc(`${this._methodName}() cannot be used with set() unless you pass {merge:true}`);return e.fieldMask.push(e.path),null}isEqual(e){return e instanceof lx}}function lN(e,t,n){return new lE({oc:3,Ec:t.settings.Ec,methodName:e._methodName,cc:n},t.databaseId,t.serializer,t.ignoreUndefinedProperties)}class lC extends null{_toFieldTransform(e){return new rg(e.path,new ro)}isEqual(e){return e instanceof lC}}class lD extends lg{constructor(e,t){super(e),this.Ac=t}_toFieldTransform(e){let t=lN(this,e,!0),n=new rl(this.Ac.map(e=>lO(e,t)));return new rg(e.path,n)}isEqual(e){return e instanceof lD&&(0,h.bD)(this.Ac,e.Ac)}}class lA extends lg{constructor(e,t){super(e),this.Ac=t}_toFieldTransform(e){let t=lN(this,e,!0),n=new rh(this.Ac.map(e=>lO(e,t)));return new rg(e.path,n)}isEqual(e){return e instanceof lA&&(0,h.bD)(this.Ac,e.Ac)}}class lk extends lg{constructor(e,t){super(e),this.Rc=t}_toFieldTransform(e){let t=new rd(e.serializer,ri(e.serializer,this.Rc));return new rg(e.path,t)}isEqual(e){return e instanceof lk&&this.Rc===e.Rc}}function lR(e,t,n,r){let i=e.dc(1,t,n);lP("Data must be an object, but it was:",i,r);let s=[],a=nc.empty();return tS(r,(e,r)=>{let o=lB(t,e,n);r=(0,h.Ku)(r);let l=i.hc(o);if(r instanceof lx)s.push(o);else{let e=lO(r,l);null!=e&&(s.push(o),a.set(o,e))}}),new lI(a,new tV(s),i.fieldTransforms)}function lV(e,t,n,r,i,s){let a=e.dc(1,t,n),o=[lU(t,r,n)],l=[i];if(s.length%2!=0)throw new D(C.INVALID_ARGUMENT,`Function ${t}() needs to be called with an even number of arguments that alternate between field names and values.`);for(let e=0;e<s.length;e+=2)o.push(lU(t,s[e])),l.push(s[e+1]);let u=[],c=nc.empty();for(let e=o.length-1;e>=0;--e)if(!lK(u,o[e])){let t=o[e],n=l[e];n=(0,h.Ku)(n);let r=a.hc(t);if(n instanceof lx)u.push(t);else{let e=lO(n,r);null!=e&&(u.push(t),c.set(t,e))}}return new lI(c,new tV(u),a.fieldTransforms)}function lM(e,t,n,r=!1){return lO(n,e.dc(r?4:3,t))}function lO(e,t){if(lL(e=(0,h.Ku)(e)))return lP("Unsupported field value:",t,e),lF(e,t);if(e instanceof lg)return function(e,t){if(!lT(t.oc))throw t.Tc(`${e._methodName}() can only be used with update() and set()`);if(!t.path)throw t.Tc(`${e._methodName}() is not currently supported inside arrays`);let n=e._toFieldTransform(t);n&&t.fieldTransforms.push(n)}(e,t),null;if(void 0===e&&t.ignoreUndefinedProperties)return null;if(t.path&&t.fieldMask.push(t.path),e instanceof Array){if(t.settings.cc&&4!==t.oc)throw t.Tc("Nested arrays are not supported");let n=[],r=0;for(let i of e){let e=lO(i,t.Pc(r));null==e&&(e={nullValue:"NULL_VALUE"}),n.push(e),r++}return{arrayValue:{values:n}}}return function(e,t){if(null===(e=(0,h.Ku)(e)))return{nullValue:"NULL_VALUE"};if("number"==typeof e)return ri(t.serializer,e);if("boolean"==typeof e)return{booleanValue:e};if("string"==typeof e)return{stringValue:e};if(e instanceof Date){let n=G.fromDate(e);return{timestampValue:r1(t.serializer,n)}}if(e instanceof G){let n=new G(e.seconds,1e3*Math.floor(e.nanoseconds/1e3));return{timestampValue:r1(t.serializer,n)}}if(e instanceof lp)return{geoPointValue:{latitude:e.latitude,longitude:e.longitude}};if(e instanceof lf)return{bytesValue:r2(t.serializer,e._byteString)};if(e instanceof ln){let n=t.databaseId,r=e.firestore._databaseId;if(!r.isEqual(n))throw t.Tc(`Document reference is for database ${r.projectId}/${r.database} but should be for database ${n.projectId}/${n.database}`);return{referenceValue:r4(e.firestore._databaseId||t.databaseId,e._key.path)}}if(e instanceof ly)return{mapValue:{fields:{[tY]:{stringValue:tZ},[t0]:{arrayValue:{values:e.toArray().map(e=>{if("number"!=typeof e)throw t.Tc("VectorValues must only contain numeric values.");return rn(t.serializer,e)})}}}}};throw t.Tc(`Unsupported field value: ${o6(e)}`)}(e,t)}function lF(e,t){let n={};return tx(e)?t.path&&t.path.length>0&&t.fieldMask.push(t.path):tS(e,(e,r)=>{let i=lO(r,t.uc(e));null!=i&&(n[e]=i)}),{mapValue:{fields:n}}}function lL(e){return!("object"!=typeof e||null===e||e instanceof Array||e instanceof Date||e instanceof G||e instanceof lp||e instanceof lf||e instanceof ln||e instanceof lg||e instanceof ly)}function lP(e,t,n){if(!lL(n)||"object"!=typeof n||null===n||Object.getPrototypeOf(n)!==Object.prototype&&null!==Object.getPrototypeOf(n)){let r=o6(n);throw"an object"===r?t.Tc(e+" a custom object"):t.Tc(e+" "+r)}}function lU(e,t,n){if((t=(0,h.Ku)(t))instanceof lm)return t._internalPath;if("string"==typeof t)return lB(e,t);throw lz("Field path arguments must be of type string or ",e,!1,void 0,n)}let lq=RegExp("[~\\*/\\[\\]]");function lB(e,t,n){if(t.search(lq)>=0)throw lz(`Invalid field path (${t}). Paths must not contain '~', '*', '/', '[', or ']'`,e,!1,void 0,n);try{return new lm(...t.split("."))._internalPath}catch(r){throw lz(`Invalid field path (${t}). Paths must not be empty, begin with '.', end with '.', or contain '..'`,e,!1,void 0,n)}}function lz(e,t,n,r,i){let s=r&&!r.isEmpty(),a=void 0!==i,o=`Function ${t}() called with invalid data`;n&&(o+=" (via `toFirestore()`)"),o+=". ";let l="";return(s||a)&&(l+=" (found",s&&(l+=` in field ${r}`),a&&(l+=` in document ${i}`),l+=")"),new D(C.INVALID_ARGUMENT,o+e+l)}function lK(e,t){return e.some(e=>e.isEqual(t))}class l${constructor(e,t,n,r,i){this._firestore=e,this._userDataWriter=t,this._key=n,this._document=r,this._converter=i}get id(){return this._key.path.lastSegment()}get ref(){return new ln(this._firestore,this._converter,this._key)}exists(){return null!==this._document}data(){if(this._document){if(this._converter){let e=new lG(this._firestore,this._userDataWriter,this._key,this._document,null);return this._converter.fromFirestore(e)}return this._userDataWriter.convertValue(this._document.data.value)}}get(e){if(this._document){let t=this._document.data.field(lj("DocumentSnapshot.get",e));if(null!==t)return this._userDataWriter.convertValue(t)}}}class lG extends l${data(){return super.data()}}function lj(e,t){return"string"==typeof t?lB(e,t):t instanceof lm?t._internalPath:t._delegate._internalPath}class lQ{}class lW extends lQ{}function lH(e,t,...n){let r=[];for(let i of(t instanceof lQ&&r.push(t),function(e){let t=e.filter(e=>e instanceof lX).length,n=e.filter(e=>e instanceof lY).length;if(t>1||t>0&&n>0)throw new D(C.INVALID_ARGUMENT,"InvalidQuery. When using composite filters, you cannot use more than one filter at the top level. Consider nesting the multiple filters within an `and(...)` statement. For example: change `query(query, where(...), or(...))` to `query(query, and(where(...), or(...)))`.")}(r=r.concat(n)),r))e=i._apply(e);return e}class lY extends lW{constructor(e,t,n){super(),this._field=e,this._op=t,this._value=n,this.type="where"}static _create(e,t,n){return new lY(e,t,n)}_apply(e){let t=this._parse(e);return l7(e._query,t),new lt(e.firestore,e.converter,nW(e._query,t))}_parse(e){let t=l_(e.firestore);return function(e,t,n,r,i,s,a){let o;if(i.isKeyField()){if("array-contains"===s||"array-contains-any"===s)throw new D(C.INVALID_ARGUMENT,`Invalid Query. You can't perform '${s}' queries on documentId().`);if("in"===s||"not-in"===s){l9(a,s);let t=[];for(let n of a)t.push(l8(r,e,n));o={arrayValue:{values:t}}}else o=l8(r,e,a)}else"in"!==s&&"not-in"!==s&&"array-contains-any"!==s||l9(a,s),o=lM(n,t,a,"in"===s||"not-in"===s);return nw.create(i,s,o)}(e._query,"where",t,e.firestore._databaseId,this._field,this._op,this._value)}}function lJ(e,t,n){let r=lj("where",e);return lY._create(r,t,n)}class lX extends lQ{constructor(e,t){super(),this.type=e,this._queryConstraints=t}static _create(e,t){return new lX(e,t)}_parse(e){let t=this._queryConstraints.map(t=>t._parse(e)).filter(e=>e.getFilters().length>0);return 1===t.length?t[0]:nv.create(t,this._getOperator())}_apply(e){let t=this._parse(e);return 0===t.getFilters().length?e:(function(e,t){let n=e;for(let e of t.getFlattenedFilters())l7(n,e),n=nW(n,e)}(e._query,t),new lt(e.firestore,e.converter,nW(e._query,t)))}_getQueryConstraints(){return this._queryConstraints}_getOperator(){return"and"===this.type?"and":"or"}}class lZ extends lW{constructor(e,t){super(),this._field=e,this._direction=t,this.type="orderBy"}static _create(e,t){return new lZ(e,t)}_apply(e){let t=function(e,t,n){if(null!==e.startAt)throw new D(C.INVALID_ARGUMENT,"Invalid query. You must not call startAt() or startAfter() before calling orderBy().");if(null!==e.endAt)throw new D(C.INVALID_ARGUMENT,"Invalid query. You must not call endAt() or endBefore() before calling orderBy().");return new np(t,n)}(e._query,this._field,this._direction);return new lt(e.firestore,e.converter,function(e,t){let n=e.explicitOrderBy.concat([t]);return new nB(e.path,e.collectionGroup,n,e.filters.slice(),e.limit,e.limitType,e.startAt,e.endAt)}(e._query,t))}}function l0(e,t="asc"){let n=lj("orderBy",e);return lZ._create(n,t)}class l1 extends lW{constructor(e,t,n){super(),this.type=e,this._limit=t,this._limitType=n}static _create(e,t,n){return new l1(e,t,n)}_apply(e){return new lt(e.firestore,e.converter,nH(e._query,this._limit,this._limitType))}}function l2(e){return function(e,t){if(t<=0)throw new D(C.INVALID_ARGUMENT,`Function ${e}() requires a positive number, but it was: ${t}.`)}("limit",e),l1._create("limit",e,"F")}class l5 extends lW{constructor(e,t,n){super(),this.type=e,this._docOrFields=t,this._inclusive=n}static _create(e,t,n){return new l5(e,t,n)}_apply(e){var t;let n=l6(e,this.type,this._docOrFields,this._inclusive);return new lt(e.firestore,e.converter,(t=e._query,new nB(t.path,t.collectionGroup,t.explicitOrderBy.slice(),t.filters.slice(),t.limit,t.limitType,n,t.endAt)))}}function l4(...e){return l5._create("startAfter",e,!1)}class l3 extends lW{constructor(e,t,n){super(),this.type=e,this._docOrFields=t,this._inclusive=n}static _create(e,t,n){return new l3(e,t,n)}_apply(e){var t;let n=l6(e,this.type,this._docOrFields,this._inclusive);return new lt(e.firestore,e.converter,(t=e._query,new nB(t.path,t.collectionGroup,t.explicitOrderBy.slice(),t.filters.slice(),t.limit,t.limitType,t.startAt,n)))}}function l6(e,t,n,r){if(n[0]=(0,h.Ku)(n[0]),n[0]instanceof l$)return function(e,t,n,r,i){if(!r)throw new D(C.NOT_FOUND,`Can't use a DocumentSnapshot that doesn't exist for ${n}().`);let s=[];for(let n of nG(e))if(n.field.isKeyField())s.push(t7(t,r.key));else{let e=r.data.field(n.field);if(t$(e))throw new D(C.INVALID_ARGUMENT,'Invalid query. You are trying to start or end a query using a document for which the field "'+n.field+'" is an uncommitted server timestamp. (Since the value of this field is unknown, you cannot start/end a query with it.)');if(null===e){let e=n.field.canonicalString();throw new D(C.INVALID_ARGUMENT,`Invalid query. You are trying to start or end a query using a document for which the field '${e}' (used as the orderBy) does not exist.`)}s.push(e)}return new nf(s,i)}(e._query,e.firestore._databaseId,t,n[0]._document,r);{let i=l_(e.firestore);return function(e,t,n,r,i,s){let a=e.explicitOrderBy;if(i.length>a.length)throw new D(C.INVALID_ARGUMENT,`Too many arguments provided to ${r}(). The number of arguments must be less than or equal to the number of orderBy() clauses`);let o=[];for(let s=0;s<i.length;s++){let l=i[s];if(a[s].field.isKeyField()){if("string"!=typeof l)throw new D(C.INVALID_ARGUMENT,`Invalid query. Expected a string for document ID in ${r}(), but got a ${typeof l}`);if(!n$(e)&&-1!==l.indexOf("/"))throw new D(C.INVALID_ARGUMENT,`Invalid query. When querying a collection and ordering by documentId(), the value passed to ${r}() must be a plain document ID, but '${l}' contains a slash.`);let n=e.path.child(H.fromString(l));if(!X.isDocumentKey(n))throw new D(C.INVALID_ARGUMENT,`Invalid query. When querying a collection group and ordering by documentId(), the value passed to ${r}() must result in a valid document path, but '${n}' is not because it contains an odd number of segments.`);let i=new X(n);o.push(t7(t,i))}else{let e=lM(n,r,l);o.push(e)}}return new nf(o,s)}(e._query,e.firestore._databaseId,i,t,n,r)}}function l8(e,t,n){if("string"==typeof(n=(0,h.Ku)(n))){if(""===n)throw new D(C.INVALID_ARGUMENT,"Invalid query. When querying with documentId(), you must provide a valid document ID, but it was an empty string.");if(!n$(t)&&-1!==n.indexOf("/"))throw new D(C.INVALID_ARGUMENT,`Invalid query. When querying a collection by documentId(), you must provide a plain document ID, but '${n}' contains a '/' character.`);let r=t.path.child(H.fromString(n));if(!X.isDocumentKey(r))throw new D(C.INVALID_ARGUMENT,`Invalid query. When querying a collection group by documentId(), the value provided must result in a valid document path, but '${r}' is not because it has an odd number of segments (${r.length}).`);return t7(e,new X(r))}if(n instanceof ln)return t7(e,n._key);throw new D(C.INVALID_ARGUMENT,`Invalid query. When querying with documentId(), you must provide a valid string or a DocumentReference, but it was: ${o6(n)}.`)}function l9(e,t){if(!Array.isArray(e)||0===e.length)throw new D(C.INVALID_ARGUMENT,`Invalid Query. A non-empty array is required for '${t.toString()}' filters.`)}function l7(e,t){let n=function(e,t){for(let n of e)for(let e of n.getFlattenedFilters())if(t.indexOf(e.op)>=0)return e.op;return null}(e.filters,function(e){switch(e){case"!=":return["!=","not-in"];case"array-contains-any":case"in":return["not-in"];case"not-in":return["array-contains-any","in","not-in","!="];default:return[]}}(t.op));if(null!==n)throw n===t.op?new D(C.INVALID_ARGUMENT,`Invalid query. You cannot use more than one '${t.op.toString()}' filter.`):new D(C.INVALID_ARGUMENT,`Invalid query. You cannot use '${t.op.toString()}' filters with '${n.toString()}' filters.`)}class ue{convertValue(e,t="none"){switch(t2(e)){case 0:return null;case 1:return e.booleanValue;case 2:return tP(e.integerValue||e.doubleValue);case 3:return this.convertTimestamp(e.timestampValue);case 4:return this.convertServerTimestamp(e,t);case 5:return e.stringValue;case 6:return this.convertBytes(tU(e.bytesValue));case 7:return this.convertReference(e.referenceValue);case 8:return this.convertGeoPoint(e.geoPointValue);case 9:return this.convertArray(e.arrayValue,t);case 11:return this.convertObject(e.mapValue,t);case 10:return this.convertVectorValue(e.mapValue);default:throw S(62114,{value:e})}}convertObject(e,t){return this.convertObjectMap(e.fields,t)}convertObjectMap(e,t="none"){let n={};return tS(e,(e,r)=>{n[e]=this.convertValue(r,t)}),n}convertVectorValue(e){var t,n,r;return new ly(null==(r=null==(n=null==(t=e.fields)?void 0:t[t0].arrayValue)?void 0:n.values)?void 0:r.map(e=>tP(e.doubleValue)))}convertGeoPoint(e){return new lp(tP(e.latitude),tP(e.longitude))}convertArray(e,t){return(e.values||[]).map(e=>this.convertValue(e,t))}convertServerTimestamp(e,t){switch(t){case"previous":let n=tG(e);return null==n?null:this.convertValue(n,t);case"estimate":return this.convertTimestamp(tj(e));default:return null}}convertTimestamp(e){let t=tL(e);return new G(t.seconds,t.nanos)}convertDocumentKey(e,t){let n=H.fromString(e);N(im(n),9688,{name:e});let r=new tH(n.get(1),n.get(3)),i=new X(n.popFirst(5));return r.isEqual(t)||E(`Document ${i} contains a document reference within a different database (${r.projectId}/${r.database}) which is not supported. It will be treated as a reference in the current database (${t.projectId}/${t.database}) instead.`),i}}function ut(e,t,n){return e?n&&(n.merge||n.mergeFields)?e.toFirestore(t,n):e.toFirestore(t):t}class un extends ue{constructor(e){super(),this.firestore=e}convertBytes(e){return new lf(e)}convertReference(e){let t=this.convertDocumentKey(e,this.firestore._databaseId);return new ln(this.firestore,null,t)}}class ur{constructor(e,t){this.hasPendingWrites=e,this.fromCache=t}isEqual(e){return this.hasPendingWrites===e.hasPendingWrites&&this.fromCache===e.fromCache}}class ui extends l${constructor(e,t,n,r,i,s){super(e,t,n,r,s),this._firestore=e,this._firestoreImpl=e,this.metadata=i}exists(){return super.exists()}data(e={}){if(this._document){if(this._converter){let t=new us(this._firestore,this._userDataWriter,this._key,this._document,this.metadata,null);return this._converter.fromFirestore(t,e)}return this._userDataWriter.convertValue(this._document.data.value,e.serverTimestamps)}}get(e,t={}){if(this._document){let n=this._document.data.field(lj("DocumentSnapshot.get",e));if(null!==n)return this._userDataWriter.convertValue(n,t.serverTimestamps)}}}class us extends ui{data(e={}){return super.data(e)}}class ua{constructor(e,t,n,r){this._firestore=e,this._userDataWriter=t,this._snapshot=r,this.metadata=new ur(r.hasPendingWrites,r.fromCache),this.query=n}get docs(){let e=[];return this.forEach(t=>e.push(t)),e}get size(){return this._snapshot.docs.size}get empty(){return 0===this.size}forEach(e,t){this._snapshot.docs.forEach(n=>{e.call(t,new us(this._firestore,this._userDataWriter,n.key,n,new ur(this._snapshot.mutatedKeys.has(n.key),this._snapshot.fromCache),this.query.converter))})}docChanges(e={}){let t=!!e.includeMetadataChanges;if(t&&this._snapshot.excludesMetadataChanges)throw new D(C.INVALID_ARGUMENT,"To include metadata changes with your document changes, you must also pass { includeMetadataChanges:true } to onSnapshot().");return this._cachedChanges&&this._cachedChangesIncludeMetadataChanges===t||(this._cachedChanges=function(e,t){if(e._snapshot.oldDocs.isEmpty()){let t=0;return e._snapshot.docChanges.map(n=>{let r=new us(e._firestore,e._userDataWriter,n.doc.key,n.doc,new ur(e._snapshot.mutatedKeys.has(n.doc.key),e._snapshot.fromCache),e.query.converter);return n.doc,{type:"added",doc:r,oldIndex:-1,newIndex:t++}})}{let n=e._snapshot.oldDocs;return e._snapshot.docChanges.filter(e=>t||3!==e.type).map(t=>{let r=new us(e._firestore,e._userDataWriter,t.doc.key,t.doc,new ur(e._snapshot.mutatedKeys.has(t.doc.key),e._snapshot.fromCache),e.query.converter),i=-1,s=-1;return 0!==t.type&&(i=n.indexOf(t.doc.key),n=n.delete(t.doc.key)),1!==t.type&&(s=(n=n.add(t.doc)).indexOf(t.doc.key)),{type:function(e){switch(e){case 0:return"added";case 2:case 3:return"modified";case 1:return"removed";default:return S(61501,{type:e})}}(t.type),doc:r,oldIndex:i,newIndex:s}})}}(this,t),this._cachedChangesIncludeMetadataChanges=t),this._cachedChanges}}function uo(e){e=o8(e,ln);let t=o8(e.firestore,lu);return(function(e,t,n={}){let r=new A;return e.asyncQueue.enqueueAndForget(async()=>(function(e,t,n,r,i){let s=new oG({next:o=>{s.yu(),t.enqueueAndForget(()=>a4(e,a));let l=o.docs.has(n);!l&&o.fromCache?i.reject(new D(C.UNAVAILABLE,"Failed to get document because the client is offline.")):l&&o.fromCache&&r&&"server"===r.source?i.reject(new D(C.UNAVAILABLE,'Failed to get document from server. (However, this document does exist in the local cache. Run again without setting source to "server" to retrieve the cached document.)')):i.resolve(o)},error:e=>i.reject(e)}),a=new a9(nz(n.path),s,{includeMetadataChanges:!0,Fa:!0});return a5(e,a)})(await o0(e),e.asyncQueue,t,n,r)),r.promise})(lc(t),e._key).then(n=>(function(e,t,n){let r=n.docs.get(t._key),i=new ul(e);return new ui(e,i,t._key,r,new ur(n.hasPendingWrites,n.fromCache),t.converter)})(t,e,n))}class ul extends ue{constructor(e){super(),this.firestore=e}convertBytes(e){return new lf(e)}convertReference(e){let t=this.convertDocumentKey(e,this.firestore._databaseId);return new ln(this.firestore,null,t)}}function uu(e){e=o8(e,lt);let t=o8(e.firestore,lu),n=lc(t),r=new ul(t);return function(e){if("L"===e.limitType&&0===e.explicitOrderBy.length)throw new D(C.UNIMPLEMENTED,"limitToLast() queries require specifying at least one orderBy() clause")}(e._query),(function(e,t,n={}){let r=new A;return e.asyncQueue.enqueueAndForget(async()=>(function(e,t,n,r,i){let s=new oG({next:n=>{s.yu(),t.enqueueAndForget(()=>a4(e,a)),n.fromCache&&"server"===r.source?i.reject(new D(C.UNAVAILABLE,'Failed to get documents from server. (However, these documents may exist in the local cache. Run again without setting source to "server" to retrieve the cached documents.)')):i.resolve(n)},error:e=>i.reject(e)}),a=new a9(n,s,{includeMetadataChanges:!0,Fa:!0});return a5(e,a)})(await o0(e),e.asyncQueue,t,n,r)),r.promise})(n,e._query).then(n=>new ua(t,r,e,n))}function uh(e,t,n,...r){e=o8(e,ln);let i=o8(e.firestore,lu),s=l_(i);return uf(i,[("string"==typeof(t=(0,h.Ku)(t))||t instanceof lm?lV(s,"updateDoc",e._key,t,n,r):lR(s,"updateDoc",e._key,t)).toMutation(e._key,ry.exists(!0))])}function uc(e){return uf(o8(e.firestore,lu),[new rC(e._key,ry.none())])}function ud(e,t){let n=o8(e.firestore,lu),r=ls(e),i=ut(e.converter,t);return uf(n,[lS(l_(e.firestore),"addDoc",r._key,i,null!==e.converter,{}).toMutation(r._key,ry.exists(!1))]).then(()=>r)}function uf(e,t){var n=lc(e);let r=new A;return n.asyncQueue.enqueueAndForget(async()=>om(await oZ(n).then(e=>e.syncEngine),t,r)),r.promise}class um{constructor(e){this.forceOwnership=e,this.kind="persistentSingleTab"}toJSON(){return{kind:this.kind}}_initialize(e){this._onlineComponentProvider=o$.provider,this._offlineComponentProvider={build:t=>new oz(t,null==e?void 0:e.cacheSizeBytes,this.forceOwnership)}}}let ug={maxAttempts:5};function up(e,t){if((e=(0,h.Ku)(e)).firestore!==t)throw new D(C.INVALID_ARGUMENT,"Provided document reference is from a different Firestore instance.");return e}class uy{constructor(e,t){this._firestore=e,this._transaction=t,this._dataReader=l_(e)}get(e){let t=up(e,this._firestore),n=new un(this._firestore);return this._transaction.lookup([t._key]).then(e=>{if(!e||1!==e.length)return S(24041);let r=e[0];if(r.isFoundDocument())return new l$(this._firestore,n,r.key,r,t.converter);if(r.isNoDocument())return new l$(this._firestore,n,t._key,null,t.converter);throw S(18433,{doc:r})})}set(e,t,n){let r=up(e,this._firestore),i=ut(r.converter,t,n),s=lS(this._dataReader,"Transaction.set",r._key,i,null!==r.converter,n);return this._transaction.set(r._key,s),this}update(e,t,n,...r){let i,s=up(e,this._firestore);return i="string"==typeof(t=(0,h.Ku)(t))||t instanceof lm?lV(this._dataReader,"Transaction.update",s._key,t,n,r):lR(this._dataReader,"Transaction.update",s._key,t),this._transaction.update(s._key,i),this}delete(e){let t=up(e,this._firestore);return this._transaction.delete(t._key),this}}class uw extends uy{constructor(e,t){super(e,t),this._firestore=e}get(e){let t=up(e,this._firestore),n=new ul(this._firestore);return super.get(e).then(e=>new ui(this._firestore,n,t._key,e._document,new ur(!1,!1),t.converter))}}function uv(e,t,n){e=o8(e,lu);let r=Object.assign(Object.assign({},ug),n);if(r.maxAttempts<1)throw new D(C.INVALID_ARGUMENT,"Max attempts must be at least 1");return function(e,t,n){let r=new A;return e.asyncQueue.enqueueAndForget(async()=>{let i=await oZ(e).then(e=>e.datastore);new oQ(e.asyncQueue,i,n,t,r).Nu()}),r.promise}(lc(e),n=>t(new uw(e,n)),r)}new WeakMap,!function(e,t=!0){w=o.MF,(0,o.om)(new l.uA("firestore",(e,{instanceIdentifier:n,options:r})=>{let i=e.getProvider("app").getImmediate(),s=new lu(new M(e.getProvider("auth-internal")),new P(i,e.getProvider("app-check-internal")),function(e,t){if(!Object.prototype.hasOwnProperty.apply(e.options,["projectId"]))throw new D(C.INVALID_ARGUMENT,'"projectId" not provided in firebase.initializeApp.');return new tH(e.options.projectId,t)}(i,n),i);return r=Object.assign({useFetchStreams:t},r),s._setSettings(r),s},"PUBLIC").setMultipleInstances(!0)),(0,o.KO)(g,p,void 0),(0,o.KO)(g,p,"esm2017")}()}}]);