const { chromium } = require('playwright');

(async () => {
  // Launch the browser
  const browser = await chromium.launch({ 
    headless: false, // Show the browser
    slowMo: 50 // Slow down operations slightly
  });
  
  // Create a new browser context
  const context = await browser.newContext({
    viewport: { width: 1280, height: 800 }
  });
  
  // Open a new page
  const page = await context.newPage();
  
  try {
    // Navigate to the login page
    console.log('Navigating to the login page...');
    await page.goto('http://localhost:3000/login');
    
    // Set up console log capture
    page.on('console', msg => {
      console.log(`PAGE LOG: ${msg.text()}`);
    });
    
    // Set up response logging for image requests
    page.on('response', response => {
      const url = response.url();
      if (url.includes('firebasestorage.googleapis.com') || url.includes('image') || url.endsWith('.jpg') || url.endsWith('.png')) {
        console.log(`Image response: ${url} - Status: ${response.status()}`);
      }
    });
    
    console.log('<PERSON><PERSON><PERSON> is open for manual testing. Press Ctrl+C in the terminal to close.');
    
    // Keep the script running
    await new Promise(() => {}); // This promise never resolves, keeping the script running
    
  } catch (error) {
    console.error('Error:', error);
  }
})();
