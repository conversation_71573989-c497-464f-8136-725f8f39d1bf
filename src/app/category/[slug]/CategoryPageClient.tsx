'use client';

import { useState, useEffect } from 'react';
import PostCard from '@/components/PostCard';
import { collection, query, where, getDocs, orderBy, Timestamp, startAfter, limit, QueryDocumentSnapshot } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import FeaturedPost from '@/components/FeaturedPost';

interface Post {
  id: string;
  title: string;
  content: string;
  thumbnailImage: string;
  category?: string | string[];
  tags?: string[];
  date: Timestamp;
  viewCount: number;
  status: string;
}

const POSTS_PER_PAGE = 13; // Show 13 posts per page (1 featured + 12 regular)

interface CategoryPageClientProps {
  slug: string;
}

export default function CategoryPageClient({ slug: categorySlug }: CategoryPageClientProps) {
  const [posts, setPosts] = useState<Post[]>([]);
  const [categoryName, setCategoryName] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [lastVisible, setLastVisible] = useState<QueryDocumentSnapshot | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  useEffect(() => {
    async function fetchCategoryPosts() {
      try {
        setIsLoading(true);
        setPosts([]);
        setLastVisible(null);
        setHasMore(true);

        // First, get the category name
        const categoriesCollection = collection(db, 'categories');
        const categoryQuery = query(categoriesCollection, where('slug', '==', categorySlug));
        const categorySnapshot = await getDocs(categoryQuery);

        console.log(`Searching for category with slug "${categorySlug}"`);
        console.log(`Found ${categorySnapshot.docs.length} matching categories`);

        if (categorySnapshot.empty) {
          console.log('No category found with this slug');
          setCategoryName('پۆلی نەدۆزراوە');
          setPosts([]);
          setIsLoading(false);
          return;
        }

        const catData = categorySnapshot.docs[0].data();
        setCategoryName(catData.name);

        console.log('Category found:', {
          id: categorySnapshot.docs[0].id,
          name: catData.name,
          slug: catData.slug
        });

        // Get all posts and filter client-side for published status
        // This avoids the need for a composite index
        const postsCollection = collection(db, 'posts');
        const postsQuery = query(
          postsCollection,
          orderBy('date', 'desc'),
          limit(POSTS_PER_PAGE * 2) // Get more to account for filtering
        );

        const postsSnapshot = await getDocs(postsQuery);
        console.log(`Found ${postsSnapshot.docs.length} total posts`);

        if (postsSnapshot.empty) {
          setHasMore(false);
          setIsLoading(false);
          return;
        }

        // Save the last visible document for pagination
        setLastVisible(postsSnapshot.docs[postsSnapshot.docs.length - 1]);

        // Map the documents to Post objects
        const allPosts = postsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Post[];

        // Filter posts for published status and this category
        const filteredPosts = allPosts.filter(post => {
          // First filter for published status
          if (post.status !== 'بڵاوکراوەتەوە') {
            return false;
          }

          const postCategory = post.category;

          // Direct match with category slug (most reliable)
          if (postCategory === categorySlug) return true;

          // Check if category is an array and includes the slug
          if (Array.isArray(postCategory) && postCategory.includes(categorySlug)) {
            return true;
          }

          return false;
        });

        console.log(`Filtered to ${filteredPosts.length} posts for category ${catData.name}`);

        // Limit to POSTS_PER_PAGE (13) posts
        const limitedPosts = filteredPosts.slice(0, POSTS_PER_PAGE);

        // Set hasMore based on whether there are more posts than our limit
        setHasMore(filteredPosts.length > POSTS_PER_PAGE);

        // Set the posts
        setPosts(limitedPosts);
      } catch (err) {
        console.error('Error fetching category posts:', err);
      } finally {
        setIsLoading(false);
      }
    }

    if (categorySlug) {
      fetchCategoryPosts();
    }
  }, [categorySlug]);

  const handleLoadMore = async () => {
    if (!lastVisible || !hasMore || isLoadingMore) return;

    setIsLoadingMore(true);

    try {
      // Get more posts starting after the last visible document
      // Avoid using where clause with orderBy to prevent index requirement
      const postsCollection = collection(db, 'posts');
      const postsQuery = query(
        postsCollection,
        orderBy('date', 'desc'),
        startAfter(lastVisible),
        limit(POSTS_PER_PAGE * 2) // Get more to account for filtering
      );

      const postsSnapshot = await getDocs(postsQuery);
      console.log(`Loaded ${postsSnapshot.docs.length} more posts`);

      if (postsSnapshot.empty) {
        setHasMore(false);
        setIsLoadingMore(false);
        return;
      }

      // Save the last visible document for pagination
      setLastVisible(postsSnapshot.docs[postsSnapshot.docs.length - 1]);

      // Map the documents to Post objects
      const morePosts = postsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Post[];

      // Filter posts for published status and this category
      const filteredPosts = morePosts.filter(post => {
        // First filter for published status
        if (post.status !== 'بڵاوکراوەتەوە') {
          return false;
        }

        const postCategory = post.category;

        // Direct match with category slug (most reliable)
        if (postCategory === categorySlug) return true;

        // Check if category is an array and includes the slug
        if (Array.isArray(postCategory) && postCategory.includes(categorySlug)) {
          return true;
        }

        return false;
      });

      console.log(`Filtered to ${filteredPosts.length} more posts for this category`);

      // Take only up to POSTS_PER_PAGE for the next page
      const nextPagePosts = filteredPosts.slice(0, POSTS_PER_PAGE);

      // Set hasMore based on whether there are more posts after filtering
      setHasMore(filteredPosts.length > POSTS_PER_PAGE);

      // Add the new posts to the existing ones
      setPosts(prevPosts => [...prevPosts, ...nextPagePosts]);
    } catch (error) {
      console.error('Error loading more posts:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <h1 className="text-3xl font-bold text-black mb-4 relative pr-6 inline-block after:content-[''] after:absolute after:top-0 after:bottom-0 after:right-0 after:w-2 after:bg-[#8f5826] after:rounded-full">{categoryName}</h1>
          <div className="h-1 bg-[#8f5826] w-full my-6 mb-8 rounded-full opacity-20"></div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin"></div>
            </div>
          ) : posts.length === 0 ? (
            <div className="flex justify-center items-center h-64">
              <p className="text-xl text-gray-500">هیچ بابەتێک نەدۆزرایەوە لەم پۆلە</p>
            </div>
          ) : (
            <>
              {/* Featured Post */}
              {posts.length > 0 && (
                <>
                  {console.log('Featured post in category page:', posts[0])}
                  <FeaturedPost post={posts[0]} />
                </>
              )}

              {/* Regular Posts Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {posts.slice(1).map((post) => (
                  <PostCard
                    key={post.id}
                    id={post.id}
                    title={post.title}
                    thumbnailImage={post.thumbnailImage}
                    date={post.date}
                    category={post.tags && Array.isArray(post.tags) && post.tags.length > 0 ? post.tags[0] : undefined}
                    viewCount={post.viewCount}
                  />
                ))}
              </div>

              {/* Load More Button */}
              {hasMore && (
                <div className="mt-12 flex justify-center">
                  <button
                    onClick={handleLoadMore}
                    disabled={isLoadingMore}
                    className={`px-6 py-3 rounded-md bg-[var(--primary-600)] text-white hover:bg-[var(--primary-700)] transition-colors ${
                      isLoadingMore ? 'opacity-70 cursor-not-allowed' : ''
                    }`}
                    type="button"
                  >
                    {isLoadingMore ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        چاوەڕوانبە...
                      </span>
                    ) : (
                      'بینینی زیاتر'
                    )}
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
}
