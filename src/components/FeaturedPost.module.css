.featuredPost {
  width: 100%;
  min-height: 300px;
  background-color: #f9f5f0;
  border-radius: 1rem;
  border: 2px solid #8f5826;
  box-shadow: 0 8px 16px rgba(143, 88, 38, 0.2);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: row-reverse; /* Reversed for RTL layout */
  margin-bottom: 3rem;
  position: relative;
  overflow: hidden;
  border-right: 6px solid #8f5826;
}

.featuredPost:hover {
  box-shadow: 0 12px 24px rgba(143, 88, 38, 0.3);
  transform: translateY(-4px);
}

.featuredPost::before {
  content: "تازەترین";
  position: absolute;
  top: 1rem;
  right: 1rem;
  background-color: #8f5826;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(143, 88, 38, 0.3);
}

.imageContainer {
  position: relative;
  width: 40%; /* Fixed width for image container */
  min-width: 300px; /* Minimum width */
  height: auto;
  min-height: 300px; /* Ensure minimum height */
  background-color: #f0f0f0; /* Background color while image loads */
  border-left: 1px solid rgba(143, 88, 38, 0.3);
}

.contentContainer {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: right; /* Align text to the right for RTL */
}

.title {
  font-size: 1.75rem !important;
  font-weight: 800 !important;
  color: #333 !important;
  margin-bottom: 1rem;
  line-height: 1.3;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  hyphens: auto;
  max-width: 100%;
}

.meta {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  color: var(--gray-600);
}

.metaItem {
  display: flex;
  align-items: center;
  margin-right: 1rem;
}

.excerpt {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--gray-700);
  margin-bottom: 1.5rem;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  hyphens: auto;
  max-width: 100%;
}

.readMore {
  display: inline-block;
  padding: 0.5rem 1.5rem;
  background-color: #8f5826;
  color: white;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(143, 88, 38, 0.2);
}

.readMore:hover {
  background-color: #734620;
  box-shadow: 0 4px 8px rgba(143, 88, 38, 0.3);
  transform: translateY(-2px);
}

/* Desktop and tablet */
@media (min-width: 768px) {
  .featuredPost {
    min-height: 400px;
  }

  .imageContainer {
    min-height: 400px;
  }

  .contentContainer {
    padding: 2rem;
  }
}

/* Mobile devices */
@media (max-width: 767px) {
  .featuredPost {
    flex-direction: column-reverse; /* Reversed for RTL layout */
    min-height: 500px;
  }

  .imageContainer {
    width: 100%;
    min-width: 100%;
    height: 200px;
    min-height: 200px;
  }

  .contentContainer {
    width: 100%;
    padding: 1.5rem;
  }

  .title {
    font-size: 1.5rem !important;
  }

  .excerpt {
    font-size: 1rem;
  }
}
