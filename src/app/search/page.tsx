'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { collection, query, where, getDocs, orderBy, limit, startAfter, QueryDocumentSnapshot, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import PostCard from '@/components/PostCard';
import SearchBar from '@/components/SearchBar';

interface Post {
  id: string;
  title: string;
  content: string;
  thumbnailImage: string;
  category?: string | string[];
  date: Timestamp;
  viewCount: number;
  status: string;
}

const POSTS_PER_PAGE = 12; // Show 12 posts per page for search results

function SearchContent() {
  const searchParams = useSearchParams();
  const searchQuery = searchParams.get('q') || '';

  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastVisible, setLastVisible] = useState<QueryDocumentSnapshot | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [totalResults, setTotalResults] = useState(0);

  useEffect(() => {
    async function searchPosts() {
      if (!searchQuery.trim()) {
        setPosts([]);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setPosts([]);
        setLastVisible(null);
        setHasMore(true);

        // Get all posts and filter client-side
        // This approach is used because Firestore doesn't support full-text search
        const postsCollection = collection(db, 'posts');
        const postsQuery = query(
          postsCollection,
          orderBy('date', 'desc'),
          limit(100) // Get a larger batch to filter from
        );

        const postsSnapshot = await getDocs(postsQuery);

        if (postsSnapshot.empty) {
          setHasMore(false);
          setIsLoading(false);
          return;
        }

        // Map the documents to Post objects
        const allPosts = postsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Post[];

        // Filter posts for published status and search term
        const searchTermLower = searchQuery.toLowerCase();
        const filteredPosts = allPosts.filter(post => {
          // First filter for published status
          if (post.status !== 'بڵاوکراوەتەوە') {
            return false;
          }

          // Search in title and content
          const titleMatch = post.title && post.title.toLowerCase().includes(searchTermLower);
          const contentMatch = post.content && post.content.toLowerCase().includes(searchTermLower);

          return titleMatch || contentMatch;
        });

        setTotalResults(filteredPosts.length);

        // Take only the first page
        const firstPagePosts = filteredPosts.slice(0, POSTS_PER_PAGE);

        // Set hasMore based on whether we have more posts than the page size
        setHasMore(filteredPosts.length > POSTS_PER_PAGE);

        // Save the last visible document for pagination if we have more posts
        if (filteredPosts.length > POSTS_PER_PAGE) {
          const lastPostIndex = Math.min(POSTS_PER_PAGE - 1, filteredPosts.length - 1);
          const lastPostId = filteredPosts[lastPostIndex].id;
          const lastPostDoc = postsSnapshot.docs.find(doc => doc.id === lastPostId);
          if (lastPostDoc) {
            setLastVisible(lastPostDoc);
          }
        }

        // Set the posts
        setPosts(firstPagePosts);
      } catch (err) {
        console.error('Error searching posts:', err);
      } finally {
        setIsLoading(false);
      }
    }

    searchPosts();
  }, [searchQuery]);

  const handleLoadMore = async () => {
    if (!lastVisible || !hasMore || isLoadingMore) return;

    setIsLoadingMore(true);

    try {
      // Get more posts starting after the last visible document
      const postsCollection = collection(db, 'posts');
      const postsQuery = query(
        postsCollection,
        orderBy('date', 'desc'),
        startAfter(lastVisible),
        limit(100) // Get a larger batch to filter from
      );

      const postsSnapshot = await getDocs(postsQuery);

      if (postsSnapshot.empty) {
        setHasMore(false);
        setIsLoadingMore(false);
        return;
      }

      // Map the documents to Post objects
      const morePosts = postsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Post[];

      // Filter posts for published status and search term
      const searchTermLower = searchQuery.toLowerCase();
      const filteredPosts = morePosts.filter(post => {
        // First filter for published status
        if (post.status !== 'بڵاوکراوەتەوە') {
          return false;
        }

        // Search in title and content
        const titleMatch = post.title && post.title.toLowerCase().includes(searchTermLower);
        const contentMatch = post.content && post.content.toLowerCase().includes(searchTermLower);

        return titleMatch || contentMatch;
      });

      // Take only up to POSTS_PER_PAGE for the next page
      const nextPagePosts = filteredPosts.slice(0, POSTS_PER_PAGE);

      // Set hasMore based on whether we got enough posts after filtering
      setHasMore(filteredPosts.length > POSTS_PER_PAGE);

      // Save the last visible document for pagination if we have more posts
      if (filteredPosts.length > POSTS_PER_PAGE) {
        const lastPostIndex = Math.min(POSTS_PER_PAGE - 1, filteredPosts.length - 1);
        const lastPostId = filteredPosts[lastPostIndex].id;
        const lastPostDoc = postsSnapshot.docs.find(doc => doc.id === lastPostId);
        if (lastPostDoc) {
          setLastVisible(lastPostDoc);
        }
      }

      // Add the new posts to the existing ones
      setPosts(prevPosts => [...prevPosts, ...nextPagePosts]);
    } catch (error) {
      console.error('Error loading more search results:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-black mb-4 relative pr-6 inline-block after:content-[''] after:absolute after:top-0 after:bottom-0 after:right-0 after:w-2 after:bg-[#8f5826] after:rounded-full">
              گەڕان
            </h1>
            <div className="h-1 bg-[#8f5826] w-full my-6 mb-8 rounded-full opacity-20"></div>

            {searchQuery && (
              <p className="text-lg text-gray-600 mb-6 text-center">
                {isLoading
                  ? 'گەڕان بۆ بابەتەکان...'
                  : totalResults > 0
                    ? `${totalResults} ئەنجام بۆ "${searchQuery}"`
                    : `هیچ ئەنجامێک نەدۆزرایەوە بۆ "${searchQuery}"`
                }
              </p>
            )}
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin"></div>
            </div>
          ) : posts.length === 0 ? (
            <div className="flex justify-center items-center h-64">
              {searchQuery ? (
                <p className="text-xl text-gray-500">هیچ بابەتێک نەدۆزرایەوە. تکایە هەوڵی گەڕانێکی تر بدە.</p>
              ) : (
                <p className="text-xl text-gray-500">تکایە وشەیەک بنووسە بۆ گەڕان.</p>
              )}
            </div>
          ) : (
            <>
              {/* Search Results Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {posts.map((post) => (
                  <PostCard
                    key={post.id}
                    id={post.id}
                    title={post.title}
                    thumbnailImage={post.thumbnailImage}
                    date={post.date}
                    category={Array.isArray(post.category) ? post.category[0] : post.category}
                    viewCount={post.viewCount}
                  />
                ))}
              </div>

              {/* Load More Button */}
              {hasMore && (
                <div className="mt-12 flex justify-center">
                  <button
                    onClick={handleLoadMore}
                    disabled={isLoadingMore}
                    className={`px-6 py-3 rounded-md bg-[var(--primary-600)] text-white hover:bg-[var(--primary-700)] transition-colors ${
                      isLoadingMore ? 'opacity-70 cursor-not-allowed' : ''
                    }`}
                    type="button"
                  >
                    {isLoadingMore ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        چاوەڕوانبە...
                      </span>
                    ) : (
                      'بینینی زیاتر'
                    )}
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
}

// Loading fallback for Suspense
function SearchLoading() {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-black mb-4 relative pr-6 inline-block after:content-[''] after:absolute after:top-0 after:bottom-0 after:right-0 after:w-2 after:bg-[#8f5826] after:rounded-full">
              گەڕان
            </h1>
            <div className="h-1 bg-[#8f5826] w-full my-6 mb-8 rounded-full opacity-20"></div>
          </div>
          <div className="flex justify-center items-center h-64">
            <div className="inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin"></div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}

// Main page component that wraps SearchContent in Suspense
export default function SearchPage() {
  return (
    <Suspense fallback={<SearchLoading />}>
      <SearchContent />
    </Suspense>
  );
}
