'use client';

import dynamic from 'next/dynamic';

// Import the LoginForm component with dynamic import to avoid SSR issues
// This component only supports email/password authentication
const LoginForm = dynamic(
  () => import("@/components/LoginForm"),
  { ssr: false }
);

export default function FirebaseAuthWrapper() {
  return (
    <div className="w-full max-w-md mx-auto bg-white p-6 rounded-lg shadow-md">
      <LoginForm />
    </div>
  );
}
