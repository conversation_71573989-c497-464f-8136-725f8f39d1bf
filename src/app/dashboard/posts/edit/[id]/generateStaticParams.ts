import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

// This function is required for static site generation with dynamic routes
export async function generateStaticParams() {
  try {
    // Fetch all post IDs from Firestore
    const postsCollection = collection(db, 'posts');
    const postsSnapshot = await getDocs(postsCollection);
    
    // Return an array of objects with id property
    return postsSnapshot.docs.map(doc => ({
      id: doc.id,
    }));
  } catch (error) {
    console.error('Error generating static params:', error);
    return []; // Return empty array as fallback
  }
}

export default generateStaticParams;
