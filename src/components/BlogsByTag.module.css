.postGrid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1.5rem;
}

@media (min-width: 640px) {
  .postGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .postGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.postCard {
  background-color: #f9f5f0;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 2px solid #8f5826;
  box-shadow: 0 4px 12px rgba(143, 88, 38, 0.2);
  transition: all 0.3s ease;
}

.postCard:hover {
  box-shadow: 0 10px 25px rgba(143, 88, 38, 0.35);
  transform: translateY(-6px);
}

.scrollContainer {
  overflow-x: auto;
  padding-bottom: 1rem;
  padding-top: 0.25rem;
  margin-top: 1.5rem; /* Added margin-top to compensate for removed line */
  margin-bottom: 0.5rem;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.scrollContainer::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.scrollRow {
  display: flex;
  gap: 1.5rem;
  padding: 0.5rem 0.25rem;
}

@media (min-width: 1024px) {
  .scrollRow {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    padding: 0.75rem 0.5rem;
  }
}

@media (min-width: 1280px) {
  .scrollRow {
    grid-template-columns: repeat(4, 1fr);
    gap: 2.5rem;
  }
}

.postCardScroll {
  min-width: 280px;
  max-width: 320px;
  flex-shrink: 0;
  width: 85vw;
}

@media (min-width: 640px) {
  .postCardScroll {
    width: 42vw;
  }
}

@media (min-width: 1024px) {
  .postCardScroll {
    min-width: 0;
    max-width: none;
    width: auto;
  }
}
