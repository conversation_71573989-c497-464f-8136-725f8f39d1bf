'use client';

import { useState, useEffect, useRef } from 'react';
import { collection, query, where, getDocs, orderBy, limit, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import Link from 'next/link';

interface SearchBarProps {
  className?: string;
  placeholder?: string;
  compact?: boolean;
}

interface Post {
  id: string;
  title: string;
  content: string;
  thumbnailImage: string;
  category?: string | string[];
  date: Timestamp;
  viewCount: number;
  status: string;
}

export default function SearchBar({ className = '', placeholder = 'گەڕان...', compact = false }: SearchBarProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<Post[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [totalResults, setTotalResults] = useState(0);
  const searchRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close search results
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Search function
  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!searchTerm.trim()) {
      setSearchResults([]);
      setShowResults(false);
      return;
    }

    // If form is submitted (Enter key pressed), go directly to search results page
    if (e.nativeEvent instanceof SubmitEvent) {
      window.location.href = `/search?q=${encodeURIComponent(searchTerm.trim())}`;
      return;
    }

    setIsSearching(true);
    setShowResults(true);

    try {
      // Get all posts and filter client-side
      const postsCollection = collection(db, 'posts');
      const postsQuery = query(
        postsCollection,
        orderBy('date', 'desc'),
        limit(50) // Get a reasonable number of posts to search through
      );

      const postsSnapshot = await getDocs(postsQuery);

      if (postsSnapshot.empty) {
        setSearchResults([]);
        setIsSearching(false);
        return;
      }

      // Map the documents to Post objects
      const allPosts = postsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Post[];

      // Filter posts for published status and search term
      const searchTermLower = searchTerm.toLowerCase();
      const filteredPosts = allPosts.filter(post => {
        // First filter for published status
        if (post.status !== 'بڵاوکراوەتەوە') {
          return false;
        }

        // Search in title and content
        const titleMatch = post.title && post.title.toLowerCase().includes(searchTermLower);
        const contentMatch = post.content && post.content.toLowerCase().includes(searchTermLower);

        return titleMatch || contentMatch;
      });

      // Set total results count
      setTotalResults(filteredPosts.length);

      // Take only the first 4 results
      setSearchResults(filteredPosts.slice(0, 4));
    } catch (err) {
      console.error('Error searching posts:', err);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    if (e.target.value.trim().length >= 2) {
      handleSearch(e as unknown as React.FormEvent);
    } else {
      setSearchResults([]);
      setShowResults(false);
    }
  };

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <form onSubmit={handleSearch} className="relative">
        <div className="relative">
          <input
            type="text"
            value={searchTerm}
            onChange={handleInputChange}
            onFocus={() => searchTerm.trim().length >= 2 && setShowResults(true)}
            placeholder={placeholder}
            spellCheck="false"
            className={`w-full py-2 px-3 pl-10 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] bg-white ${
              compact ? 'text-sm' : 'text-base'
            }`}
          />
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`text-gray-400 ${compact ? 'h-4 w-4' : 'h-5 w-5'}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
          <button
            type="submit"
            className={`absolute inset-y-0 left-0 pl-3 flex items-center opacity-0 ${
              searchTerm.trim() ? 'cursor-pointer' : 'cursor-default'
            }`}
            aria-label="گەڕان"
          >
            <span className="sr-only">گەڕان</span>
          </button>
        </div>
      </form>

      {/* Search Results Dropdown */}
      {showResults && (
        <div className="absolute z-50 mt-1 w-full bg-white border border-[var(--gray-300)] rounded-md shadow-lg max-h-96 overflow-y-auto">
          {isSearching ? (
            <div className="p-4 text-center">
              <div className="inline-block w-6 h-6 border-2 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin"></div>
              <p className="mt-2 text-sm text-gray-500">گەڕان...</p>
            </div>
          ) : searchResults.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              {searchTerm.trim() ? 'هیچ ئەنجامێک نەدۆزرایەوە' : 'تکایە وشەیەک بنووسە بۆ گەڕان'}
            </div>
          ) : (
            <>
              <div className="p-2 border-b border-[var(--gray-200)] bg-[var(--primary-50)] hover:bg-[var(--primary-100)] transition-colors">
                <Link
                  href={`/search?q=${encodeURIComponent(searchTerm)}`}
                  className="block text-center"
                  onClick={() => setShowResults(false)}
                >
                  <p className="text-sm font-medium text-[var(--primary-700)]">{totalResults} ئەنجام بۆ "{searchTerm}"</p>
                </Link>
              </div>
              <ul>
                {searchResults.map(post => (
                  <li key={post.id} className="border-b border-[var(--gray-200)]">
                    <Link
                      href={`/post/${post.id}`}
                      className="block p-3 hover:bg-[var(--gray-50)] transition-colors"
                      onClick={() => setShowResults(false)}
                    >
                      <h4 className="font-medium text-[var(--gray-900)] mb-1 line-clamp-1">{post.title}</h4>
                      <p className="text-sm text-[var(--gray-600)] line-clamp-1">
                        {post.content.replace(/<[^>]*>/g, '').substring(0, 100)}...
                      </p>
                    </Link>
                  </li>
                ))}

              </ul>
            </>
          )}
        </div>
      )}
    </div>
  );
}
