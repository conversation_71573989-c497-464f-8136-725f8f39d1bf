'use client';

import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/lib/firebase/auth-context';
import { signOut } from '@/lib/firebase/auth';
import { useSidebar } from '@/lib/context/SidebarContext';
import Link from 'next/link';

export default function DashboardHeader() {
  const { user } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const { isOpen, isInitialized, toggle } = useSidebar();

  const handleSignOut = async () => {
    await signOut();
    router.push('/login');
  };

  // Don't adjust layout until initialization is complete
  if (!isInitialized) {
    return (
      <header className="bg-[var(--header-bg)] shadow-md fixed top-0 left-0 right-0 z-20">
        <div className="flex justify-between items-center px-6 py-3">
          {/* Minimal header content */}
        </div>
      </header>
    );
  }

  return (
    <header className={`bg-[var(--header-bg)] shadow-md fixed top-0 left-0 ${isOpen ? 'right-64' : 'right-0'} z-20 transition-all duration-300`}>
      <div className="flex justify-between items-center px-6 py-3">
        <div className="flex items-center">
          <button
            type="button"
            onClick={toggle}
            className="p-2 rounded-md text-[var(--primary-600)] hover:bg-[var(--primary-50)] transition-colors ml-3"
            aria-label="Toggle sidebar"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
          <div className="flex items-center">
            <Link href="/dashboard/posts" className="flex items-center">
              <img src="/logomain.svg" alt="Ramiyari Logo" className="h-8 w-auto ml-2" />
              <span className="text-xl font-bold text-black hover:text-[var(--primary-600)] transition-colors">
                بەڕێوەبردنی ناوەڕۆک
              </span>
            </Link>
          </div>
        </div>

        <div className="flex items-center">
          {user && (
            <div className="flex items-center">
              <div className="flex items-center bg-[var(--gray-100)] px-3 py-1.5 rounded-full">
                <div className="w-8 h-8 rounded-full bg-[var(--primary-100)] flex items-center justify-center text-[var(--primary-700)] font-bold">
                  {user.email?.charAt(0).toUpperCase() || 'U'}
                </div>
                <div className="mr-2 text-sm font-medium text-black">
                  <span>{user.email}</span>
                </div>
              </div>

              <div className="flex items-center mr-4">
                <button
                  type="button"
                  onClick={handleSignOut}
                  className="px-4 py-1.5 bg-[var(--danger-500)] text-white rounded-md hover:bg-[var(--danger-700)] transition-colors text-sm font-medium flex items-center cursor-pointer"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  دەرچوون
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Breadcrumb */}
      <div className="px-6 py-2 bg-[var(--gray-50)] border-t border-[var(--gray-200)] text-sm">
        <div className="flex items-center text-black">
          <span className="text-black font-medium">
            {pathname.includes('/posts') ? 'بابەتەکان' :
             pathname.includes('/categories') ? 'پۆلەکان' :
             pathname.includes('/tags') ? 'تاگەکان' : ''}
          </span>
        </div>
      </div>
    </header>
  );
}
