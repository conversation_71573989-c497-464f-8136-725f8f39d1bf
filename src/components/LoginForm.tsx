'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/firebase/auth-context';
import { signIn, signOut } from '@/lib/firebase/auth';

export default function LoginForm() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [message, setMessage] = useState('');

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    const result = await signIn(email, password);
    if (result.error) {
      setMessage(`هەڵە لە چوونەژوورەوە: ${result.error}`);
    } else {
      setMessage('بە سەرکەوتوویی چوویتە ژوورەوە!');
      setEmail('');
      setPassword('');

      // Redirect to posts page immediately after successful login
      router.push('/dashboard/posts');
    }
  };

  const handleSignOut = async () => {
    const result = await signOut();
    if (result.error) {
      setMessage(`هەڵە لە دەرچوون: ${result.error}`);
    } else {
      setMessage('بە سەرکەوتوویی دەرچوویت!');
    }
  };

  if (loading) {
    return <div className="p-4">باری ڕاستاندنەکە بارکردن...</div>;
  }

  return (
    <div className="max-w-md mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-center text-black">چوونەژوورەوە</h2>

      {message && (
        <div className={`mb-6 p-3 rounded-md text-sm font-medium ${
          message.includes('هەڵە')
            ? 'bg-[var(--danger-50)] text-[var(--danger-700)] border border-[var(--danger-500)]'
            : 'bg-[var(--success-50)] text-[var(--success-700)] border border-[var(--success-500)]'
        }`}>
          {message}
        </div>
      )}

      {user ? (
        <div className="bg-[var(--gray-50)] p-4 rounded-lg border border-[var(--gray-200)]">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 rounded-full bg-[var(--primary-100)] flex items-center justify-center text-[var(--primary-700)] text-xl font-bold">
              {user.email?.charAt(0).toUpperCase() || 'U'}
            </div>
            <div className="mr-4">
              <p className="font-medium text-black">{user.email}</p>
              <p className="text-sm text-black">ناسنامەی بەکارهێنەر: {user.uid.substring(0, 8)}...</p>
            </div>
          </div>
          <button
            type="button"
            onClick={handleSignOut}
            className="w-full px-4 py-2 bg-[var(--danger-500)] text-white rounded-md hover:bg-[var(--danger-700)] transition-colors flex items-center justify-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            دەرچوون
          </button>
        </div>
      ) : (
        <div>
          <form onSubmit={handleSignIn} className="space-y-5">
            <div>
              <label htmlFor="email" className="block mb-2 text-sm font-medium text-black">ئیمەیل</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none text-[var(--gray-500)]">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                  </svg>
                </div>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full pl-12 py-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors"
                  placeholder="ئیمەیلەکەت بنووسە"
                  required
                  dir="rtl"
                  spellCheck="false"
                />
              </div>
            </div>

            <div>
              <div className="mb-2">
                <label htmlFor="password" className="text-sm font-medium text-black">وشەی نهێنی</label>
              </div>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none text-[var(--gray-500)]">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pl-12 py-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors"
                  placeholder="وشەی نهێنیت بنووسە"
                  required
                  dir="rtl"
                />
              </div>
            </div>

            <div className="pt-2">
              <button
                type="submit"
                className="w-full px-4 py-3 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors font-medium flex items-center justify-center cursor-pointer"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                </svg>
                چوونەژوورەوە
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
}
