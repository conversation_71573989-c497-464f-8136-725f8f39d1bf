'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { doc, getDoc, collection, query, where, getDocs, limit, Timestamp, runTransaction } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import FeaturedImageComponent from '@/components/FeaturedImageComponent';
import RelatedPostCard from '@/components/RelatedPostCard';
import PostContent from '@/components/PostContent';

interface Post {
  id: string;
  title: string;
  content: string;
  featuredImage?: string;
  category?: string;
  tags?: string[];
  date: Timestamp;
  viewCount: number;
  author: string;
}

interface RelatedPost {
  id: string;
  title: string;
  thumbnailImage?: string;
  date: Timestamp;
  status: string;
}

interface PostPageClientProps {
  id: string;
}

export default function PostPageClient({ id: postId }: PostPageClientProps) {
  const [post, setPost] = useState<Post | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<RelatedPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [categoryName, setCategoryName] = useState<string>('');

  // This useEffect will run only once when the component mounts
  useEffect(() => {
    // We'll increment the view count on every page view
    console.log(`Loading post ${postId} and incrementing view count`);

    async function fetchPostAndIncrementView() {
      try {
        setIsLoading(true);
        const postRef = doc(db, 'posts', postId);
        const postSnap = await getDoc(postRef);

        if (postSnap.exists()) {
          const postData = { id: postSnap.id, ...postSnap.data() } as Post;
          setPost(postData);

          // Increment view count asynchronously to avoid blocking page load
          setTimeout(async () => {
            try {
              console.log(`Incrementing view count for post ${postId}`);

              // Use a simpler transaction to reduce database load
              await runTransaction(db, async (transaction) => {
                const docInTransaction = await transaction.get(postRef);

                if (docInTransaction.exists()) {
                  const currentCount = docInTransaction.data()?.viewCount || 0;
                  const updatedCount = currentCount + 1;

                  transaction.update(postRef, {
                    viewCount: updatedCount
                  });

                  // Update local state optimistically
                  setPost(prevPost => prevPost ? { ...prevPost, viewCount: updatedCount } : null);
                }
              });

              console.log(`Successfully incremented view count`);
            } catch (error) {
              console.error("Error incrementing view count:", error);
              // Silently fail to not affect user experience
            }
          }, 1000); // Delay by 1 second to not block page load

          // Fetch category name if post has a category
          if (postData.category) {
            try {
              // First check if the category is a slug or a name
              const categoriesCollection = collection(db, 'categories');
              const categoryQuery = query(categoriesCollection, where('slug', '==', postData.category));
              const categorySnapshot = await getDocs(categoryQuery);

              if (!categorySnapshot.empty) {
                // If we found a category with this slug, use its name
                const categoryData = categorySnapshot.docs[0].data();
                setCategoryName(categoryData.name);
                console.log(`Found category name: ${categoryData.name} for slug: ${postData.category}`);
              } else {
                // If not found, the category field might already be the name
                setCategoryName(postData.category);
                console.log(`Using category as is: ${postData.category}`);
              }
            } catch (error) {
              console.error('Error fetching category name:', error);
              // Fallback to using the category as is
              setCategoryName(postData.category);
            }

            // Fetch related posts from the same category
            const postsCollection = collection(db, 'posts');

            // Use a simpler query that doesn't require the composite index
            const relatedPostsQuery = query(
              postsCollection,
              where('category', '==', postData.category),
              limit(10) // Get more to filter client-side
            );

            const relatedPostsSnapshot = await getDocs(relatedPostsQuery);
            const relatedPostsList = relatedPostsSnapshot.docs
              .map(doc => ({
                id: doc.id,
                title: doc.data().title,
                thumbnailImage: doc.data().thumbnailImage,
                date: doc.data().date,
                status: doc.data().status
              }))
              .filter(relatedPost =>
                relatedPost.id !== postId &&
                relatedPost.status === 'بڵاوکراوەتەوە'
              )
              .sort((a, b) => b.date?.toMillis() - a.date?.toMillis())
              .slice(0, 3) as RelatedPost[];

            setRelatedPosts(relatedPostsList);
          }
        } else {
          setError('بابەت نەدۆزرایەوە');
        }
      } catch (err) {
        console.error('Error fetching post:', err);
        setError('هەڵە لە هێنانی بابەت');
      } finally {
        setIsLoading(false);
      }
    }

    if (postId) {
      fetchPostAndIncrementView();
    }

    // Cleanup function
    return () => {
      // Reset state when component unmounts or postId changes
      setPost(null);
      setRelatedPosts([]);
      setError(null);
    };
  }, [postId]); // Only re-run if postId changes

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="flex justify-center items-center h-64">
              <div className="inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin"></div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="bg-red-50 border border-red-200 text-red-700 p-6 rounded-md">
              <h2 className="text-xl font-bold mb-2">هەڵە</h2>
              <p>{error}</p>
              <Link href="/" className="mt-4 inline-block px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors">
                گەڕانەوە بۆ سەرەکی
              </Link>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Post header */}
          <div className="mb-8">
            {post.category && (
              <Link href={`/category/${post.category}`} className="inline-block bg-[var(--primary-100)] text-[var(--primary-700)] px-3 py-1 text-sm font-semibold rounded-md mb-4">
                {categoryName || post.category}
              </Link>
            )}
            <h1 className="text-3xl md:text-4xl font-bold text-black mb-4" translate="yes">{post.title}</h1>
            <div className="flex items-center text-gray-500 text-sm" translate="no">
              <span className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                {post.date ? new Date(post.date.toDate()).toLocaleDateString('ku-IQ') : ''}
              </span>
              <span className="mx-2">•</span>
              <span className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                {post.viewCount || 0} بینین
              </span>

            </div>
          </div>

          {/* Featured Image */}
          {post.featuredImage && (
            <FeaturedImageComponent
              imageUrl={post.featuredImage}
              title={post.title}
            />
          )}

          {/* Post content */}
          <PostContent content={post.content} />

          {/* Tags */}
          {post.tags && post.tags.length > 0 && (
            <div className="mt-8 pt-6 border-t border-gray-200">
              <h3 className="text-lg font-bold text-black mb-4" translate="no">تاگەکان</h3>
              <div className="flex flex-wrap gap-2">
                {post.tags.map((tag, index) => (
                  <Link
                    key={index}
                    href={`/tag/${tag}`}
                    className="bg-gray-100 text-gray-800 px-3 py-1 rounded-md text-sm hover:bg-gray-200 transition-colors"
                    translate="yes"
                  >
                    {tag}
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* Related posts */}
          {relatedPosts.length > 0 && (
            <div className="mt-12 pt-8 border-t border-gray-200">
              <h3 className="text-xl font-bold text-black mb-6" translate="no">بابەتی پەیوەندیدار</h3>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
                {relatedPosts.map((relatedPost) => (
                  <RelatedPostCard
                    key={relatedPost.id}
                    id={relatedPost.id}
                    title={relatedPost.title}
                    thumbnailImage={relatedPost.thumbnailImage}
                    date={relatedPost.date}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
}
