import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';

export default function TranslateTestPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <h1 className="text-3xl md:text-4xl font-bold text-black mb-8" translate="yes">
            تاقیکردنەوەی گۆڕینی زمان
          </h1>
          
          <div className="space-y-6">
            <section className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-2xl font-bold text-black mb-4" translate="yes">
                ناونیشانی یەکەم
              </h2>
              <p className="text-gray-700 mb-4" translate="yes">
                ئەمە دەقێکی تاقیکردنەوەیە بۆ بینینی ئەوەی کە ئایا گووگڵ ترانسلەیت دەتوانێت ئەم ناوەڕۆکە وەربگێڕێت یان نا. ئەم دەقە بە زمانی کوردی نووسراوە و دەبێت بە ئاسانی وەربگێردرێت.
              </p>
              <p className="text-gray-700" translate="yes">
                کاتێک کە گووگڵ ترانسلەیت کاردەکات، دەبێت بتوانێت ئەم دەقە بناسێتەوە و وەربگێڕێت بۆ زمانەکانی تر وەک ئینگلیزی، عەرەبی، فارسی و هتد.
              </p>
            </section>

            <section className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-2xl font-bold text-black mb-4" translate="yes">
                ناونیشانی دووەم
              </h2>
              <div className="prose prose-lg max-w-none blog-content" translate="yes">
                <p>ئەمە نموونەیەکی دیکەی دەقە کە لە ناو کۆمپۆنێنتی PostContent دا بەکاردەهێنرێت. ئەم دەقە دەبێت بە هەمان شێوە وەربگێردرێت.</p>
                
                <h3>ژێرناونیشان</h3>
                <p>ئەمە پەرەگرافێکی دیکەیە کە زانیاری زیاتر لەخۆدەگرێت. دەبێت گووگڵ ترانسلەیت بتوانێت ئەم هەموو دەقانە بناسێتەوە.</p>
                
                <ul>
                  <li>خاڵی یەکەم</li>
                  <li>خاڵی دووەم</li>
                  <li>خاڵی سێیەم</li>
                </ul>
              </div>
            </section>

            <section className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-2xl font-bold text-black mb-4" translate="no">
                ناونیشانی نەگۆڕ (translate="no")
              </h2>
              <p className="text-gray-700 mb-4" translate="yes">
                ئەم بەشە نیشاندەدات کە چۆن دەتوانین کۆنترۆڵی وەرگێڕان بکەین. ناونیشانەکە نابێت وەربگێردرێت بەڵام ئەم دەقە دەبێت وەربگێردرێت.
              </p>
            </section>

            <section className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-2xl font-bold text-black mb-4" translate="yes">
                تاقیکردنەوەی ناوەڕۆکی HTML
              </h2>
              <div 
                className="prose prose-lg max-w-none blog-content"
                translate="yes"
                dangerouslySetInnerHTML={{
                  __html: `
                    <p>ئەمە ناوەڕۆکێکە کە بە dangerouslySetInnerHTML دانراوە، وەک ئەوەی لە PostContent کۆمپۆنێنت بەکاردەهێنرێت.</p>
                    <h3>ژێرناونیشانی HTML</h3>
                    <p>ئەم دەقە دەبێت بە هەمان شێوە وەربگێردرێت وەک دەقی ئاسایی.</p>
                    <p><strong>دەقی قەڵەو</strong> و <em>دەقی لار</em> هەردووکیان دەبێت وەربگێردرێن.</p>
                  `
                }}
              />
            </section>

            <section className="bg-white p-6 rounded-lg shadow-md">
              <h2 className="text-2xl font-bold text-black mb-4" translate="yes">
                زانیاری تەکنیکی
              </h2>
              <div className="bg-gray-100 p-4 rounded-md" translate="no">
                <p className="text-sm text-gray-600 mb-2">Technical Information (should not be translated):</p>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>Language: ckb (Central Kurdish)</li>
                  <li>Direction: RTL</li>
                  <li>Font: Noto Kufi Arabic</li>
                  <li>Google Translate Meta: Enabled</li>
                </ul>
              </div>
            </section>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
