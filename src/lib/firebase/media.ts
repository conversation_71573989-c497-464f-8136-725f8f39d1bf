import { ref, listAll, getDownloadURL, getMetadata, deleteObject } from 'firebase/storage';
import { storage } from './config';

export interface MediaFile {
  name: string;
  fullPath: string;
  url: string;
  contentType: string;
  size: number;
  timeCreated: string;
  folder: string;
}

/**
 * List all media files from Firebase Storage
 */
export async function listMediaFiles(): Promise<MediaFile[]> {
  try {
    const mediaFiles: MediaFile[] = [];
    
    // Define folders to scan
    const folders = ['posts/featured', 'posts/thumbnails', 'posts/content'];
    
    for (const folder of folders) {
      const folderRef = ref(storage, folder);
      const filesList = await listAll(folderRef);
      
      // Process each file in the folder
      for (const fileRef of filesList.items) {
        try {
          const [url, metadata] = await Promise.all([
            getDownloadURL(fileRef),
            getMetadata(fileRef)
          ]);
          
          mediaFiles.push({
            name: fileRef.name,
            fullPath: fileRef.fullPath,
            url,
            contentType: metadata.contentType || 'unknown',
            size: metadata.size || 0,
            timeCreated: metadata.timeCreated || '',
            folder: folder.split('/').pop() || folder
          });
        } catch (error) {
          console.error(`Error processing file ${fileRef.name}:`, error);
        }
      }
    }
    
    // Sort by creation date (newest first)
    return mediaFiles.sort((a, b) => 
      new Date(b.timeCreated).getTime() - new Date(a.timeCreated).getTime()
    );
  } catch (error) {
    console.error('Error listing media files:', error);
    throw error;
  }
}

/**
 * Delete a file from Firebase Storage
 */
export async function deleteMediaFile(filePath: string): Promise<void> {
  try {
    const fileRef = ref(storage, filePath);
    await deleteObject(fileRef);
  } catch (error) {
    console.error('Error deleting file:', error);
    throw error;
  }
}
