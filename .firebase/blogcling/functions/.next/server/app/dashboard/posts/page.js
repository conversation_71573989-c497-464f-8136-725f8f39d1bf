(()=>{var e={};e.id=261,e.ids=[261],e.modules={1254:(e,r,t)=>{Promise.resolve().then(t.bind(t,6001))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6001:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var s=t(60687),a=t(43210),o=t(16189),i=t(85814),n=t.n(i),l=t(24157),c=t(31596),d=t(75535),p=t(71557),u=t(24665);function m(){let{user:e,loading:r}=(0,l.A)(),t=(0,o.useRouter)(),[i,m]=(0,a.useState)([]),[x,h]=(0,a.useState)(!0),[g,f]=(0,a.useState)(null),b=async e=>{if(confirm("ئایا دڵنیایت دەتەوێت ئەم بابەتە بسڕیتەوە؟"))try{let r=(0,d.H9)(p.db,"posts",e),t=await (0,d.x7)(r);if(t.exists()){let s=t.data(),a=[];s.featuredImage&&"string"==typeof s.featuredImage&&a.push((0,u.jn)(s.featuredImage)),s.thumbnailImage&&"string"==typeof s.thumbnailImage&&a.push((0,u.jn)(s.thumbnailImage)),await Promise.all(a),await (0,d.kd)(r),m(i.filter(r=>r.id!==e)),alert("بابەت بە سەرکەوتوویی سڕایەوە")}else throw Error("Post not found")}catch(e){console.error("Error deleting post:",e),f("هەڵە لە سڕینەوەی بابەت")}},v=e=>{t.push(`/dashboard/posts/edit/${e}`)};if(r)return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("p",{className:"text-xl",children:"باری ڕاستاندنەکە بارکردن..."})});if(!e)return null;let w=e=>{if(!e)return"N/A";try{return e.toDate().toLocaleDateString("ku",{year:"numeric",month:"long",day:"numeric"})}catch(e){return console.error("Error formatting date:",e),"N/A"}};return(0,s.jsx)(c.A,{children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-black",children:"بابەتەکان"}),(0,s.jsx)(n(),{href:"/dashboard/posts/new",className:"px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors font-medium",style:{color:"white"},children:"بابەتی نوێ"})]}),g&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md",children:g}),x?(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[(0,s.jsx)("div",{className:"inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin mb-4"}),(0,s.jsx)("p",{className:"text-black",children:"بارکردنی بابەتەکان..."})]}):0===i.length?(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[(0,s.jsx)("div",{className:"text-5xl mb-4",children:"\uD83D\uDCDD"}),(0,s.jsx)("h3",{className:"text-xl font-bold text-black mb-2",children:"هیچ بابەتێک نییە"}),(0,s.jsx)("p",{className:"text-black mb-4",children:'دەتوانیت یەکەم بابەت دروست بکەیت بە کرتەکردن لە دوگمەی "بابەتی نوێ"'}),(0,s.jsx)(n(),{href:"/dashboard/posts/new",className:"inline-block px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors font-medium cursor-pointer",style:{color:"white"},children:"بابەتی نوێ"})]}):(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-[var(--primary-50)]",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{scope:"col",className:"px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider",children:"وێنە"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider",children:"ناونیشان"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider",children:"دۆخ"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider",children:"بەروار"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider",children:"کردارەکان"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:i.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("div",{className:"flex items-center justify-center",children:e.thumbnailImage?(0,s.jsx)("div",{className:"h-16 w-16 relative rounded-md overflow-hidden",children:(0,s.jsx)("img",{src:e.thumbnailImage,alt:e.title,className:"h-full w-full object-cover"})}):(0,s.jsx)("div",{className:"h-16 w-16 bg-gray-200 flex items-center justify-center rounded-md",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})})})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-black",children:e.title}),void 0!==e.viewCount&&(0,s.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:(0,s.jsxs)("span",{className:"inline-flex items-center",children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3 w-3 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]}),e.viewCount," بینین"]})})]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${"بڵاوکراوەتەوە"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:e.status})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-black",children:w(e.date)}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex items-center justify-end space-x-3 space-x-reverse",children:[(0,s.jsx)("button",{type:"button",onClick:()=>v(e.id),className:"text-[var(--primary-600)] hover:text-[var(--primary-800)] transition-colors p-2 rounded-full hover:bg-[var(--primary-50)] cursor-pointer",title:"دەستکاری",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),(0,s.jsx)("button",{type:"button",onClick:()=>b(e.id),className:"text-[var(--danger-600)] hover:text-[var(--danger-800)] transition-colors p-2 rounded-full hover:bg-[var(--danger-50)] cursor-pointer",title:"سڕینەوە",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})})]},e.id))})]})})})]})})}},9502:(e,r,t)=>{Promise.resolve().then(t.bind(t,45619))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},24665:(e,r,t)=>{"use strict";t.d(r,{jn:()=>d,If:()=>n,V1:()=>c,PX:()=>l});var s=t(70146),a=t(71557);async function o(e,r=1200,t=1200,s=.85){return new Promise((a,o)=>{try{let i=new Image;i.onload=()=>{let n=i.width,l=i.height;if(n>r||l>t){let e=n/l;n>r&&(l=(n=r)/e),l>t&&(n=(l=t)*e)}let c=document.createElement("canvas");c.width=n,c.height=l;let d=c.getContext("2d");if(!d)return void o(Error("Could not get canvas context"));d.drawImage(i,0,0,n,l);let p=e.split(";")[0].split(":")[1]||"image/jpeg",u=c.toDataURL(p,s);a(u)},i.onerror=()=>{console.error("Error loading image for optimization"),a(e)},i.src=e}catch(r){console.error("Error optimizing image:",r),a(e)}})}async function i(e,r,t,i,n,l){try{let c=e;if(i&&n)try{c=await o(e,i,n,l),console.log("Image optimized successfully")}catch(e){console.error("Error optimizing image:",e)}let d=c.match(/^data:([A-Za-z-+/]+);base64,(.+)$/);if(!d||3!==d.length)throw Error("Invalid base64 image format");let p=d[1],u=d[2],m="jpg";p.includes("png")?m="png":p.includes("gif")?m="gif":p.includes("webp")&&(m="webp");let x=t||`${Date.now()}.${m}`,h=(0,s.KR)(a.IG,`${r}/${x}`);try{let e=await (0,s.ls)(h,u,"base64",{contentType:p,cacheControl:"public, max-age=31536000"}),r=await (0,s.qk)(e.ref);return console.log("Image uploaded successfully to Firebase Storage:",r),r}catch(e){return console.error("Firebase Storage upload failed:",e),e instanceof Error&&(e.message.includes("unauthorized")?(console.warn("Storage permission denied. Using base64 fallback. Please update Firebase Storage rules."),alert("تکایە ڕێساکانی Firebase Storage نوێ بکەرەوە بۆ ڕێگەدان بە بارکردنی وێنەکان")):e.message.includes("CORS")&&console.warn("CORS error detected. Using base64 fallback. Please configure CORS for Firebase Storage.")),console.warn("Falling back to storing image as base64 in Firestore"),c}}catch(r){return console.error("Error processing image:",r),console.warn("Error occurred, falling back to base64 storage"),e}}function n(e){let r=Date.now(),t=Math.random().toString(36).substring(2,8);if(e){let s=e.split(".").pop()||"jpg";return`${r}-${t}.${s}`}return`${r}-${t}.jpg`}async function l(e,r){return i(e,"posts/thumbnails",r,600,600,.7)}async function c(e,r){return i(e,"posts/featured",r,1600,1600,.85)}async function d(e){try{if(!e||!e.includes("firebasestorage.googleapis.com"))return console.warn("Not a Firebase Storage URL:",e),!1;let r=new URL(e),t=decodeURIComponent(r.pathname.split("/o/")[1]?.split("?")[0]);if(!t)return console.warn("Could not extract path from URL:",e),!1;let o=(0,s.KR)(a.IG,t);return await (0,s.XR)(o),console.log("File deleted successfully:",t),!0}catch(e){return console.error("Error deleting file:",e),!1}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},45619:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/dashboard/posts/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/dashboard/posts/page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},61646:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=t(65239),a=t(48088),o=t(88170),i=t.n(o),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let c={children:["",{children:["dashboard",{children:["posts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,45619)),"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/dashboard/posts/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/dashboard/posts/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/posts/page",pathname:"/dashboard/posts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,724,934,814,791],()=>t(61646));module.exports=s})();