'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useSidebar } from '@/lib/context/SidebarContext';

// Define sidebar menu items
const menuItems = [
  {
    title: 'بلۆگ',
    path: '/dashboard/posts',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
      </svg>
    ),
  },
  {
    title: 'پۆلەکان',
    path: '/dashboard/categories',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
      </svg>
    ),
  },
  {
    title: 'تاگەکان',
    path: '/dashboard/tags',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
      </svg>
    ),
  },
  {
    title: 'میدیا',
    path: '/dashboard/media',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
    ),
  },
];

export default function Sidebar() {
  const pathname = usePathname();
  const { isOpen, isInitialized } = useSidebar();

  // Don't render anything until initialization is complete to prevent flicker
  if (!isInitialized) {
    return null;
  }

  return (
    <div
      className={`h-screen w-64 bg-[var(--sidebar-bg)] text-white fixed right-0 top-0 pt-16 overflow-y-auto shadow-lg z-10 transition-all duration-300 transform ${
        isOpen ? 'translate-x-0' : 'translate-x-full'
      }`}
    >
      <div className="p-4">
        <div className="mb-6 text-center">
          <div className="w-auto h-16 mx-auto mb-2 bg-white rounded-lg flex items-center justify-center shadow-md px-4">
            <img src="/logomain.svg" alt="Ramiyari Logo" className="h-10 w-auto" />
          </div>
          <div className="text-sm text-white font-medium">بەخێربێیت</div>
        </div>

        <ul className="space-y-1">
          {menuItems.map((item) => (
            <li key={item.path} className="mb-3">
              <Link
                href={item.path}
                className={`flex items-center p-2.5 rounded-md cursor-pointer transition-colors ${
                  pathname.includes(item.path)
                    ? 'bg-[var(--sidebar-active)] text-white'
                    : 'text-white hover:bg-[var(--sidebar-hover)]'
                }`}
              >
                <span className="ml-3 text-white">{item.icon}</span>
                <span className="font-medium text-white">{item.title}</span>
              </Link>
            </li>
          ))}
        </ul>

        <div className="mt-8 pt-6 border-t border-[var(--primary-700)]">
          <div className="text-xs text-white font-medium mb-2">سیستەمی بەڕێوەبردنی ناوەڕۆک</div>
          <div className="text-xs text-white">© {new Date().getFullYear()}</div>
        </div>
      </div>
    </div>
  );
}
