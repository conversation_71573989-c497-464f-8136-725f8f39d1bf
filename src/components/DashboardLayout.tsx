'use client';

import { ReactNode } from 'react';
import DashboardHeader from './DashboardHeader';
import Sidebar from './Sidebar';
import { SidebarProvider, useSidebar } from '@/lib/context/SidebarContext';

interface DashboardLayoutProps {
  children: ReactNode;
}

function DashboardContent({ children }: DashboardLayoutProps) {
  const { isOpen, isInitialized } = useSidebar();

  return (
    <div className="min-h-screen bg-[var(--background)]">
      <DashboardHeader />
      <Sidebar />
      <div className={`pt-28 ${isInitialized && isOpen ? 'pr-64' : 'pr-0'} transition-all duration-300`}>
        <main className="p-6">
          {children}
        </main>
        <footer className="mt-8 p-6 text-center text-black text-sm">
          <p>سیستەمی بەڕێوەبردنی ناوەڕۆک © {new Date().getFullYear()}</p>
        </footer>
      </div>
    </div>
  );
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <SidebarProvider>
      <DashboardContent children={children} />
    </SidebarProvider>
  );
}
