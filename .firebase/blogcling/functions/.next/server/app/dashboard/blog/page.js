(()=>{var e={};e.id=262,e.ids=[262],e.modules={2844:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(60687),n=r(24157);function o({children:e}){return(0,s.jsx)(n.O,{children:e})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10182:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(65239),n=r(48088),o=r(88170),i=r.n(o),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let c={children:["",{children:["dashboard",{children:["blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80638)),"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/dashboard/blog/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/dashboard/blog/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/blog/page",pathname:"/dashboard/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10396:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},24157:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,O:()=>a});var s=r(60687),n=r(43210);r(98537);let o=(0,n.createContext)({user:null,loading:!0}),i=()=>(0,n.useContext)(o);function a({children:e}){let[t,r]=(0,n.useState)(null),[i,a]=(0,n.useState)(!0);return(0,s.jsx)(o.Provider,{value:{user:t,loading:i},children:e})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29528:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(60687);r(43210);var n=r(16189);function o(){return(0,n.useRouter)(),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("p",{className:"text-xl",children:"ڕەوانەکردن بۆ بەشی بابەتەکان..."})})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37366:e=>{"use strict";e.exports=require("dns")},55511:e=>{"use strict";e.exports=require("crypto")},58015:(e,t,r)=>{Promise.resolve().then(r.bind(r,98122))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68604:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71557:(e,t,r)=>{"use strict";let s;r.d(t,{IG:()=>d,db:()=>c,j2:()=>l});var n=r(67989),o=r(75535),i=r(65553),a=r(70146);s=(0,n.Dk)().length?(0,n.Dk)()[0]:(0,n.Wp)({apiKey:"AIzaSyDrLmTWQcbGNPUlYcTEWIGNcZbCYe7J8og",authDomain:"blogcling.firebaseapp.com",projectId:"blogcling",storageBucket:"blogcling.firebasestorage.app",messagingSenderId:"771192391646",appId:"1:771192391646:web:37954fb7162ac16d8fff4f",measurementId:"G-NYZ36H3N04"});let l=(0,i.xI)(s);l.useDeviceLanguage();let c=(0,o.aU)(s),d=(0,a.c7)(s)},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79023:(e,t,r)=>{Promise.resolve().then(r.bind(r,80638))},79551:e=>{"use strict";e.exports=require("url")},80638:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/dashboard/blog/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/app/dashboard/blog/page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},87271:(e,t,r)=>{Promise.resolve().then(r.bind(r,29528))},91645:e=>{"use strict";e.exports=require("net")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>c});var s=r(37413),n=r(2202),o=r.n(n),i=r(64988),a=r.n(i);r(61135);var l=r(98122);let c={title:"ڕامیاری",description:"ڕامیاری، سەرچاوەی هەواڵ و زانیاری ڕامیاری و کۆمەڵایەتی"};function d({children:e}){return(0,s.jsxs)("html",{lang:"ckb",dir:"rtl",children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("link",{rel:"icon",href:"/favicon.svg",type:"image/svg+xml"}),(0,s.jsx)("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/logomain.svg"}),(0,s.jsx)("link",{rel:"manifest",href:"/manifest.webmanifest"}),(0,s.jsx)("meta",{name:"theme-color",content:"#8f5826"}),(0,s.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,s.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,s.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100..900&display=swap",rel:"stylesheet"}),(0,s.jsx)("link",{rel:"stylesheet",href:"https://cdnjs.cloudflare.com/ajax/libs/jodit/3.24.5/jodit.min.css"})]}),(0,s.jsx)("body",{className:`${o().variable} ${a().variable} antialiased`,children:(0,s.jsx)(l.default,{children:e})})]})}},94735:e=>{"use strict";e.exports=require("events")},94967:(e,t,r)=>{Promise.resolve().then(r.bind(r,2844))},98122:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/components/AuthProviderWrapper.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/nextjs/cmsfile/cms/src/components/AuthProviderWrapper.tsx","default")},98537:(e,t,r)=>{"use strict";r.d(t,{Bh:()=>a,CI:()=>o,HW:()=>i});var s=r(65553),n=r(71557);async function o(){try{return await (0,s.CI)(n.j2),{success:!0,error:null}}catch(e){return{success:!1,error:e.message}}}function i(){return n.j2.currentUser}function a(e){return(0,s.hg)(n.j2,e)}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,724,934],()=>r(10182));module.exports=s})();