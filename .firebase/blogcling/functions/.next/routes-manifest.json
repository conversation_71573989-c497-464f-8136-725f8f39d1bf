{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/category/[slug]", "regex": "^/category/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/category/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/dashboard/posts/edit/[id]", "regex": "^/dashboard/posts/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/posts/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/post/[id]", "regex": "^/post/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/post/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/tag/[slug]", "regex": "^/tag/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/tag/(?<nxtPslug>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/dashboard/blog", "regex": "^/dashboard/blog(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/blog(?:/)?$"}, {"page": "/dashboard/categories", "regex": "^/dashboard/categories(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/categories(?:/)?$"}, {"page": "/dashboard/media", "regex": "^/dashboard/media(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/media(?:/)?$"}, {"page": "/dashboard/posts", "regex": "^/dashboard/posts(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/posts(?:/)?$"}, {"page": "/dashboard/posts/new", "regex": "^/dashboard/posts/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/posts/new(?:/)?$"}, {"page": "/dashboard/tags", "regex": "^/dashboard/tags(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/tags(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/posts", "regex": "^/posts(?:/)?$", "routeKeys": {}, "namedRegex": "^/posts(?:/)?$"}, {"page": "/search", "regex": "^/search(?:/)?$", "routeKeys": {}, "namedRegex": "^/search(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}