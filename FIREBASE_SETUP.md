# Firebase Setup for Next.js CMS

This document provides instructions for setting up Firebase in your Next.js CMS project.

## Prerequisites

1. A Firebase account (create one at [firebase.google.com](https://firebase.google.com/) if you don't have one)
2. Node.js and npm installed on your machine
3. Firebase CLI installed globally (installed with `npm install -g firebase-tools`)

## Step 1: Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click "Add project" and follow the setup wizard
3. Give your project a name and configure the settings as needed
4. Enable Google Analytics if desired (optional)
5. Click "Create project"

## Step 2: Register Your Web App

1. In the Firebase Console, select your project
2. Click on the web icon (</>) to add a web app
3. Register your app with a nickname (e.g., "nextjs-cms")
4. Check the box for "Also set up Firebase Hosting" if desired (optional)
5. Click "Register app"
6. You'll be shown your Firebase configuration object - keep this page open as you'll need these values

## Step 3: Configure Environment Variables

1. Create a `.env.local` file in the root of your project (copy from `.env.local.example`)
2. Add your Firebase configuration values to the `.env.local` file:

```
NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your-measurement-id
```

## Step 4: Enable Email Authentication

For this project, we're using only Email/Password authentication:

1. In the Firebase Console, go to "Authentication" > "Sign-in method"
2. Enable the "Email/Password" authentication method
3. Make sure other authentication methods are disabled

## Step 5: Set Up Firestore Database (Optional)

If you want to use Firestore:

1. In the Firebase Console, go to "Firestore Database"
2. Click "Create database"
3. Choose "Start in production mode" or "Start in test mode" (test mode allows anyone to read/write to your database)
4. Select a location for your database
5. Click "Enable"

## Step 6: Set Up Storage (Optional)

If you want to use Firebase Storage:

1. In the Firebase Console, go to "Storage"
2. Click "Get started"
3. Review and accept the default security rules (or modify them as needed)
4. Click "Next"
5. Select a location for your storage bucket
6. Click "Done"

## Step 7: Set Up Firebase CLI (Optional)

If you want to use Firebase CLI for deployment:

1. Log in to Firebase:

```bash
npm run firebase:login
```

2. Update the `.firebaserc` file with your Firebase project ID:

```json
{
  "projects": {
    "default": "YOUR_FIREBASE_PROJECT_ID"
  }
}
```

## Step 8: Deploy to Firebase Hosting (Optional)

To deploy your Next.js app to Firebase Hosting:

1. Build and export your Next.js app:

```bash
npm run build
```

2. Deploy to Firebase Hosting:

```bash
npm run firebase:deploy:hosting
```

## Step 9: Run Your Next.js App

1. Start your Next.js development server:

```bash
npm run dev
```

2. Open your browser and navigate to `http://localhost:3000`
3. You should see the Firebase Authentication test component on the home page

## Troubleshooting

- If you encounter CORS errors, make sure your Firebase project's authentication settings have the correct domains listed (localhost:3000 for development)
- If authentication doesn't work, check that you've enabled the authentication methods in the Firebase Console
- Make sure your environment variables are correctly set in `.env.local`

## Additional Resources

- [Firebase Documentation](https://firebase.google.com/docs)
- [Next.js Documentation](https://nextjs.org/docs)
- [Firebase JavaScript SDK Reference](https://firebase.google.com/docs/reference/js)
- [Firebase CLI Reference](https://firebase.google.com/docs/cli)
- [Firebase Hosting](https://firebase.google.com/docs/hosting)
