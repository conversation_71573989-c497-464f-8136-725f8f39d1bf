const { chromium } = require('playwright');

(async () => {
  // Launch the browser
  const browser = await chromium.launch({ 
    headless: false, // Set to false to see the browser
    slowMo: 100 // Slow down operations by 100ms
  });
  
  // Create a new browser context
  const context = await browser.newContext();
  
  // Open a new page
  const page = await context.newPage();
  
  try {
    // Navigate to the login page
    console.log('Navigating to the login page...');
    await page.goto('http://localhost:3000/login');
    
    // Wait for the login form to load
    console.log('Waiting for the login form to load...');
    await page.waitForSelector('form', { timeout: 10000 });
    
    // Fill in the login form
    console.log('Filling in login credentials...');
    // Replace with actual email and password fields and values
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'your-password');
    
    // Submit the form
    console.log('Submitting login form...');
    await page.click('button[type="submit"]');
    
    // Wait for navigation after login
    console.log('Waiting for navigation after login...');
    await page.waitForNavigation({ timeout: 30000 });
    
    // Navigate to the new post page
    console.log('Navigating to the new post page...');
    await page.goto('http://localhost:3000/dashboard/posts/new');
    
    // Wait for the editor to load
    console.log('Waiting for the editor to load...');
    await page.waitForSelector('.lexical-editor-container', { timeout: 10000 });
    
    // Click on the image upload button
    console.log('Clicking on the image upload button...');
    const uploadButton = await page.locator('button[aria-label="Insert Image"]');
    await uploadButton.click();
    
    // Wait for the file input to be available
    console.log('Setting up file input...');
    
    // Create a file input for uploading
    const fileChooserPromise = page.waitForEvent('filechooser');
    await uploadButton.click();
    const fileChooser = await fileChooserPromise;
    
    // Upload a test image
    console.log('Uploading test image...');
    await fileChooser.setFiles('./test-image.jpg');
    
    // Wait for the upload to complete and image to appear
    console.log('Waiting for upload to complete...');
    await page.waitForSelector('.editor-image', { timeout: 30000 });
    
    // Check if the image is visible
    console.log('Checking if image is visible...');
    const imageVisible = await page.isVisible('.editor-image');
    console.log(`Image visible: ${imageVisible}`);
    
    // Take a screenshot
    console.log('Taking a screenshot...');
    await page.screenshot({ path: 'editor-with-image.png', fullPage: true });
    
    // Get console logs
    console.log('Console logs from the page:');
    page.on('console', msg => {
      console.log(`PAGE LOG: ${msg.text()}`);
    });
    
    // Wait a bit to see the result
    await page.waitForTimeout(10000);
    
    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Test failed:', error);
    await page.screenshot({ path: 'error-screenshot.png', fullPage: true });
  } finally {
    // Keep the browser open for manual inspection
    // Comment out the line below if you want to keep the browser open
    // await browser.close();
  }
})();
