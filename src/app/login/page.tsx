'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useAuth } from '@/lib/firebase/auth-context';
import FirebaseAuthWrapper from '@/components/FirebaseAuthWrapper';

export default function LoginPage() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && user) {
      // If already logged in, redirect to posts page
      router.push('/dashboard/posts');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p className="text-xl">باری ڕاستاندنەکە بارکردن...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-white">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="bg-white p-4 rounded-full inline-block shadow-md mb-6">
            <Image
              src="/logomain.svg"
              alt="<PERSON><PERSON>ri Logo"
              width={120}
              height={40}
              className="mx-auto"
              priority
            />
          </div>
          <h1 className="text-3xl font-bold text-black">سیستەمی بەڕێوەبردنی ناوەڕۆک</h1>
          <p className="text-black mt-2 mb-6 font-medium">تکایە چوونەژوورەوە بۆ بەردەوامبوون</p>
        </div>

        <div className="bg-white rounded-lg shadow-xl overflow-hidden border border-[var(--gray-200)]">
          <div className="p-1 bg-gradient-to-l from-[var(--primary-500)] to-[var(--primary-700)]"></div>
          <div className="p-6">
            <FirebaseAuthWrapper />
          </div>
        </div>

        <div className="mt-8 text-center">
          <a href="/" className="text-[var(--primary-600)] hover:text-[var(--primary-800)] transition-colors">
            گەڕانەوە بۆ پەڕەی سەرەکی
          </a>
        </div>
      </div>

      <div className="absolute bottom-4 text-center text-black text-sm">
        <p>سیستەمی بەڕێوەبردنی ناوەڕۆک © {new Date().getFullYear()}</p>
      </div>
    </div>
  );
}
