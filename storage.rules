rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Allow authenticated users to read and write their own files
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Allow public read access to post images
    match /posts/{folder}/{filename} {
      allow read: if true; // Anyone can read post images
      allow write: if request.auth != null; // Only authenticated users can write
    }

    // Default deny all other operations
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
