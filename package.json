{"name": "cms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build", "firebase:login": "firebase login", "firebase:logout": "firebase logout", "firebase:init": "firebase init", "firebase:deploy": "firebase deploy", "firebase:deploy:hosting": "firebase deploy --only hosting", "firebase:deploy:firestore": "firebase deploy --only firestore", "firebase:deploy:storage": "firebase deploy --only storage", "firebase:deploy:functions": "firebase deploy --only functions", "firebase:build-deploy": "npm run build && firebase deploy --only hosting", "generate-fake-posts": "node scripts/run-fake-posts.js", "generate-fake-posts:batch": "node -e \"require('dotenv').config({path: '.env.local'}); require('./scripts/generate-fake-posts-batch.js')\"", "generate-test-posts": "node -e \"require('dotenv').config({path: '.env.local'}); require('./scripts/generate-test-posts.js')\""}, "dependencies": {"dotenv": "^16.4.5", "firebase": "^11.6.1", "firebase-admin": "^12.0.0", "firebase-functions": "^6.3.2", "jodit": "^4.6.2", "jodit-react": "^5.2.19", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@playwright/test": "^1.52.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}