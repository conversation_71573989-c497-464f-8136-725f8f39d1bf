'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { collection, getDocs, doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import SearchBar from './SearchBar';

interface Category {
  id: string;
  name: string;
  slug: string;
}

export default function Navbar() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [currentPostCategory, setCurrentPostCategory] = useState<string | null>(null);
  const pathname = usePathname();

  // Fetch categories
  useEffect(() => {
    async function fetchCategories() {
      try {
        // Check if we already have categories in localStorage
        const cachedCategories = localStorage.getItem('navbarCategories');
        const cacheTimestamp = localStorage.getItem('navbarCategoriesTimestamp');

        // Use cache if it's less than 1 hour old
        if (cachedCategories && cacheTimestamp) {
          const parsedTimestamp = parseInt(cacheTimestamp, 10);
          const now = Date.now();
          const oneHour = 60 * 60 * 1000;

          if (now - parsedTimestamp < oneHour) {
            setCategories(JSON.parse(cachedCategories));
            return;
          }
        }

        // Fetch fresh data if cache is missing or expired
        const categoriesCollection = collection(db, 'categories');
        const categoriesSnapshot = await getDocs(categoriesCollection);
        const categoriesList = categoriesSnapshot.docs.map(doc => ({
          id: doc.id,
          name: doc.data().name,
          slug: doc.data().slug
        })) as Category[];

        // Update state
        setCategories(categoriesList);

        // Update cache
        localStorage.setItem('navbarCategories', JSON.stringify(categoriesList));
        localStorage.setItem('navbarCategoriesTimestamp', Date.now().toString());
      } catch (err) {
        console.error('Error fetching categories:', err);

        // Try to use cached data even if it's old in case of error
        const cachedCategories = localStorage.getItem('navbarCategories');
        if (cachedCategories) {
          setCategories(JSON.parse(cachedCategories));
        }
      }
    }

    fetchCategories();
  }, []);

  // Check if current page is a post and get its category
  useEffect(() => {
    async function checkPostCategory() {
      // Check if we're on a post page
      const postPathMatch = pathname.match(/^\/post\/([^\/]+)$/);
      if (postPathMatch && postPathMatch[1]) {
        const postId = postPathMatch[1];
        try {
          // Fetch the post to get its category
          const postRef = doc(db, 'posts', postId);
          const postSnap = await getDoc(postRef);

          if (postSnap.exists() && postSnap.data().category) {
            setCurrentPostCategory(postSnap.data().category);
            console.log(`Current post category: ${postSnap.data().category}`);
          }
        } catch (error) {
          console.error('Error fetching post category:', error);
        }
      } else {
        // Reset if not on a post page
        setCurrentPostCategory(null);
      }
    }

    checkPostCategory();
  }, [pathname]);

  return (
    <nav className="bg-white shadow-md sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link href="/" className="flex-shrink-0 flex items-center">
              <Image
                src="/logomain.svg"
                alt="Ramiyari Logo"
                width={120}
                height={40}
                className="h-10 w-auto"
              />
            </Link>
          </div>

          {/* Desktop menu */}
          <div className="hidden md:flex items-center space-x-4 mr-4">
            <Link
              href="/"
              className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                pathname === '/'
                  ? 'text-[var(--primary-600)] font-bold border-b-2 border-[var(--primary-600)]'
                  : 'text-black hover:text-[var(--primary-600)]'
              }`}
            >
              سەرەکی
            </Link>

            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/category/${category.slug}`}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  pathname === `/category/${category.slug}` || currentPostCategory === category.slug
                    ? 'text-[var(--primary-600)] font-bold border-b-2 border-[var(--primary-600)]'
                    : 'text-black hover:text-[var(--primary-600)]'
                }`}
              >
                {category.name}
              </Link>
            ))}
          </div>

          {/* Desktop search bar */}
          <div className="hidden md:flex items-center ml-4 w-64">
            <SearchBar compact={true} placeholder="گەڕان..." className="w-full" />
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              type="button"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-black hover:text-[var(--primary-600)] hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[var(--primary-500)]"
              aria-expanded={isMenuOpen ? "true" : "false"}
            >
              <span className="sr-only">Open main menu</span>
              {/* Icon when menu is closed */}
              {!isMenuOpen ? (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              ) : (
                /* Icon when menu is open */
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu, show/hide based on menu state */}
      {isMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            {/* Mobile search bar */}
            <div className="px-3 py-2 mb-3">
              <SearchBar placeholder="گەڕان..." />
            </div>

            <Link
              href="/"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                pathname === '/'
                  ? 'text-[var(--primary-600)] font-bold border-r-2 border-[var(--primary-600)]'
                  : 'text-black hover:text-[var(--primary-600)]'
              }`}
              onClick={() => setIsMenuOpen(false)}
            >
              سەرەکی
            </Link>

            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/category/${category.slug}`}
                className={`block px-3 py-2 rounded-md text-base font-medium ${
                  pathname === `/category/${category.slug}` || currentPostCategory === category.slug
                    ? 'text-[var(--primary-600)] font-bold border-r-2 border-[var(--primary-600)]'
                    : 'text-black hover:text-[var(--primary-600)]'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                {category.name}
              </Link>
            ))}
          </div>
        </div>
      )}
    </nav>
  );
}
