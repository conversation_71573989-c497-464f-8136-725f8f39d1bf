'use client';

import { useState } from 'react';

interface GoogleTranslatePostProps {
  postId: string;
}

export default function GoogleTranslatePost({ postId }: GoogleTranslatePostProps) {
  const [isTranslating, setIsTranslating] = useState(false);



  const translateToEnglish = () => {
    if (!isLoaded || isTranslating) return;

    setIsTranslating(true);

    try {
      // Use Google Translate URL method for more reliable translation
      const currentUrl = window.location.href;
      const translateUrl = `https://translate.google.com/translate?sl=ckb&tl=en&u=${encodeURIComponent(currentUrl)}`;

      // Open translation in the same window
      window.location.href = translateUrl;

    } catch (error) {
      console.error('Error translating page:', error);
      setIsTranslating(false);
    }
  };



  return (
    <div className="google-translate-container mb-6">
      <div className="flex items-center justify-center">
        <button
          type="button"
          onClick={translateToEnglish}
          disabled={isTranslating}
          className="group relative inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-[#8f5826] to-[#b17d4a] text-white font-bold text-lg rounded-xl shadow-lg hover:shadow-2xl transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none border-2 border-[#734620] hover:border-[#8f5826]"
          translate="no"
        >
          {isTranslating ? (
            <>
              <svg className="animate-spin -ml-1 mr-3 h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Translating...
            </>
          ) : (
            <>
              <svg className="w-6 h-6 mr-3 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
              </svg>
              Translate to EN
            </>
          )}

          {/* Shine effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-30 transform -skew-x-12 group-hover:animate-pulse rounded-xl"></div>
        </button>
      </div>

      {/* Small subtitle */}
      <div className="text-center mt-2">
        <span className="text-xs text-gray-500" translate="no">
          Powered by Google Translate
        </span>
      </div>


    </div>
  );
}
