# Firebase Cloud Functions

This directory contains Firebase Cloud Functions for the CMS project.

## Scheduled Functions

### Daily View Count Increment

A scheduled function that runs daily at 2 AM London time to add 100 views to all published posts.

## Deployment Instructions

To deploy the functions:

1. Make sure you have the Firebase CLI installed:
   ```
   npm install -g firebase-tools
   ```

2. Login to Firebase:
   ```
   npm run firebase:login
   ```

3. Build and deploy the functions:
   ```
   npm run firebase:deploy:functions
   ```

## Monitoring

You can monitor the execution of the scheduled function in the Firebase Console:

1. Go to the Firebase Console
2. Select your project
3. Navigate to "Functions" in the left sidebar
4. Check the logs for the "incrementDailyViews" function

## Troubleshooting

If the function is not running as expected:

1. Check the function logs in the Firebase Console
2. Verify that the function is deployed correctly
3. Make sure the billing is set up for your Firebase project (scheduled functions require the Blaze plan)
