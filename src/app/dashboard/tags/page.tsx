'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/firebase/auth-context';
import DashboardLayout from '@/components/DashboardLayout';
import { collection, getDocs, addDoc, deleteDoc, doc, query, where, Timestamp, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';

// Define tag type
interface Tag {
  id: string;
  name: string;
  slug: string;
  count: number;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

export default function TagsPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [tags, setTags] = useState<Tag[]>([]);
  const [newTag, setNewTag] = useState('');
  const [newSlug, setNewSlug] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Edit mode state
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingTag, setEditingTag] = useState<Tag | null>(null);
  const [editName, setEditName] = useState('');
  const [editSlug, setEditSlug] = useState('');

  // Fetch tags from Firebase
  useEffect(() => {
    async function fetchTags() {
      try {
        const tagsCollection = collection(db, 'tags');
        const tagsSnapshot = await getDocs(tagsCollection);
        const tagsList = tagsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          count: doc.data().count || 0 // Ensure count has a default value
        })) as Tag[];

        setTags(tagsList);
      } catch (err) {
        console.error('Error fetching tags:', err);
        setError('هەڵە لە هێنانی تاگەکان');
      } finally {
        setIsLoading(false);
      }
    }

    if (user) {
      fetchTags();
    }
  }, [user]);

  // Check authentication
  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  const handleAddTag = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newTag && newSlug) {
      try {
        // Check if a tag with the same slug already exists
        const tagsCollection = collection(db, 'tags');
        const q = query(tagsCollection, where('slug', '==', newSlug));
        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
          setError('تاگێک بە هەمان سلاگ بوونی هەیە');
          return;
        }

        // Create new tag object
        const newTagData = {
          name: newTag,
          slug: newSlug,
          count: 0,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now()
        };

        // Add to Firestore
        const docRef = await addDoc(tagsCollection, newTagData);

        // Update local state
        setTags([
          ...tags,
          { id: docRef.id, ...newTagData }
        ]);

        setNewTag('');
        setNewSlug('');
        setError(null);
      } catch (err) {
        console.error('Error adding tag:', err);
        setError('هەڵە لە زیادکردنی تاگ');
      }
    }
  };

  const handleDeleteTag = async (id: string) => {
    try {
      // Delete from Firestore
      const tagRef = doc(db, 'tags', id);
      await deleteDoc(tagRef);

      // Update local state
      setTags(tags.filter(tag => tag.id !== id));
    } catch (err) {
      console.error('Error deleting tag:', err);
      setError('هەڵە لە سڕینەوەی تاگ');
    }
  };

  // Handle edit tag
  const handleEditTag = (tag: Tag) => {
    setEditingTag(tag);
    setEditName(tag.name);
    setEditSlug(tag.slug);
    setIsEditMode(true);
  };

  // Handle update tag
  const handleUpdateTag = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingTag || !editName || !editSlug) return;

    try {
      // Check if another tag with the same slug already exists (excluding the current one)
      if (editSlug !== editingTag.slug) {
        const tagsCollection = collection(db, 'tags');
        const q = query(tagsCollection, where('slug', '==', editSlug));
        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
          setError('تاگێک بە هەمان سلاگ بوونی هەیە');
          return;
        }
      }

      // Update in Firestore
      const tagRef = doc(db, 'tags', editingTag.id);
      await updateDoc(tagRef, {
        name: editName,
        slug: editSlug,
        updatedAt: Timestamp.now()
      });

      // Update local state
      setTags(tags.map(tag =>
        tag.id === editingTag.id
          ? { ...tag, name: editName, slug: editSlug, updatedAt: Timestamp.now() }
          : tag
      ));

      // Reset edit mode
      setIsEditMode(false);
      setEditingTag(null);
      setEditName('');
      setEditSlug('');
      setError(null);
    } catch (err) {
      console.error('Error updating tag:', err);
      setError('هەڵە لە نوێکردنەوەی تاگ');
    }
  };

  // Cancel edit mode
  const handleCancelEdit = () => {
    setIsEditMode(false);
    setEditingTag(null);
    setEditName('');
    setEditSlug('');
    setError(null);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p className="text-xl">باری ڕاستاندنەکە بارکردن...</p>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6 text-black">تاگەکان</h1>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            {isLoading ? (
              <div className="bg-white rounded-lg shadow-md p-8 text-center">
                <div className="inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin mb-4"></div>
                <p className="text-black">بارکردنی تاگەکان...</p>
              </div>
            ) : tags.length === 0 ? (
              <div className="bg-white rounded-lg shadow-md p-8 text-center">
                <div className="text-5xl mb-4">🏷️</div>
                <h3 className="text-xl font-bold text-black mb-2">هیچ تاگێک نییە</h3>
                <p className="text-black mb-4">دەتوانیت یەکەم تاگ دروست بکەیت لە فۆرمی "زیادکردنی تاگی نوێ"</p>
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-[var(--primary-50)]">
                    <tr>
                      <th scope="col" className="px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider">
                        ناو
                      </th>
                      <th scope="col" className="px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider">
                        سلاگ
                      </th>
                      <th scope="col" className="px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider">
                        ژمارە
                      </th>
                      <th scope="col" className="px-6 py-4 text-right text-xs font-bold text-[var(--primary-700)] uppercase tracking-wider">
                        کردارەکان
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {tags.map((tag) => (
                      <tr key={tag.id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-black">{tag.name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-black">{tag.slug}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-black">{tag.count}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center justify-end space-x-3 space-x-reverse">
                            <button
                              type="button"
                              onClick={() => handleEditTag(tag)}
                              className="text-[var(--primary-600)] hover:text-[var(--primary-800)] transition-colors p-2 rounded-full hover:bg-[var(--primary-50)] cursor-pointer"
                              title="دەستکاری"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            </button>
                            <button
                              type="button"
                              onClick={() => handleDeleteTag(tag.id)}
                              className="text-[var(--danger-600)] hover:text-[var(--danger-800)] transition-colors p-2 rounded-full hover:bg-[var(--danger-50)] cursor-pointer"
                              title="سڕینەوە"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          <div className="md:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-bold mb-4 text-black">زیادکردنی تاگی نوێ</h2>
              <form onSubmit={handleAddTag}>
                <div className="mb-4">
                  <label htmlFor="name" className="block text-black text-sm font-bold mb-2">
                    ناو
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    className="w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors"
                    required
                  />
                </div>
                <div className="mb-4">
                  <label htmlFor="slug" className="block text-black text-sm font-bold mb-2">
                    سلاگ
                  </label>
                  <input
                    type="text"
                    id="slug"
                    value={newSlug}
                    onChange={(e) => setNewSlug(e.target.value)}
                    className="w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors"
                    required
                  />
                </div>
                <button
                  type="submit"
                  className="px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors font-medium w-full cursor-pointer"
                >
                  زیادکردن
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>

      {/* Edit Tag Modal */}
      {isEditMode && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-black">دەستکاریکردنی تاگ</h2>
              <button
                type="button"
                onClick={handleCancelEdit}
                className="text-gray-500 hover:text-gray-700 transition-colors"
                title="داخستن"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <form onSubmit={handleUpdateTag}>
              <div className="mb-4">
                <label htmlFor="editName" className="block text-black text-sm font-bold mb-2">
                  ناو
                </label>
                <input
                  type="text"
                  id="editName"
                  value={editName}
                  onChange={(e) => setEditName(e.target.value)}
                  className="w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors"
                  required
                />
              </div>
              <div className="mb-4">
                <label htmlFor="editSlug" className="block text-black text-sm font-bold mb-2">
                  سلاگ
                </label>
                <input
                  type="text"
                  id="editSlug"
                  value={editSlug}
                  onChange={(e) => setEditSlug(e.target.value)}
                  className="w-full py-2 px-3 border border-[var(--gray-300)] rounded-md focus:ring-2 focus:ring-[var(--primary-300)] focus:border-[var(--primary-500)] hover:border-[var(--primary-400)] transition-colors"
                  required
                />
              </div>
              <div className="flex justify-end space-x-3 space-x-reverse">
                <button
                  type="button"
                  onClick={handleCancelEdit}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors font-medium"
                >
                  هەڵوەشاندنەوە
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-[var(--primary-600)] text-white rounded-md hover:bg-[var(--primary-700)] transition-colors font-medium"
                >
                  نوێکردنەوە
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}
