'use client';

import React from 'react';

interface LoadingOverlayProps {
  isLoading: boolean;
  message?: string;
  progress?: number;
}

export default function LoadingOverlay({ isLoading, message = 'بارکردن...', progress }: LoadingOverlayProps) {
  if (!isLoading) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg max-w-sm w-full text-center">
        <div className="flex flex-col items-center">
          {progress !== undefined ? (
            <div className="w-full mb-4">
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div 
                  className="bg-[var(--primary-600)] h-2.5 rounded-full transition-all duration-300 ease-in-out" 
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-600 mt-1">{progress}%</p>
            </div>
          ) : (
            <div className="w-12 h-12 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin mb-4"></div>
          )}
          <p className="text-gray-800 font-medium">{message}</p>
        </div>
      </div>
    </div>
  );
}
