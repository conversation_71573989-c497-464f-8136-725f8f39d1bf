const { chromium } = require('playwright');

(async () => {
  // Launch the browser
  const browser = await chromium.launch({
    headless: false, // Set to true for headless mode
    slowMo: 100 // Slow down operations by 100ms
  });

  // Create a new browser context
  const context = await browser.newContext();

  // Open a new page
  const page = await context.newPage();

  try {
    // Navigate to the new post page
    console.log('Navigating to the new post page...');
    await page.goto('http://localhost:3000/dashboard/posts/new');

    // Wait for the editor to load
    console.log('Waiting for the editor to load...');
    await page.waitForSelector('.lexical-editor-container', { timeout: 10000 });

    // Click on the image upload button
    console.log('Clicking on the image upload button...');
    const uploadButton = await page.locator('button[aria-label="Insert Image"]');
    await uploadButton.click();

    // Wait for the file input to be available
    console.log('Setting up file input...');

    // Create a file input for uploading
    const fileChooserPromise = page.waitForEvent('filechooser');
    await uploadButton.click();
    const fileChooser = await fileChooserPromise;

    // Upload a test image
    console.log('Uploading test image...');
    await fileChooser.setFiles('./test-image.jpg');

    // Wait for the upload to complete
    console.log('Waiting for upload to complete...');
    await page.waitForSelector('.editor-image', { timeout: 30000 });

    // Check if the image is visible
    console.log('Checking if image is visible...');
    const imageVisible = await page.isVisible('.editor-image');
    console.log(`Image visible: ${imageVisible}`);

    // Take a screenshot
    console.log('Taking a screenshot...');
    await page.screenshot({ path: 'editor-with-image.png', fullPage: true });

    // Get console logs
    console.log('Console logs from the page:');
    page.on('console', msg => {
      console.log(`PAGE LOG: ${msg.text()}`);
    });

    // Wait a bit to see the result
    await page.waitForTimeout(5000);

    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Test failed:', error);
    await page.screenshot({ path: 'error-screenshot.png', fullPage: true });
  } finally {
    // Close the browser
    await browser.close();
  }
})();
