'use client';

import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { collection, query, where, getDocs, limit, orderBy, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import styles from './HeroSection.module.css';

interface Post {
  id: string;
  title: string;
  content: string;
  thumbnailImage: string;
  featuredImage: string;
  category?: string;
  date: Timestamp;
  viewCount: number;
  status: string;
  isPrimary?: boolean;
}

export default function HeroSection() {
  const [primaryPosts, setPrimaryPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  useEffect(() => {
    async function fetchPrimaryPosts() {
      try {
        setIsLoading(true);
        const postsCollection = collection(db, 'posts');
        const categoriesCollection = collection(db, 'categories');

        // Fetch all categories to map slugs to names
        const categoriesSnapshot = await getDocs(categoriesCollection);
        const categoriesMap = new Map();
        categoriesSnapshot.docs.forEach(doc => {
          const data = doc.data();
          categoriesMap.set(data.slug, data.name);
        });

        // Use a simpler query that doesn't require a composite index
        const fallbackQuery = query(
          postsCollection,
          orderBy('date', 'desc'),
          limit(20) // Get more to filter
        );

        const fallbackSnapshot = await getDocs(fallbackQuery);
        const fallbackList = fallbackSnapshot.docs
          .map(doc => {
            const data = doc.data();
            // Replace category slug with category name if available
            if (data.category && categoriesMap.has(data.category)) {
              data.category = categoriesMap.get(data.category);
            }
            return {
              id: doc.id,
              ...data
            } as Post;
          })
          .filter(post =>
            post.status === 'بڵاوکراوەتەوە' &&
            post.isPrimary === true
          )
          .slice(0, 4); // Limit to maximum 4 posts

        setPrimaryPosts(fallbackList);
      } catch (err) {
        console.error('Error fetching primary posts:', err);
      } finally {
        setIsLoading(false);
      }
    }

    fetchPrimaryPosts();
  }, []);

  // Auto-advance carousel
  useEffect(() => {
    if (!isAutoPlaying || primaryPosts.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % primaryPosts.length);
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(interval);
  }, [primaryPosts.length, isAutoPlaying]);

  const goToSlide = useCallback((index: number) => {
    setCurrentSlide(index);
    // Pause auto-play briefly when manually changing slides
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000); // Resume after 10 seconds
  }, []);

  const nextSlide = useCallback(() => {
    if (primaryPosts.length <= 1) return;
    setCurrentSlide((prev) => (prev + 1) % primaryPosts.length);
    // Pause auto-play briefly when manually changing slides
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000); // Resume after 10 seconds
  }, [primaryPosts.length]);

  const prevSlide = useCallback(() => {
    if (primaryPosts.length <= 1) return;
    setCurrentSlide((prev) => (prev - 1 + primaryPosts.length) % primaryPosts.length);
    // Pause auto-play briefly when manually changing slides
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000); // Resume after 10 seconds
  }, [primaryPosts.length]);

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex justify-center items-center h-96">
          <div className="inline-block w-8 h-8 border-4 border-[var(--primary-500)] border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  if (primaryPosts.length === 0) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex justify-center items-center h-96">
          <p className="text-xl text-gray-500">هیچ بابەتێکی سەرەکی نەدۆزرایەوە</p>
        </div>
      </div>
    );
  }

  return (
    <section
      className={`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 ${styles.heroSection}`}
    >
      {/* Main Carousel */}
      <div className="relative h-[500px] rounded-xl overflow-hidden shadow-lg mb-8">
        {/* Carousel slides */}
        <div className="h-full relative">
          {primaryPosts.map((post, index) => (
            <div
              key={post.id}
              className={`absolute inset-0 transition-opacity duration-1000 ${
                index === currentSlide ? 'opacity-100 z-10' : 'opacity-0 z-0'
              }`}
            >
              <Link href={`/post/${post.id}`} className="block h-full">
                <div className="absolute inset-0">
                  <Image
                    src={post.featuredImage || post.thumbnailImage || '/placeholder.jpg'}
                    alt={post.title}
                    fill
                    sizes="100vw"
                    priority={index === currentSlide}
                    loading={index === currentSlide ? "eager" : "lazy"}
                    className="object-contain"
                    quality={index === currentSlide ? 85 : 60}
                    unoptimized={true} // Skip Next.js optimization for all images
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-[#3d2314]/15 via-[#5c3a24]/8 to-transparent"></div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 p-4 pb-2 z-20">
                  <div className="bg-white/70 backdrop-blur-sm rounded-lg p-6 max-w-3xl">
                    <div className="mb-3">
                      <span className="inline-block bg-[var(--primary-600)] text-white px-3 py-1 text-sm font-semibold rounded-md">
                        {post.category || 'گشتی'}
                      </span>
                    </div>
                    <h2 className="text-2xl md:text-3xl font-bold text-black mb-3">{post.title}</h2>
                    <div className="flex items-center text-gray-700 text-sm">
                      <span className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        {post.date ? new Date(post.date.toDate()).toLocaleDateString('ku-IQ') : ''}
                      </span>
                      <span className="mx-2">•</span>
                      <span className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        {post.viewCount || 0} بینین
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>
      </div>

      {/* Thumbnail Grid */}
      {primaryPosts.length > 1 && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {primaryPosts.map((post, index) => (
            <div
              key={post.id}
              className={`relative h-40 rounded-xl overflow-hidden shadow-md cursor-pointer transition-transform hover:scale-[1.02] ${
                index === currentSlide ? 'ring-2 ring-[var(--primary-600)]' : ''
              }`}
              onClick={() => goToSlide(index)}
            >
              <div className="absolute inset-0">
                <Image
                  src={post.thumbnailImage || post.featuredImage || '/placeholder.jpg'}
                  alt={post.title}
                  fill
                  sizes="(max-width: 768px) 50vw, 25vw"
                  className="object-cover"
                  loading="lazy"
                  quality={60}
                  unoptimized={true} // Skip Next.js optimization for all images
                />
                <div className="absolute inset-0 bg-gradient-to-t from-[#3d2314]/15 to-transparent"></div>
              </div>
              <div className="absolute bottom-0 left-0 right-0 p-3">
                <div className="bg-white/70 backdrop-blur-sm rounded p-2">
                  <h3 className="text-xs font-bold text-black line-clamp-2">{post.title}</h3>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </section>
  );
}
