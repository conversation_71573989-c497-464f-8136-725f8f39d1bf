rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // TEMPORARY: Allow all access for testing
    match /{document=**} {
      allow read, write: if true;
    }

    // COMMENTED OUT FOR TESTING - UNCOMMENT LATER
    // // Allow authenticated users to read and write their own data
    // match /users/{userId} {
    //   allow read, write: if request.auth != null && request.auth.uid == userId;
    // }

    // // Allow authenticated users to read and write posts
    // match /posts/{postId} {
    //   allow read: if true;
    //   allow write: if request.auth != null;
    // }

    // // Allow authenticated users to read and write categories
    // match /categories/{categoryId} {
    //   allow read: if true;
    //   allow write: if request.auth != null;
    // }

    // // Allow authenticated users to read and write tags
    // match /tags/{tagId} {
    //   allow read: if true;
    //   allow write: if request.auth != null;
    // }
  }
}
