import { ref, uploadString, getDownloadURL, deleteObject } from 'firebase/storage';
import { storage } from './config';
import { optimizeImage } from '../utils/imageOptimizer';

/**
 * Upload a base64 image to Firebase Storage with optimization
 * @param base64Image - Base64 encoded image string
 * @param path - Storage path (e.g., 'posts/featured')
 * @param filename - Optional filename, defaults to timestamp
 * @param maxWidth - Maximum width for optimization
 * @param maxHeight - Maximum height for optimization
 * @param quality - JPEG quality (0-1)
 * @returns Promise with download URL
 */
export async function uploadImageToStorage(
  base64Image: string,
  path: string,
  filename?: string,
  maxWidth?: number,
  maxHeight?: number,
  quality?: number
): Promise<string> {
  try {
    // Optimize the image if dimensions are provided
    let optimizedImage = base64Image;
    if (maxWidth && maxHeight) {
      try {
        optimizedImage = await optimizeImage(base64Image, maxWidth, maxHeight, quality);
        console.log('Image optimized successfully');
      } catch (optimizeError) {
        console.error('Error optimizing image:', optimizeError);
        // Continue with original image if optimization fails
      }
    }

    // Extract content type and base64 data
    const matches = optimizedImage.match(/^data:([A-Za-z-+/]+);base64,(.+)$/);

    if (!matches || matches.length !== 3) {
      throw new Error('Invalid base64 image format');
    }

    const contentType = matches[1];
    const base64Content = matches[2];

    // Determine file extension based on content type
    let extension = 'jpg';
    if (contentType.includes('png')) {
      extension = 'png';
    } else if (contentType.includes('gif')) {
      extension = 'gif';
    } else if (contentType.includes('webp')) {
      extension = 'webp';
    }

    // Generate a unique filename if not provided
    const uniqueFilename = filename || `${Date.now()}.${extension}`;

    // Create a reference to the storage location
    const storageRef = ref(storage, `${path}/${uniqueFilename}`);

    try {
      // Set metadata with the correct content type
      const metadata = {
        contentType: contentType,
        cacheControl: 'public, max-age=31536000' // Cache for 1 year
      };

      // Try to upload to Firebase Storage with metadata
      const snapshot = await uploadString(storageRef, base64Content, 'base64', metadata);
      const downloadURL = await getDownloadURL(snapshot.ref);
      console.log('Image uploaded successfully to Firebase Storage:', downloadURL);
      return downloadURL;
    } catch (storageError) {
      console.error('Firebase Storage upload failed:', storageError);

      // Check for specific errors
      if (storageError instanceof Error) {
        if (storageError.message.includes('unauthorized')) {
          console.warn('Storage permission denied. Using base64 fallback. Please update Firebase Storage rules.');
          alert('تکایە ڕێساکانی Firebase Storage نوێ بکەرەوە بۆ ڕێگەدان بە بارکردنی وێنەکان');
        } else if (storageError.message.includes('CORS')) {
          console.warn('CORS error detected. Using base64 fallback. Please configure CORS for Firebase Storage.');
        }
      }

      // Fallback to base64 storage in Firestore
      console.warn('Falling back to storing image as base64 in Firestore');
      return optimizedImage; // Return the optimized image even in fallback case
    }
  } catch (error) {
    console.error('Error processing image:', error);

    // Return the base64 image as fallback
    console.warn('Error occurred, falling back to base64 storage');
    return base64Image;
  }
}

/**
 * Generate a unique filename for an image
 * @param originalFilename - Original filename (optional)
 * @returns Unique filename
 */
export function generateUniqueFilename(originalFilename?: string): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 8);

  if (originalFilename) {
    // Extract file extension
    const extension = originalFilename.split('.').pop() || 'jpg';
    return `${timestamp}-${randomString}.${extension}`;
  }

  return `${timestamp}-${randomString}.jpg`;
}

/**
 * Upload a thumbnail image with optimization
 * @param base64Image - Base64 encoded image string
 * @param filename - Optional filename
 * @returns Promise with download URL
 */
export async function uploadThumbnailImage(
  base64Image: string,
  filename?: string
): Promise<string> {
  // Thumbnails are optimized to 600x600 with 70% quality
  return uploadImageToStorage(
    base64Image,
    'posts/thumbnails',
    filename,
    600,
    600,
    0.7
  );
}

/**
 * Upload a featured image with optimization
 * @param base64Image - Base64 encoded image string
 * @param filename - Optional filename
 * @returns Promise with download URL
 */
export async function uploadFeaturedImage(
  base64Image: string,
  filename?: string
): Promise<string> {
  // Featured images are optimized to 1600x1600 with 85% quality
  return uploadImageToStorage(
    base64Image,
    'posts/featured',
    filename,
    1600,
    1600,
    0.85
  );
}

/**
 * Delete a file from Firebase Storage
 * @param fileUrl - The full URL of the file to delete
 * @returns Promise<boolean> - True if deletion was successful, false otherwise
 */
export async function deleteFileFromStorage(fileUrl: string): Promise<boolean> {
  try {
    // Check if the URL is a Firebase Storage URL
    if (!fileUrl || !fileUrl.includes('firebasestorage.googleapis.com')) {
      console.warn('Not a Firebase Storage URL:', fileUrl);
      return false;
    }

    // Extract the path from the URL
    // The URL format is typically: https://firebasestorage.googleapis.com/v0/b/[bucket]/o/[path]?alt=media&token=[token]
    const urlObj = new URL(fileUrl);
    const path = decodeURIComponent(urlObj.pathname.split('/o/')[1]?.split('?')[0]);

    if (!path) {
      console.warn('Could not extract path from URL:', fileUrl);
      return false;
    }

    // Create a reference to the file
    const fileRef = ref(storage, path);

    // Delete the file
    await deleteObject(fileRef);
    console.log('File deleted successfully:', path);
    return true;
  } catch (error) {
    console.error('Error deleting file:', error);
    return false;
  }
}
